import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Box, Flex, Text } from "@chakra-ui/react";

import Customize from "../../components/common/Customize";
import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import PreviewSlider from "../../components/common/PreviewSlider";

import { dotGrid } from "../../constants/code/Backgrounds/dotGridCode";
import DotGrid from "../../content/Backgrounds/DotGrid/DotGrid";

const DotGridDemo = () => {
  const [dotSize, setDotSize] = useState(5);
  const [gap, setGap] = useState(15);
  const [baseColor, setBaseColor] = useState("#ffffff");
  const [activeColor, setActiveColor] = useState("#00d8ff");
  const [proximity, setProximity] = useState(120);
  const [shockRadius, setShockRadius] = useState(250);
  const [shockStrength, setShockStrength] = useState(5);
  const [resistance, setResistance] = useState(750);
  const [returnDuration, setReturnDuration] = useState(1.5);

  const propData = [
    {
      name: "dotSize",
      type: "number",
      default: "16",
      description: "Size of each dot in pixels.",
    },
    {
      name: "gap",
      type: "number",
      default: "32",
      description: "Gap between each dot in pixels.",
    },
    {
      name: "baseColor",
      type: "string",
      default: "'#00d8ff'",
      description: "Base color of the dots.",
    },
    {
      name: "activeColor",
      type: "string",
      default: "'#00d8ff'",
      description: "Color of dots when hovered or activated.",
    },
    {
      name: "proximity",
      type: "number",
      default: "150",
      description: "Radius around the mouse pointer within which dots react.",
    },
    {
      name: "speedTrigger",
      type: "number",
      default: "100",
      description: "Mouse speed threshold to trigger inertia effect.",
    },
    {
      name: "shockRadius",
      type: "number",
      default: "250",
      description: "Radius of the shockwave effect on click.",
    },
    {
      name: "shockStrength",
      type: "number",
      default: "5",
      description: "Strength of the shockwave effect on click.",
    },
    {
      name: "maxSpeed",
      type: "number",
      default: "5000",
      description: "Maximum speed for inertia calculation.",
    },
    {
      name: "resistance",
      type: "number",
      default: "750",
      description: "Resistance for the inertia effect.",
    },
    {
      name: "returnDuration",
      type: "number",
      default: "1.5",
      description:
        "Duration for dots to return to their original position after inertia.",
    },
    {
      name: "className",
      type: "string",
      default: "''",
      description: "Additional CSS classes for the component.",
    },
    {
      name: "style",
      type: "React.CSSProperties",
      default: "{}",
      description: "Inline styles for the component.",
    },
  ];

  return (
    <TabbedLayout data-oid="xnkaex.">
      <PreviewTab data-oid="clowhvu">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          overflow="hidden"
          data-oid="n0e26u2"
        >
          <DotGrid
            dotSize={dotSize}
            gap={gap}
            baseColor={baseColor}
            activeColor={activeColor}
            proximity={proximity}
            shockRadius={shockRadius}
            shockStrength={shockStrength}
            resistance={resistance}
            returnDuration={returnDuration}
            data-oid="4hq7f7."
          />
        </Box>

        <Customize data-oid="hiv7-n-">
          <Flex alignItems="center" mb={4} data-oid="s.2madx">
            <Text fontSize="sm" mr={2} data-oid="307c2ao">
              Base Color
            </Text>
            <input
              type="color"
              value={baseColor}
              style={{ height: "22px", outline: "none", border: "none" }}
              onChange={(e) => setBaseColor(e.target.value)}
              data-oid="2f1cuxw"
            />
          </Flex>
          <Flex alignItems="center" mb={4} data-oid="jv:6u2u">
            <Text fontSize="sm" mr={2} data-oid=":c6f_m3">
              Active Color
            </Text>
            <input
              type="color"
              value={activeColor}
              style={{ height: "22px", outline: "none", border: "none" }}
              onChange={(e) => setActiveColor(e.target.value)}
              data-oid="0s1lrxv"
            />
          </Flex>
          <PreviewSlider
            title="Dot Size"
            min={2}
            max={50}
            step={1}
            value={dotSize}
            onChange={setDotSize}
            data-oid="gwx5spp"
          />

          <PreviewSlider
            title="Gap"
            min={5}
            max={100}
            step={1}
            value={gap}
            onChange={setGap}
            data-oid="2xa7t.-"
          />

          <PreviewSlider
            title="Proximity"
            min={50}
            max={500}
            step={10}
            value={proximity}
            onChange={setProximity}
            data-oid="ah:zdt4"
          />

          <PreviewSlider
            title="Shock Radius"
            min={50}
            max={500}
            step={10}
            value={shockRadius}
            onChange={setShockRadius}
            data-oid="rh7wbx_"
          />

          <PreviewSlider
            title="Shock Strength"
            min={1}
            max={20}
            step={1}
            value={shockStrength}
            onChange={setShockStrength}
            data-oid="2fz8i8:"
          />

          <PreviewSlider
            title="Resistance (Inertia)"
            min={100}
            max={2000}
            step={50}
            value={resistance}
            onChange={setResistance}
            data-oid="z-5._wn"
          />

          <PreviewSlider
            title="Return Duration (Inertia)"
            min={0.1}
            max={5}
            step={0.1}
            value={returnDuration}
            onChange={setReturnDuration}
            data-oid="bn2-kiz"
          />
        </Customize>

        <PropTable data={propData} data-oid="0:fs9xs" />
        <Dependencies dependencyList={["gsap"]} data-oid="8k1ps3w" />
      </PreviewTab>

      <CodeTab data-oid="qq:3k5t">
        <CodeExample codeObject={dotGrid} data-oid="gqfzn.g" />
      </CodeTab>

      <CliTab data-oid="r61jrb.">
        <CliInstallation {...dotGrid} data-oid="o58a_gt" />
      </CliTab>
    </TabbedLayout>
  );
};

export default DotGridDemo;
