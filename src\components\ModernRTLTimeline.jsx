import React from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../contexts/LanguageContext";
import { experiences } from "../constants";

// Modern RTL Timeline component with a different style
const ModernRTLTimeline = () => {
  const { t } = useTranslation();
  const { dir, language } = useLanguage();

  // Function to determine which service this is to get the right translations
  const getServiceKey = (title) => {
    return title.includes("Executive")
      ? "executive"
      : title.includes("Airport")
        ? "airport"
        : "group";
  };

  return (
    <div className="modern-rtl-timeline" data-oid="d8:nym_">
      {experiences.map((experience, index) => {
        const serviceKey = getServiceKey(experience.title);

        return (
          <div
            key={`modern-timeline-item-${index}`}
            className="modern-timeline-item"
            data-oid="g1hdoo8"
          >
            {/* Timeline connector */}
            <div className="modern-timeline-connector" data-oid="w6hjo99">
              {/* Timeline icon */}
              <motion.div
                className="modern-timeline-icon"
                style={{ background: experience.iconBg }}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{
                  type: "spring",
                  stiffness: 260,
                  damping: 20,
                  delay: index * 0.1 + 0.2,
                }}
                data-oid="aazlzod"
              >
                <motion.div
                  className="flex justify-center items-center w-full h-full"
                  whileHover={{
                    scale: 1.1,
                    rotate: 5,
                    transition: { duration: 0.2 },
                  }}
                  data-oid="47duhu:"
                >
                  <img
                    src={experience.icon}
                    alt={t(`service-details.${serviceKey}.company`)}
                    className="w-[60%] h-[60%] object-contain"
                    data-oid="m2y_x2g"
                  />
                </motion.div>
              </motion.div>
            </div>

            {/* Timeline content */}
            <motion.div
              className="modern-timeline-content"
              initial={{ opacity: 0, x: dir === "rtl" ? -50 : 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{
                type: "spring",
                stiffness: 100,
                damping: 12,
                delay: index * 0.15 + 0.3,
              }}
              whileHover={{
                scale: 1.02,
                transition: { duration: 0.2 },
              }}
              data-oid="qv5xxnq"
            >
              <div className="modern-timeline-header" data-oid="-jp_22h">
                <h3
                  className="text-[#D4AF37] text-[24px] font-bold"
                  data-oid="qy14j8c"
                >
                  {t(`service-details.${serviceKey}.title`)}
                </h3>
                <p
                  className="text-white text-[16px] font-semibold"
                  style={{ margin: 0 }}
                  data-oid="fc_lm1o"
                >
                  {t(`service-details.${serviceKey}.company`)}
                </p>
                <div className="modern-timeline-date" data-oid="0-l.lz0">
                  <span className="text-gold font-bold" data-oid="tk2xp3:">
                    {t(`service-details.${serviceKey}.date`)}
                  </span>
                </div>
              </div>

              <ul className="modern-timeline-points" data-oid="0sdam6m">
                {t(`service-details.${serviceKey}.points`, {
                  returnObjects: true,
                }).map((point, pointIndex) => (
                  <motion.li
                    key={`modern-timeline-point-${index}-${pointIndex}`}
                    className="modern-timeline-point"
                    initial={{ opacity: 0.8 }}
                    whileHover={{
                      opacity: 1,
                      x: -5,
                      color: "#D4AF37",
                      transition: { duration: 0.2 },
                    }}
                    data-oid="o2dwt6k"
                  >
                    {point}
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          </div>
        );
      })}
    </div>
  );
};

export default ModernRTLTimeline;
