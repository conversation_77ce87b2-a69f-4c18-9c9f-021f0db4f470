<!doctype html>
<html lang="en" style="background: #060606;">

<head>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-1GF5NXTV27"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'G-1GF5NXTV27');
  </script>

  <!-- Basic Meta Tags -->
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="theme-color" content="#060606" />
  <meta name="author" content="<PERSON>">
  <meta name="description"
    content="An open source collection of high quality, animated, interactive & fully customizable React components for building stunning, memorable user interfaces." />
  <meta name="keywords"
    content="React, React tutorials, React tips, React best practices, React development, React guides, React articles, React ecosystem, JavaScript, frontend development, ReactJS, UI Library, Component Library, React Components, React Animations" />

  <!-- Font -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Gochi+Hand&display=swap" rel="stylesheet">

  <!-- Icons -->
  <link rel="icon" type="image/svg+xml" sizes="16x16 32x32" href="favicon.ico" />
  <link rel="icon" type="image/png" href="favicon-96x96.png" sizes="96x96" />
  <link rel="shortcut icon" href="favicon.ico" />
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
  <meta name="apple-mobile-web-app-title" content="React Bits" />
  <link rel="manifest" href="/site.webmanifest" />

  <!-- Open Graph (OG) - Facebook, LinkedIn, etc. -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="React Bits">
  <meta property="og:description" content="An open source collection of high quality, animated, interactive & fully customizable React components for building stunning, memorable user interfaces.">
  <meta property="og:image" content="https://reactbits.dev/og-pic.png">
  <meta property="og:image:alt" content="The React Bits landing page, showcasing 4 awesome components!">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:url" content="https://reactbits.dev">
  <meta property="og:site_name" content="React Bits">
  <meta property="og:locale" content="en_US">

  <!-- Twitter Card - Twitter Sharing -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="React Bits">
  <meta name="twitter:description" content="An open source collection of high quality, animated, interactive & fully customizable React components for building stunning, memorable user interfaces.">
  <meta name="twitter:image" content="https://reactbits.dev/og-pic.png">
  <meta name="twitter:image:alt" content="The React Bits landing page, showcasing 4 awesome components!">

  <!-- Favicon & Apple Touch Icons -->
  <link rel="icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="manifest" href="/site.webmanifest">

  <!-- Canonical & Robots Meta Tags -->
  <link rel="canonical" href="https://reactbits.dev">
  <meta name="robots" content="index, follow">

  <!-- Structured Data (JSON-LD for SEO) -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "React Bits",
    "url": "https://reactbits.dev",
    "description": "An open source collection of high quality, animated, interactive & fully customizable React components for building stunning, memorable user interfaces.",
    "image": "https://reactbits.dev/og-pic.png",
    "author": {
      "@type": "Person",
      "name": "David Haz"
    },
    "publisher": {
      "@type": "Person",
      "name": "David Haz",
      "logo": {
        "@type": "ImageObject",
        "url": "https://davidhaz.com"
      }
    }
  }
  </script>
  <title>React Bits - Animated UI Components For React</title>
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.jsx"></script>
</body>

</html>