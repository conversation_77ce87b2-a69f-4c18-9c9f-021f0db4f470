import { useEffect, useState } from "react";
import { Box, Flex, Image, Text } from "@chakra-ui/react";
import { AiOutlinePlusCircle } from "react-icons/ai";
import { Helmet } from "react-helmet-async";

import Confetti from "react-confetti";
import Header from "../components/landing/LandingHeader/LandingHeader";
import FadeContent from "../content/Animations/FadeContent/FadeContent";
import logo from "../assets/logos/reactbits-logo.svg";

import "../css/showcase.css";

const ShowcasePage = () => {
  const [isLoaded, setIsLoaded] = useState(false);

  const showcaseItems = [
    {
      name: "<PERSON><PERSON>",
      url: "https://devrajchatribin.com/about",
      using: "<CountUp />",
    },
    {
      name: "<PERSON>",
      url: "https://resume-tex.vercel.app",
      using: "<Squares />",
    },
    {
      name: "<PERSON>",
      url: "https://oscarhernandez.vercel.app",
      using: "<LetterGlitch />",
    },
    {
      name: "<PERSON><PERSON><PERSON>za<PERSON>",
      url: "https://www.evolvion.io/",
      using: "<SpotlightCard />",
    },
  ];

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });

    setTimeout(() => {
      setIsLoaded(true);
    }, 1000);
  }, []);

  return (
    <section className="showcase-wrapper" data-oid="t57qm9p">
      <Helmet data-oid=".pknie2">
        <title data-oid="zh8nd_:">React Bits - Showcase 🎉</title>
      </Helmet>
      <Header data-oid="1f54.en" />
      {isLoaded && (
        <Confetti
          run={window.innerWidth > 1000}
          recycle={false}
          colors={["#00d8ff"]}
          gravity={0.5}
          frameRate={60}
          numberOfPieces={100}
          data-oid="lj5pxss"
        />
      )}

      <Flex data-oid="5lflt__">
        <FadeContent blur duration={1000} data-oid="72omxc1">
          <Text className="title" data-oid="ejdrwa:">
            Built with
          </Text>
        </FadeContent>

        <FadeContent blur duration={1000} data-oid="chp-2n8">
          <Image className="title-logo" src={logo} data-oid="r85_6x3" />
        </FadeContent>
      </Flex>
      <FadeContent blur duration={1000} data-oid="cgfq5dk">
        <Text className="sub-text" data-oid="-3dtvqo">
          Discover how other developers are using React Bits to build awesome
          user experiences
        </Text>
      </FadeContent>

      <FadeContent
        blur
        duration={1000}
        className="fade-grid"
        data-oid=":v6j_h7"
      >
        <div className="grid-container" data-oid="i7pdhe9">
          <Box
            as="a"
            href="https://docs.google.com/forms/d/e/1FAIpQLSdlzugJovfr5HPon3YAi8YYSSRuackqX8XIXSeeQmSQypNc7w/viewform?usp=dialog"
            target="_blank"
            rel="noreferrer"
            className="grid-item add-yours"
            data-oid="4i2y9wl"
          >
            <AiOutlinePlusCircle className="add-icon" data-oid="aidyizw" />
            <Text data-oid="mf-keef">Submit New Project</Text>
          </Box>

          {showcaseItems.map((item, index) => (
            <Box
              as="a"
              href={item.url}
              rel="noreferrer"
              target="_blank"
              className="grid-item"
              key={item.url}
              data-oid="dd9sard"
            >
              <img
                className="showcase-img"
                src={`https://davidhaz.com/react-bits-showcase/showcase-${index + 1}.webp`}
                alt={`Showcase website submitted by: ${item.name ? item.name : "Anonymous"}`}
                data-oid="4:e4uhc"
              />

              <div className="showcase-info" data-oid="htq8p80">
                {item.name && (
                  <Text className="author" data-oid="ibr9t3w">
                    {item.name}
                  </Text>
                )}
                <Text className="using" data-oid="r5na0p3">
                  Using {item.using}
                </Text>
              </div>
            </Box>
          ))}

          <div className="grid-item" data-oid="x5aamj5"></div>
          <div className="grid-item" data-oid="7pyfopj"></div>
          <div className="grid-item" data-oid="8tktiqd"></div>
          <div className="grid-item" data-oid="hek4128"></div>
        </div>
      </FadeContent>
    </section>
  );
};

export default ShowcasePage;
