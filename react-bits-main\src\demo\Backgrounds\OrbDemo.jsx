import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Box, Text } from "@chakra-ui/react";
import { useDebounce } from "react-haiku";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import PreviewSlider from "../../components/common/PreviewSlider";
import PreviewSwitch from "../../components/common/PreviewSwitch";
import Customize from "../../components/common/Customize";

import Orb from "../../content/Backgrounds/Orb/Orb";
import { orb } from "../../constants/code/Backgrounds/orbCode";

const OrbDemo = () => {
  const [hue, setHue] = useState(0);
  const [hoverIntensity, setHoverIntensity] = useState(0.5);
  const [rotateOnHover, setRotateOnHover] = useState(true);
  const [forceHoverState, setForceHoverState] = useState(false);

  const debouncedHue = useDebounce(hue, 300);
  const debouncedHoverIntensity = useDebounce(hoverIntensity, 300);

  const propData = [
    {
      name: "hue",
      type: "number",
      default: "0",
      description: "The base hue for the orb (in degrees).",
    },
    {
      name: "hoverIntensity",
      type: "number",
      default: "0.2",
      description: "Controls the intensity of the hover distortion effect.",
    },
    {
      name: "rotateOnHover",
      type: "boolean",
      default: "true",
      description: "Toggle to enable or disable continuous rotation on hover.",
    },
    {
      name: "forceHoverState",
      type: "boolean",
      default: "false",
      description:
        "Force hover animations even when the orb is not actually hovered.",
    },
  ];

  return (
    <TabbedLayout data-oid="dh0ak1h">
      <PreviewTab data-oid=":4ycgtw">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          p={0}
          overflow="hidden"
          data-oid="05mj8xh"
        >
          <Orb
            hoverIntensity={debouncedHoverIntensity}
            rotateOnHover={rotateOnHover}
            hue={debouncedHue}
            forceHoverState={forceHoverState}
            data-oid="tr_ahcp"
          />

          <Text
            position="absolute"
            zIndex={0}
            fontSize="clamp(2rem, 2vw, 6rem)"
            fontWeight={900}
            mb={0}
            mixBlendMode="difference"
            data-oid="y-_k839"
          >
            Hover.
          </Text>
        </Box>

        <Customize data-oid="3vskidg">
          <PreviewSlider
            title="Hue Shift"
            min={0}
            max={360}
            step={1}
            value={hue}
            onChange={setHue}
            data-oid="m5pa_90"
          />

          <PreviewSlider
            title="Hover Intensity"
            min={0}
            max={5}
            step={0.01}
            value={hoverIntensity}
            onChange={setHoverIntensity}
            data-oid="j10au0i"
          />

          <PreviewSwitch
            title="Rotate On Hover"
            isChecked={rotateOnHover}
            onChange={(e) => setRotateOnHover(e.target.checked)}
            data-oid="pbkn2fj"
          />

          <PreviewSwitch
            title="Force Hover State"
            isChecked={forceHoverState}
            onChange={(e) => setForceHoverState(e.target.checked)}
            data-oid="89..ls."
          />
        </Customize>

        <PropTable data={propData} data-oid="py7q7xb" />
        <Dependencies dependencyList={["ogl"]} data-oid="ekf_bcr" />
      </PreviewTab>

      <CodeTab data-oid="480vgbl">
        <CodeExample codeObject={orb} data-oid="my8ogf:" />
      </CodeTab>

      <CliTab data-oid="ppxi-av">
        <CliInstallation {...orb} data-oid="6ahqk8j" />
      </CliTab>
    </TabbedLayout>
  );
};

export default OrbDemo;
