import { Flex, Switch, Text } from "@chakra-ui/react";

const PreviewSwitch = ({ title, isChecked, onChange, isDisabled }) => {
  return (
    <Flex gap={4} align="center" mt={4} data-oid="pw_u0c0">
      <Text fontSize="sm" data-oid="iyvigeh">
        {title}
      </Text>
      <Switch
        isDisabled={isDisabled}
        isChecked={isChecked}
        onChange={(e) => onChange(e)}
        data-oid="fb23652"
      />
    </Flex>
  );
};

export default PreviewSwitch;
