import { Flex } from "@chakra-ui/react";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";

import CodeExample from "../../components/code/CodeExample";
import PropTable from "../../components/common/PropTable";
import CliInstallation from "../../components/code/CliInstallation";

import MagnetLines from "../../content/Animations/MagnetLines/MagnetLines";
import { magnetLines } from "../../constants/code/Animations/magnetLinesCode";

const MagnetLinesDemo = () => {
  const propData = [
    {
      name: "rows",
      type: "number",
      default: "9",
      description: "Number of grid rows.",
    },
    {
      name: "columns",
      type: "number",
      default: "9",
      description: "Number of grid columns.",
    },
    {
      name: "containerSize",
      type: "string",
      default: "80vmin",
      description:
        "Specifies the width and height of the entire grid container.",
    },
    {
      name: "lineColor",
      type: "string",
      default: "#efefef",
      description: "Color for each line (the <span> elements).",
    },
    {
      name: "lineWidth",
      type: "string",
      default: "1vmin",
      description: "Specifies each line’s thickness.",
    },
    {
      name: "lineHeight",
      type: "string",
      default: "6vmin",
      description: "Specifies each line’s length.",
    },
    {
      name: "baseAngle",
      type: "number",
      default: "-10",
      description:
        "Initial rotation angle (in degrees) before pointer movement.",
    },
    {
      name: "className",
      type: "string",
      default: "",
      description: "Additional class name(s) applied to the container.",
    },
    {
      name: "style",
      type: "object",
      default: "{}",
      description: "Inline styles for the container.",
    },
  ];

  return (
    <TabbedLayout data-oid="0xr2nkq">
      <PreviewTab data-oid="qoxk::p">
        <Flex
          overflow="hidden"
          justifyContent="center"
          pb={"1em"}
          alignItems="center"
          className="demo-container"
          data-oid="f5fqycn"
        >
          <MagnetLines
            rows={10}
            columns={12}
            containerSize="40vmin"
            lineWidth="2px"
            lineHeight="30px"
            data-oid="snot3vv"
          />
        </Flex>

        <PropTable data={propData} data-oid="nqzkklw" />
      </PreviewTab>

      <CodeTab data-oid="ng9qon1">
        <CodeExample codeObject={magnetLines} data-oid="vvzqg:u" />
      </CodeTab>

      <CliTab data-oid="32xpoh-">
        <CliInstallation {...magnetLines} data-oid="xcizst8" />
      </CliTab>
    </TabbedLayout>
  );
};

export default MagnetLinesDemo;
