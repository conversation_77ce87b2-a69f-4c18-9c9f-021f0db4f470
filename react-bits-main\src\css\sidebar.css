.sidebar,
.sidebar-mobile {
  background-color: #060606 !important;
  min-width: 200px;
}

.sidebar {
  padding: 4.75rem 0 6em 0 !important;
}

.sidebar::-webkit-scrollbar {
  width: 10px;
}

.sidebar::-webkit-scrollbar-track {
  display: none;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #060606;
  border-radius: 50px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: #222;
}

.sidebar-logo {
  padding-bottom: 1.2em !important;
  margin-top: 1em;
  border-bottom: none;
  width: 100%;
}

.sidebar-logo img {
  position: relative;
  left: -3px;
  width: 120px;
}

.sidebar-item {
  position: relative;
  font-size: 0.9rem;
  color: #a1a1aa;
  padding: 0.25em 0;
  display: flex;
  align-items: center;
  transition: color 0.3s ease;
}

.sidebar-item:hover {
  color: #fff;
}

.sidebar-item::before {
  content: "";
  width: 1px;
  height: 16px;
  background-color: #fff;
  position: absolute;
  left: -17px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sidebar-item .new-tag,
.sidebar-item .updated-tag {
  margin-left: 0.6em;
  font-size: 10px;
  border-radius: 6px;
  font-weight: 500;
  padding: 0.2em 0.4em;
  opacity: 1;
  text-decoration: none !important;
}

.sidebar-item .new-tag {
  color: #00d9ff;
  border: 1px solid #00d9ff74;
  background-color: #00d9ff2c;
}

.sidebar-item .updated-tag {
  color: #ff9346;
  border: 1px solid #ff934677;
  background-color: #ff934626;
}

.active-sidebar-item {
  width: fit-content;
  padding: 0.25em 0.25em 0.25em 0;
  color: #fff;
  position: relative;
  transition: color 0.3s ease;
}

.active-sidebar-item:hover {
  text-decoration: none;
}

.active-sidebar-item::before {
  opacity: 1;
  transition: opacity 0.3s ease;
}

.github-button {
  border: 1px solid transparent;
  transition: 0.3s ease;
}

.github-button img {
  transition: filter 0.3s ease;
}

.github-button:hover {
  border-color: #ffffff1c;
  background-color: #060606 !important;
  color: #fff;
}

.github-button:hover img {
  filter: invert(100%);
}