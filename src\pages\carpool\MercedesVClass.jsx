import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { styles } from "../../styles";
import { fadeIn, textVariant } from "../../utils/motion";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../../contexts/LanguageContext";
import RollingGallery from "../../components/ReactBits/RollingGallery";
import ModelViewer from "../../components/ReactBits/ModelViewer";

import "../../styles/carpool.css";



const MercedesVClass = () => {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const [isMobile, setIsMobile] = useState(false);
  const [activeImage, setActiveImage] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Using images from the V_Class folder
  const images = [
    "/src/assets/V_Class/V1.jpg",
    "/src/assets/V_Class/V2.jpg",
    "/src/assets/V_Class/V3.jpg",
    "/src/assets/V_Class/V4.jpg",
  ];

  // Fallback image in case the original images don't load
  const fallbackImage =
    "https://images.unsplash.com/photo-1551446591-142875a901a1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80";

  // Toggle fullscreen view
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  useEffect(() => {
    // Add a listener for changes to the screen size
    const mediaQuery = window.matchMedia("(max-width: 500px)");

    // Set the initial value of the `isMobile` state variable
    setIsMobile(mediaQuery.matches);

    // Define a callback function to handle changes to the media query
    const handleMediaQueryChange = (event) => {
      setIsMobile(event.matches);
    };

    // Add the callback function as a listener for changes to the media query
    mediaQuery.addEventListener("change", handleMediaQueryChange);

    // Remove the listener when the component is unmounted
    return () => {
      mediaQuery.removeEventListener("change", handleMediaQueryChange);
    };
  }, []);

  return (
    <div className="carpool-container black-gold-bg" data-oid="6vly_6j">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid="9lbdon."
      >
        <motion.div
          variants={fadeIn("", "", 0.1, 1)}
          className="carpool-frame"
          data-oid="rcpj_:5"
        >
          <motion.div
            variants={textVariant()}
            className="carpool-header"
            data-oid="57y.m0a"
          >
            <p className="carpool-subtitle" data-oid="ze3dt18">
              {language === "ar"
                ? t("common.premiumVehicle")
                : "Premium Vehicle"}
            </p>
            <h2 className="carpool-title" data-oid="wqsoqli">
              {language === "ar"
                ? t("vehicles.mercedes-vclass.title")
                : "Mercedes V-Class"}
            </h2>
            <p className="carpool-description" data-oid="7lj4gv2">
              {language === "ar"
                ? t("vehicles.mercedes-vclass.description")
                : "Spacious luxury MPV perfect for group travel with exceptional comfort."}
            </p>
          </motion.div>

          {/* 3D Model Viewer Section */}
          <div className="model-viewer-section" data-oid="m88l_t.">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="model-viewer-wrapper"
            >
              <ModelViewer
                url="/Mercedes_ V_Cclass/scene.gltf"
                width={400}
                height={450}
                defaultRotationX={-25}
                defaultRotationY={45}
                defaultZoom={2.2}
                minZoomDistance={1.2}
                maxZoomDistance={5}
                enableManualRotation={true}
                enableManualZoom={true}
                enableMouseParallax={true}
                enableHoverRotation={true}
                autoRotate={true}
                autoRotateSpeed={0.4}
                environmentPreset="studio"
                ambientIntensity={0.4}
                keyLightIntensity={1.2}
                fillLightIntensity={0.6}
                rimLightIntensity={0.8}
                fadeIn={true}
                showScreenshotButton={false}
              />
            </motion.div>
          </div>
        </motion.div>

        {/* Rolling Gallery Section */}
        <motion.div
          variants={fadeIn("", "", 0.2, 1)}
          className="rolling-gallery-section"
          data-oid="gallery-section"
        >
          <h3 className="section-title" data-oid="gallery-title">
            {language === "ar"
              ? t("vehicles.mercedes-vclass.gallery")
              : "Vehicle Gallery"}
          </h3>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="rolling-gallery-wrapper"
          >
            <RollingGallery
              images={images}
              autoplay={true}
              pauseOnHover={true}
            />
          </motion.div>
        </motion.div>

        {/* New Modern Gallery */}
        <motion.div
          variants={fadeIn("", "", 0.2, 1)}
          className="car-info-section"
          data-oid="fx4i7et"
        >
          <h3 className="section-title" data-oid="1efmaz0">
            {language === "ar"
              ? t("vehicles.mercedes-vclass.gallery")
              : "Gallery"}
          </h3>
          <div className="modern-gallery-container" data-oid="u-69xay">
            {/* Main large image */}
            <div
              className="modern-gallery-main"
              onClick={toggleFullscreen}
              data-oid="2ka_pfs"
            >
              <motion.img
                key={activeImage}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                src={images[activeImage]}
                alt={
                  language === "ar"
                    ? t("vehicles.mercedes-vclass.title")
                    : "Mercedes V-Class Featured"
                }
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = fallbackImage;
                }}
                data-oid="-y:h3a2"
              />
            </div>

            {/* Thumbnails row */}
            <div className="modern-gallery-thumbnails" data-oid="27e30vd">
              {images.map((image, index) => (
                <div
                  key={index}
                  className={`modern-gallery-thumb ${activeImage === index ? "active" : ""}`}
                  onClick={() => setActiveImage(index)}
                  data-oid="x7z.f5a"
                >
                  <img
                    src={image}
                    alt={
                      language === "ar"
                        ? `${t("vehicles.mercedes-vclass.title")} ${index + 1}`
                        : `Mercedes V-Class ${index + 1}`
                    }
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = fallbackImage;
                    }}
                    data-oid="-jjqrl0"
                  />
                </div>
              ))}
            </div>

            {/* Fullscreen Modal */}
            {isFullscreen && (
              <motion.div
                className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                data-oid="dpdyl4b"
              >
                <div className="relative w-full max-w-6xl" data-oid="nl9gfw4">
                  <button
                    className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full w-10 h-10 flex items-center justify-center border border-[#D4AF37] hover:bg-opacity-70 z-10"
                    onClick={toggleFullscreen}
                    data-oid="vaevirk"
                  >
                    ✕
                  </button>
                  <img
                    src={images[activeImage]}
                    alt={
                      language === "ar"
                        ? t("vehicles.mercedes-vclass.title")
                        : "Mercedes V-Class Fullscreen"
                    }
                    className="max-h-[80vh] w-auto mx-auto object-cover p-2"
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = fallbackImage;
                    }}
                    data-oid="qxcb1ko"
                  />

                  <div
                    className="flex justify-center mt-4 gap-2"
                    data-oid="nghwkq4"
                  >
                    {images.map((image, index) => (
                      <div
                        key={`fullscreen-thumb-${index}`}
                        className={`w-32 h-24 cursor-pointer rounded-md overflow-hidden border-2 ${activeImage === index ? "border-[#D4AF37]" : "border-gray-600"} flex items-center justify-center p-1`}
                        onClick={(e) => {
                          e.stopPropagation();
                          setActiveImage(index);
                        }}
                        data-oid="3j:xmya"
                      >
                        <img
                          src={image}
                          alt={
                            language === "ar"
                              ? `${t("vehicles.mercedes-vclass.title")} ${index + 1}`
                              : `Mercedes V-Class Thumbnail ${index + 1}`
                          }
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = fallbackImage;
                          }}
                          data-oid="mvqne6:"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>

        <div className="cta-container" data-oid="0r:_51i">
          <div className="car-info-section" data-oid="60pz.dz">
            <h3 className="section-title" data-oid="x--6l65">
              {language === "ar"
                ? t("vehicles.mercedes-vclass.premium-services")
                : "Premium Services & Group Travel"}
            </h3>
            <div className="specs-grid mb-6" data-oid="hf9pu13">
              <div className="specs-category" data-oid="ryb.u4o">
                <ul className="specs-list" data-oid="6rkmhpg">
                  {language === "ar" ? (
                    // Arabic services list (first half)
                    t("vehicles.mercedes-vclass.services-list", {
                      returnObjects: true,
                    })
                      .slice(0, 4)
                      .map((service, index) => (
                        <li
                          key={index}
                          className="specs-item"
                          data-oid="akgy7cj"
                        >
                          <span className="specs-item-icon" data-oid="7wwut0h">
                            •
                          </span>{" "}
                          {service}
                        </li>
                      ))
                  ) : (
                    // English services list (first half)
                    <>
                      <li className="specs-item" data-oid="3u46nx9">
                        <span className="specs-item-icon" data-oid="f.i.aar">
                          •
                        </span>{" "}
                        Professional chauffeur service
                      </li>
                      <li className="specs-item" data-oid="r9dub84">
                        <span className="specs-item-icon" data-oid="_vxfby.">
                          •
                        </span>{" "}
                        Complimentary Wi-Fi onboard
                      </li>
                      <li className="specs-item" data-oid="mk6c8ps">
                        <span className="specs-item-icon" data-oid="cry_:3d">
                          •
                        </span>{" "}
                        Premium refreshments
                      </li>
                      <li className="specs-item" data-oid="anq9_tp">
                        <span className="specs-item-icon" data-oid="udpjab4">
                          •
                        </span>{" "}
                        Daily newspapers and magazines
                      </li>
                    </>
                  )}
                </ul>
              </div>
              <div className="specs-category" data-oid="qu01nd1">
                <ul className="specs-list" data-oid="5om4zw.">
                  {language === "ar" ? (
                    // Arabic services list (second half)
                    t("vehicles.mercedes-vclass.services-list", {
                      returnObjects: true,
                    })
                      .slice(4)
                      .map((service, index) => (
                        <li
                          key={index + 4}
                          className="specs-item"
                          data-oid="vv16631"
                        >
                          <span className="specs-item-icon" data-oid="u9aar..">
                            •
                          </span>{" "}
                          {service}
                        </li>
                      ))
                  ) : (
                    // English services list (second half)
                    <>
                      <li className="specs-item" data-oid="khzokdc">
                        <span className="specs-item-icon" data-oid="wxdifb2">
                          •
                        </span>{" "}
                        Premium leather upholstery with ambient lighting
                      </li>
                      <li className="specs-item" data-oid="663rn1s">
                        <span className="specs-item-icon" data-oid="u57jn7b">
                          •
                        </span>{" "}
                        Burmester® surround sound system
                      </li>
                      <li className="specs-item" data-oid="ej.esau">
                        <span className="specs-item-icon" data-oid="j6cl16b">
                          •
                        </span>{" "}
                        Panoramic sliding sunroof
                      </li>
                      <li className="specs-item" data-oid="2.-harp">
                        <span className="specs-item-icon" data-oid="u3bo9r9">
                          •
                        </span>{" "}
                        Electric sliding doors for easy access
                      </li>
                    </>
                  )}
                </ul>
              </div>
            </div>
            <p className="section-content" data-oid="90i37ry">
              {language === "ar"
                ? t("vehicles.mercedes-vclass.book-cta")
                : "Book your premium Mercedes V-Class chauffeur service today and enjoy spacious luxury for your group or family."}
            </p>
            <div className="flex justify-center mt-6" data-oid="5agtx48">
              <Link to="/contact" className="cta-button" data-oid="20lzuzz">
                {language === "ar" ? t("common.bookNow") : "Book Now"}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MercedesVClass;
