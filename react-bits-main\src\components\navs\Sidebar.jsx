import {
  Box,
  Flex,
  VStack,
  Text,
  Stack,
  IconButton,
  useColorModeValue,
  <PERSON>er,
  <PERSON>er<PERSON><PERSON>,
  Drawer<PERSON>eader,
  Drawer<PERSON><PERSON>lay,
  DrawerContent,
  Image,
  Divider,
} from "@chakra-ui/react";
import {
  ArrowForwardIcon,
  CloseIcon,
  HamburgerIcon,
  SearchIcon,
} from "@chakra-ui/icons";
import { Link, useLocation } from "react-router-dom";
import Logo from "../../assets/logos/reactbits-logo.svg";
import { useRef, useState } from "react";
import { CATEGORIES, NEW, UPDATED } from "../../constants/Categories";
import { useSearch } from "../context/SearchContext/useSearch";

const scrollToTop = () => window.scrollTo(0, 0);

const Sidebar = () => {
  const [isDrawerOpen, setDrawerOpen] = useState(false);
  const sidebarBgColor = useColorModeValue("gray.100", "black");
  const linkHoverColor = useColorModeValue("gray.800", "gray");
  const btnRef = useRef();
  const location = useLocation();

  const { toggleSearch } = useSearch();

  return (
    <>
      <Box
        display={{ md: "none" }}
        position="fixed"
        top={0}
        left={0}
        zIndex="overlay"
        p="1em"
        w="100%"
        bgColor="#060606"
        data-oid="wfm5u65"
      >
        <Flex
          alignItems="center"
          gap="1em"
          justifyContent="space-between"
          transition=".2s ease"
          transform={isDrawerOpen ? "translateY(-200%)" : "none"}
          data-oid="d--k946"
        >
          <Link to="/" data-oid=".6d4sbe">
            <Image
              src={Logo}
              height="25px"
              alt="React Bits logo"
              data-oid="5g1pe-k"
            />
          </Link>
          <Flex alignItems="center" gap={2} data-oid=".ak5-a:">
            <IconButton
              borderRadius="10px"
              border="1px solid #ffffff1c"
              bg="#060606"
              ref={btnRef}
              icon={<SearchIcon data-oid="o4:mqw5" />}
              onClick={() => {
                setDrawerOpen(false);
                toggleSearch();
              }}
              aria-label="Toggle Search"
              data-oid="n70yp8m"
            />

            <IconButton
              borderRadius="10px"
              border="1px solid #ffffff1c"
              bg="#060606"
              ref={btnRef}
              icon={<HamburgerIcon data-oid="d.fe-t1" />}
              onClick={() => setDrawerOpen(true)}
              aria-label="Open Menu"
              data-oid="39u7j.i"
            />
          </Flex>
        </Flex>
      </Box>

      <Drawer
        isOpen={isDrawerOpen}
        placement="left"
        onClose={() => setDrawerOpen(false)}
        finalFocusRef={btnRef}
        size="full"
        data-oid="kqw73cp"
      >
        <DrawerOverlay data-oid="-_q_e-b" />
        <DrawerContent className="sidebar-mobile" data-oid="4-woemt">
          <DrawerHeader
            py={0}
            h="72px"
            borderBottomWidth="1px"
            className="sidebar-logo"
            data-oid="p2ypt8i"
          >
            <Flex
              alignItems="center"
              justifyContent="space-between"
              data-oid="1ri4zks"
            >
              <Link to="/" data-oid="8006oa4">
                <Image
                  height="25px"
                  src={Logo}
                  alt="Bits Logo"
                  data-oid="62-r:3p"
                />
              </Link>
              <IconButton
                borderRadius="10px"
                border="1px solid #ffffff1c"
                bg="#060606"
                size="md"
                icon={<CloseIcon boxSize={3} data-oid="s0qspv9" />}
                aria-label="Close Menu"
                onClick={() => setDrawerOpen(false)}
                data-oid="g-nethc"
              />
            </Flex>
          </DrawerHeader>
          <DrawerBody pb="6em" data-oid="mgxy55o">
            <VStack align="stretch" spacing={5} mt={8} data-oid="ys8ixcp">
              {CATEGORIES.map((category) => (
                <Category
                  key={category.name}
                  category={category}
                  hoverColor={linkHoverColor}
                  location={location}
                  handleClick={() => {
                    setDrawerOpen(false);
                    scrollToTop();
                  }}
                  data-oid="5gfeyaj"
                />
              ))}
            </VStack>
            <Divider my={4} data-oid=".ng9m_d" />
            <Text color="#a6a6a6" mb={3} data-oid="p-_gjof">
              Useful Links
            </Text>
            <Flex direction="column" data-oid="h5k1ok5">
              <Link
                to="https://github.com/DavidHDev/react-bits"
                target="_blank"
                display="block"
                mb={2}
                onClick={() => setDrawerOpen(false)}
                data-oid="-asvgt_"
              >
                GitHub
                <ArrowForwardIcon
                  boxSize={7}
                  transform="rotate(-45deg)"
                  position="relative"
                  top="-1px"
                  data-oid="ei4fxzj"
                />
              </Link>
              <Link
                to="/showcase"
                display="block"
                mb={2}
                onClick={() => setDrawerOpen(false)}
                data-oid="akcxpoo"
              >
                Showcase
                <ArrowForwardIcon
                  boxSize={7}
                  transform="rotate(-45deg)"
                  position="relative"
                  top="-1px"
                  data-oid="o_u_5oy"
                />
              </Link>
              <Link
                to="https://davidhaz.com/"
                target="_blank"
                display="block"
                mb={2}
                onClick={() => setDrawerOpen(false)}
                data-oid="5bk6krm"
              >
                Who made this?
                <ArrowForwardIcon
                  boxSize={7}
                  transform="rotate(-45deg)"
                  position="relative"
                  top="-1px"
                  data-oid="5v5:6zn"
                />
              </Link>
            </Flex>
          </DrawerBody>
        </DrawerContent>
      </Drawer>

      <Box
        as="nav"
        position="fixed"
        top="57px"
        height="calc(100vh - 57px)"
        className="sidebar"
        overflowY="auto"
        bg={sidebarBgColor}
        w={{ base: 0, md: 40 }}
        p={5}
        display={{ base: "none", md: "block" }}
        data-oid="nvw93ud"
      >
        <VStack align="stretch" spacing={4} data-oid="uf6abc5">
          {CATEGORIES.map((category) => (
            <Category
              key={category.name}
              category={category}
              location={location}
              handleClick={() => scrollToTop()}
              hoverColor={linkHoverColor}
              data-oid="h5b:r21"
            />
          ))}
        </VStack>
      </Box>
    </>
  );
};

const Category = ({ category, handleClick, location }) => {
  const formatForURL = (str) => str.replace(/\s+/g, "-").toLowerCase();
  return (
    <Box data-oid="no_z4cc">
      <Text className="category-name" mb={2} data-oid="w3tjycm">
        {category.name}
      </Text>
      <Stack
        spacing={0.5}
        pl={4}
        borderLeft="1px solid #ffffff1c"
        data-oid="tiqr6wp"
      >
        {category.subcategories.map((sub) => {
          const path = `/${formatForURL(category.name)}/${formatForURL(sub)}`;
          const isActive = location.pathname === path;
          const isNew = NEW.includes(sub);
          const isUpdated = UPDATED.includes(sub);
          return (
            <Link
              key={path}
              className={
                isActive ? "sidebar-item active-sidebar-item" : "sidebar-item"
              }
              to={path}
              onClick={handleClick}
              data-oid="z5vqn-_"
            >
              {sub}
              {isNew && (
                <span className="new-tag" data-oid="pj57m9g">
                  New
                </span>
              )}
              {isUpdated && (
                <span className="updated-tag" data-oid="pzbou-w">
                  Updated
                </span>
              )}
            </Link>
          );
        })}
      </Stack>
    </Box>
  );
};

export default Sidebar;
