/* Model<PERSON>iewer Styles */
.model-viewer-container {
  width: 100%;
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(20, 20, 20, 0.9));
  border: 1px solid rgba(212, 175, 55, 0.3);
  box-shadow: 
    0 15px 35px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(212, 175, 55, 0.1);
  transition: all 0.3s ease;
}

.model-viewer-container:hover {
  border-color: rgba(212, 175, 55, 0.5);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(212, 175, 55, 0.2);
  transform: translateY(-2px);
}

/* Loading Styles */
.model-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #D4AF37;
  z-index: 10;
}

.loader-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(212, 175, 55, 0.3);
  border-top: 3px solid #D4AF37;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.model-loader p {
  font-size: 0.9rem;
  font-weight: 500;
  margin: 0;
  text-align: center;
}

/* Error Styles */
.model-error {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(20, 20, 20, 0.9);
  border-radius: 15px;
  border: 1px solid rgba(212, 175, 55, 0.3);
}

.error-content {
  text-align: center;
  color: #fff;
  padding: 2rem;
}

.error-content h3 {
  color: #D4AF37;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.error-content p {
  color: #e0e0e0;
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

/* Canvas Styles */
.model-viewer-container canvas {
  width: 100% !important;
  height: 100% !important;
  border-radius: 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .model-viewer-container {
    height: 300px !important;
  }
  
  .loader-spinner {
    width: 30px;
    height: 30px;
  }
  
  .model-loader p {
    font-size: 0.8rem;
  }
  
  .error-content {
    padding: 1.5rem;
  }
  
  .error-content h3 {
    font-size: 1rem;
  }
  
  .error-content p {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .model-viewer-container {
    height: 250px !important;
    border-radius: 10px;
  }
  
  .model-viewer-container canvas {
    border-radius: 10px;
  }
}
