import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import {
  Box,
  Flex,
  Slider,
  SliderFilledTrack,
  SliderThumb,
  SliderTrack,
  Switch,
  Text,
} from "@chakra-ui/react";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";

import LiquidChrome from "../../content/Backgrounds/LiquidChrome/LiquidChrome";
import { liquidChrome } from "../../constants/code/Backgrounds/liquidChromeCode";
import { useState } from "react";

const LiquidChromeDemo = () => {
  const [speed, setSpeed] = useState(0.3);
  const [baseColor, setBaseColor] = useState([0.1, 0.1, 0.1]);
  const [interactive, setInteractive] = useState(true);
  const [amplitude, setAmplitude] = useState(0.3);

  const propData = [
    {
      name: "baseColor",
      type: "RGB array (number[3])",
      default: "[0.1, 0.1, 0.1]",
      description: "Base color of the component. Specify as an RGB array.",
    },
    {
      name: "speed",
      type: "number",
      default: "1.0",
      description: "Animation speed multiplier.",
    },
    {
      name: "amplitude",
      type: "number",
      default: "0.6",
      description: "Amplitude of the distortion.",
    },
    {
      name: "frequencyX",
      type: "number",
      default: "2.5",
      description: "Frequency modifier for the x distortion.",
    },
    {
      name: "frequencyY",
      type: "number",
      default: "1.5",
      description: "Frequency modifier for the y distortion.",
    },
    {
      name: "interactive",
      type: "boolean",
      default: "true",
      description: "Enable mouse/touch interaction.",
    },
  ];

  return (
    <TabbedLayout data-oid=":yax0zp">
      <PreviewTab data-oid="awriu:-">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          p={0}
          overflow="hidden"
          data-oid="zny8._q"
        >
          <LiquidChrome
            baseColor={baseColor}
            amplitude={amplitude}
            speed={speed}
            interactive={interactive}
            data-oid="76i5589"
          />
        </Box>

        <div className="preview-options" data-oid="huj:my0">
          <h2 className="demo-title-extra" data-oid="m3x2i1y">
            Customize
          </h2>

          <Text fontSize="sm" data-oid="k3h2fd7">
            Colors
          </Text>
          <Flex gap={4} wrap="wrap" data-oid="6_6l:2v">
            <Flex
              gap={4}
              align="center"
              mt={2}
              background="#0D0D0D"
              pl={4}
              pr={10}
              py={4}
              borderRadius={16}
              position="relative"
              data-oid="e-o1ec4"
            >
              <Text fontSize="sm" data-oid="a1h5_yj">
                R
              </Text>
              <Slider
                min={0.1}
                max={0.5}
                step={0.01}
                value={baseColor[0]}
                onChange={(val) => {
                  setBaseColor((prev) => {
                    const newColors = [...prev];
                    newColors[0] = val;
                    return newColors;
                  });
                }}
                minWidth="60px"
                maxWidth="60px"
                data-oid="hf4rtqh"
              >
                <SliderTrack data-oid="2zk-d7h">
                  <SliderFilledTrack data-oid="okhnfx6" />
                </SliderTrack>
                <SliderThumb data-oid="qn29.m4" />
              </Slider>
              <Text
                position="absolute"
                right={3.5}
                fontSize="sm"
                data-oid="4ylp4e8"
              >
                {baseColor[0]}
              </Text>
            </Flex>

            <Flex
              gap={4}
              align="center"
              mt={2}
              background="#0D0D0D"
              pl={4}
              pr={10}
              py={4}
              borderRadius={16}
              position="relative"
              data-oid=":s2av48"
            >
              <Text fontSize="sm" data-oid="996sf4a">
                G
              </Text>
              <Slider
                min={0.1}
                max={0.5}
                step={0.01}
                value={baseColor[1]}
                onChange={(val) => {
                  setBaseColor((prev) => {
                    const newColors = [...prev];
                    newColors[1] = val;
                    return newColors;
                  });
                }}
                minWidth="60px"
                maxWidth="60px"
                data-oid="0mxm2aw"
              >
                <SliderTrack data-oid="w6_fcvh">
                  <SliderFilledTrack data-oid="pw9f_18" />
                </SliderTrack>
                <SliderThumb data-oid="yjehrdl" />
              </Slider>
              <Text
                position="absolute"
                right={3.5}
                fontSize="sm"
                data-oid="qnr9b9l"
              >
                {baseColor[1]}
              </Text>
            </Flex>

            <Flex
              gap={4}
              align="center"
              mt={2}
              background="#0D0D0D"
              pl={4}
              pr={10}
              py={4}
              borderRadius={16}
              position="relative"
              data-oid="qvhwccx"
            >
              <Text fontSize="sm" data-oid="373:rdw">
                B
              </Text>
              <Slider
                min={0.1}
                max={0.5}
                step={0.01}
                value={baseColor[2]}
                onChange={(val) => {
                  setBaseColor((prev) => {
                    const newColors = [...prev];
                    newColors[2] = val;
                    return newColors;
                  });
                }}
                minWidth="60px"
                maxWidth="60px"
                data-oid="iw35qf6"
              >
                <SliderTrack data-oid="qewm4-:">
                  <SliderFilledTrack data-oid="rxpbzrc" />
                </SliderTrack>
                <SliderThumb data-oid="pxadj5w" />
              </Slider>
              <Text
                position="absolute"
                right={3.5}
                fontSize="sm"
                data-oid="4kdu_tn"
              >
                {baseColor[2]}
              </Text>
            </Flex>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="zjpb:yb">
            <Text fontSize="sm" data-oid="af9423m">
              Speed
            </Text>
            <Slider
              min={0}
              max={5}
              step={0.01}
              value={speed}
              onChange={(val) => {
                setSpeed(val);
              }}
              width="200px"
              data-oid="vszr2hz"
            >
              <SliderTrack data-oid="o6mgqxm">
                <SliderFilledTrack data-oid="6tp1nqn" />
              </SliderTrack>
              <SliderThumb data-oid="dhiqset" />
            </Slider>
            <Text fontSize="sm" data-oid="w0pg0uf">
              {speed}
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="gyzkx_b">
            <Text fontSize="sm" data-oid=":6iaiuv">
              Amplitude
            </Text>
            <Slider
              min={0.1}
              max={1}
              step={0.01}
              value={amplitude}
              onChange={(val) => {
                setAmplitude(val);
              }}
              width="200px"
              data-oid="stpxkl2"
            >
              <SliderTrack data-oid=".o0yjj6">
                <SliderFilledTrack data-oid="2oma:9y" />
              </SliderTrack>
              <SliderThumb data-oid="xyl3d07" />
            </Slider>
            <Text fontSize="sm" data-oid="wl7m_:4">
              {amplitude}
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="7txa3gh">
            <Text fontSize="sm" data-oid="gg9v2.z">
              Interactive
            </Text>
            <Switch
              isChecked={interactive}
              onChange={(e) => {
                setInteractive(e.target.checked);
              }}
              data-oid="j5zh659"
            />
          </Flex>
        </div>

        <PropTable data={propData} data-oid="4au71_g" />
        <Dependencies dependencyList={["ogl"]} data-oid="aefc2q3" />
      </PreviewTab>

      <CodeTab data-oid="ph22e88">
        <CodeExample codeObject={liquidChrome} data-oid="ca7hqwy" />
      </CodeTab>

      <CliTab data-oid=".v40-hn">
        <CliInstallation {...liquidChrome} data-oid="7-u9fn0" />
      </CliTab>
    </TabbedLayout>
  );
};

export default LiquidChromeDemo;
