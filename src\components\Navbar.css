/* Navbar dropdown styles */
.nav-item {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  min-width: 220px;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.15s ease;
  z-index: 100;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-item:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  padding: 0.5rem 1rem;
  color: #aaa6c3;
  font-size: 0.9rem;
  transition: all 0.1s ease;
  white-space: nowrap;
}

.dropdown-item:hover {
  background-color: rgba(212, 175, 55, 0.8);
  color: #0a0a0a;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3);
}

/* Mobile dropdown styles */
.mobile-dropdown-menu {
  margin-left: 1rem;
  margin-top: 0.5rem;
  display: none;
  border-left: 2px solid rgba(212, 175, 55, 0.3);
  padding-left: 0.8rem;
  padding-top: 0.3rem;
  padding-bottom: 0.3rem;
}

.mobile-dropdown-active {
  display: block;
  animation: slideDown 0.3s ease forwards;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-dropdown-item {
  padding: 0.7rem 0.5rem;
  color: #e0e0e0;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  border-radius: 6px;
  margin: 0.3rem 0;
  display: flex;
  align-items: center;
  min-height: 44px; /* Apple's recommended minimum touch target size */
}

.mobile-dropdown-item:hover,
.mobile-dropdown-item:active {
  color: #d4af37;
  background-color: rgba(212, 175, 55, 0.1);
}

.mobile-dropdown-toggle {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0;
  min-height: 44px; /* Apple's recommended minimum touch target size */
}

/* Add animation for mobile menu */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease forwards;
}

.dropdown-arrow {
  margin-left: 0.5rem;
  transition: transform 0.15s ease;
  font-size: 0.7rem;
  opacity: 0.7;
  display: inline-block;
  margin-top: 2px;
}

.dropdown-arrow-active {
  transform: rotate(180deg);
}
