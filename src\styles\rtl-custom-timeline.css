/* Custom RTL Timeline Styles */

.rtl-custom-timeline {
  position: relative;
  width: 100%;
  max-width: 1170px;
  margin: 0 auto;
  padding: 2em 0;
  direction: rtl;
}

/* Center line */
.rtl-timeline-center-line {
  position: absolute;
  content: '';
  width: 4px;
  background: #D4AF37;
  top: 0;
  bottom: 0;
  right: 50%;
  margin-right: -2px;
  z-index: 0;
  height: 100%;
}

/* Timeline item */
.rtl-timeline-item {
  position: relative;
  margin: 4em 0;
  clear: both;
  min-height: 150px;
}

/* Timeline icon */
.rtl-timeline-icon {
  position: absolute;
  width: 60px;
  height: 60px;
  right: 50%;
  margin-right: -30px;
  border-radius: 50%;
  box-shadow: 0 0 0 4px #D4AF37, inset 0 2px 0 rgba(0, 0, 0, 0.08), 0 3px 0 4px rgba(0, 0, 0, 0.05);
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Timeline content */
.rtl-timeline-content {
  position: relative;
  margin-right: 60px;
  background: #000000;
  color: #fff;
  border-radius: 10px;
  padding: 1.5em;
  box-shadow: 0 3px 0 #D4AF37;
  border: 1px solid #D4AF37;
  text-align: right;
  direction: rtl;
  width: 45%;
  box-sizing: border-box;
}

/* Content arrow */
.rtl-timeline-content-arrow {
  position: absolute;
  top: 24px;
  width: 0;
  height: 0;
}

/* Right side content (odd items) */
.rtl-timeline-content-right {
  float: right;
  margin-right: 0;
  margin-left: auto;
}

.rtl-timeline-content-arrow-right {
  right: -7px;
  border-left: 7px solid #000000;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
}

/* Left side content (even items) */
.rtl-timeline-content-left {
  float: left;
  margin-left: 0;
  margin-right: auto;
}

.rtl-timeline-content-arrow-left {
  left: -7px;
  border-right: 7px solid #000000;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
}

/* Date styling */
.rtl-timeline-date {
  position: absolute;
  top: 0;
  width: 100%;
  font-weight: bold;
  color: #D4AF37;
}

.rtl-timeline-date-right {
  left: 124%;
  text-align: left;
}

.rtl-timeline-date-left {
  right: 124%;
  text-align: right;
}

/* Text color for gold text */
.text-gold {
  color: #D4AF37;
}

/* Mobile styles */
@media only screen and (max-width: 1169px) {
  .rtl-timeline-center-line {
    right: 18px;
    margin-right: 0;
  }

  .rtl-timeline-icon {
    right: 0;
    margin-right: -12px;
    width: 40px;
    height: 40px;
  }

  .rtl-timeline-content {
    width: calc(100% - 60px);
    margin-right: 60px;
    margin-left: 0;
    float: none;
  }

  .rtl-timeline-content-right,
  .rtl-timeline-content-left {
    float: none;
    margin-right: 60px;
    margin-left: 0;
  }

  .rtl-timeline-content-right::before,
  .rtl-timeline-content-left::before {
    right: -7px;
    left: auto;
    border-left: 7px solid #000000;
    border-right: none;
  }

  .rtl-timeline-date-right,
  .rtl-timeline-date-left {
    position: relative;
    right: auto;
    left: auto;
    top: auto;
    width: 100%;
    padding: 0.5em 0 0;
    text-align: right;
  }
}

/* Clearfix for timeline items */
.rtl-timeline-item::after {
  content: "";
  display: table;
  clear: both;
}
