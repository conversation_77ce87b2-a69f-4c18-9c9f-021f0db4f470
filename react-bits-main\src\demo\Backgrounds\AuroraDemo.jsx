import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Box, Flex, Text } from "@chakra-ui/react";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";

import { useState } from "react";
import useForceRerender from "../../hooks/useForceRerender";
import Aurora from "../../content/Backgrounds/Aurora/Aurora";
import { aurora } from "../../constants/code/Backgrounds/auroraCode";
import PreviewSlider from "../../components/common/PreviewSlider";
import Customize from "../../components/common/Customize";

const AuroraDemo = () => {
  const [color1, setColor1] = useState("#00d8ff");
  const [color2, setColor2] = useState("#7cff67");
  const [color3, setColor3] = useState("#00d8ff");

  const [speed, setSpeed] = useState(1);
  const [blend, setBlend] = useState(0.5);

  const [key, forceRerender] = useForceRerender();

  const propData = [
    {
      name: "colorStops",
      type: "[string, string, string]",
      default: '["#3A29FF", "#FF94B4", "#FF3232"]',
      description: "An array of three hex colors defining the aurora gradient.",
    },
    {
      name: "speed",
      type: "number",
      default: "1.0",
      description:
        "Controls the animation speed. Higher values make the aurora move faster.",
    },
    {
      name: "blend",
      type: "number",
      default: "0.5",
      description:
        "Controls the blending of the aurora effect with the background.",
    },
    {
      name: "amplitude",
      type: "number",
      default: "1.0",
      description: "Controls the height intensity of the aurora effect.",
    },
  ];

  return (
    <TabbedLayout data-oid="g1-4l5_">
      <PreviewTab data-oid="dbuaq0w">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          p={0}
          overflow="hidden"
          data-oid="0_0st-p"
        >
          <Aurora
            key={key}
            blend={blend}
            speed={speed}
            colorStops={[color1, color2, color3]}
            data-oid="wdu4iks"
          />
        </Box>

        <Customize data-oid="n3lclcu">
          <Flex gap={4} mb={2} data-oid="op6j2em">
            <Flex alignItems="center" data-oid="iqr_.g9">
              <Text mr={2} data-oid="beyax2-">
                Color 1
              </Text>
              <input
                type="color"
                value={color1}
                style={{ height: "22px", outline: "none", border: "none" }}
                onChange={(e) => {
                  setColor1(e.target.value);
                  forceRerender();
                }}
                data-oid="e_gaty5"
              />
            </Flex>

            <Flex alignItems="center" data-oid="-uvnoj.">
              <Text mr={2} data-oid="vx7j6.l">
                Color 2
              </Text>
              <input
                type="color"
                value={color2}
                style={{ height: "22px", outline: "none", border: "none" }}
                onChange={(e) => {
                  setColor2(e.target.value);
                  forceRerender();
                }}
                data-oid="r42njp_"
              />
            </Flex>

            <Flex alignItems="center" data-oid="vecsde9">
              <Text mr={2} data-oid="jb1-ofv">
                Color 3
              </Text>
              <input
                type="color"
                value={color3}
                style={{ height: "22px", outline: "none", border: "none" }}
                onChange={(e) => {
                  setColor3(e.target.value);
                  forceRerender();
                }}
                data-oid="itdjn41"
              />
            </Flex>
          </Flex>

          <PreviewSlider
            title="Speed"
            min={0}
            max={2}
            step={0.1}
            value={speed}
            onChange={(val) => {
              setSpeed(val);
              forceRerender();
            }}
            data-oid="7:p0j.:"
          />

          <PreviewSlider
            title="Blend"
            min={0}
            max={1}
            step={0.01}
            value={blend}
            onChange={(val) => {
              setBlend(val);
              forceRerender();
            }}
            data-oid="ebek1x2"
          />
        </Customize>

        <PropTable data={propData} data-oid="gsldaf7" />
        <Dependencies dependencyList={["ogl"]} data-oid="7u.ea59" />
      </PreviewTab>

      <CodeTab data-oid="t10c4xo">
        <CodeExample codeObject={aurora} data-oid="g042x5o" />
      </CodeTab>

      <CliTab data-oid="4ts42:z">
        <CliInstallation {...aurora} data-oid="w4nz710" />
      </CliTab>
    </TabbedLayout>
  );
};

export default AuroraDemo;
