{"name": "react-bits", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run build:registry && vite build", "build:registry": "concurrently -p \"[{name}]\" -n \"default,tailwind,ts-default,ts-tailwind,readmes\" \"npm run build:registry:default\" \"npm run build:registry:tailwind\" \"npm run build:registry:ts-default\" \"npm run build:registry:ts-tailwind\" \"node ./scripts/createRegistryReadmes.js\"", "build:registry:default": "jsrepo build --dirs ./src/content --output-dir ./public/default", "build:registry:tailwind": "jsrepo build --dirs ./src/tailwind --output-dir ./public/tailwind", "build:registry:ts-default": "jsrepo build --dirs ./src/ts-default --output-dir ./public/ts/default", "build:registry:ts-tailwind": "jsrepo build --dirs ./src/ts-tailwind --output-dir ./public/ts/tailwind", "new:component": "node scripts/generateComponent.js", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@react-three/drei": "^9.121.4", "@react-three/fiber": "^8.17.14", "@react-three/postprocessing": "^2.19.1", "@react-three/rapier": "^1.4.0", "@tailwindcss/vite": "^4.0.3", "framer-motion": "^11.3.24", "gl-matrix": "^3.4.3", "gsap": "^3.13.0", "matter-js": "^0.20.0", "meshline": "^3.3.1", "ogl": "^1.0.11", "postprocessing": "^6.36.0", "react": "^18.3.1", "react-confetti": "^6.2.2", "react-dom": "^18.3.1", "react-haiku": "^2.2.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.2.1", "react-router-dom": "^6.26.0", "react-syntax-highlighter": "^15.5.0", "react-use-gesture": "^9.1.3", "sonner": "^1.7.1", "tailwindcss": "^4.0.3", "three": "^0.167.1"}, "devDependencies": {"@types/matter-js": "^0.19.8", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.4", "concurrently": "^9.1.2", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "jsrepo": "^1.30.1", "postcss-safe-parser": "^7.0.1", "typescript": "^5.7.3", "vite": "^5.3.4"}}