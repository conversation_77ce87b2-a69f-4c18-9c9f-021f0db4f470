import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import {
  FiBarChart2,
  FiBook,
  FiCloud,
  FiEdit,
  FiFileText,
  FiHeart,
} from "react-icons/fi";
import { Box } from "@chakra-ui/react";

import Customize from "../../components/common/Customize";
import PreviewSwitch from "../../components/common/PreviewSwitch";
import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";

import GlassIcons from "../../content/Components/GlassIcons/GlassIcons";
import { glassIcons } from "../../constants/code/Components/glassIconsCode";

const GlassIconsDemo = () => {
  const [colorful, setColorful] = useState(false);

  const propData = [
    {
      name: "items",
      type: "GlassIconsItem[]",
      default: "[]",
      description:
        "Array of items to render. Each item should include: an icon (React.ReactElement), a color (string), a label (string), and an optional customClass (string).",
    },
    {
      name: "className",
      type: "string",
      default: "''",
      description:
        "Optional additional CSS class(es) to be added to the container.",
    },
  ];

  const items = [
    {
      icon: <FiFileText data-oid="pax:cj." />,
      color: colorful ? "blue" : "#444",
      label: "Files",
    },
    {
      icon: <FiBook data-oid="jejbtjb" />,
      color: colorful ? "purple" : "#444",
      label: "Books",
    },
    {
      icon: <FiHeart data-oid="gz1_sta" />,
      color: colorful ? "red" : "#444",
      label: "Health",
    },
    {
      icon: <FiCloud data-oid="ixc8-_w" />,
      color: colorful ? "indigo" : "#444",
      label: "Weather",
    },
    {
      icon: <FiEdit data-oid="uys1c6_" />,
      color: colorful ? "orange" : "#444",
      label: "Notes",
    },
    {
      icon: <FiBarChart2 data-oid="k06276:" />,
      color: colorful ? "green" : "#444",
      label: "Stats",
    },
  ];

  return (
    <TabbedLayout data-oid="hikv58c">
      <PreviewTab data-oid="p575ogv">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          overflow="hidden"
          data-oid="-927ojc"
        >
          <GlassIcons
            items={items}
            className="my-glass-icons"
            data-oid="vpj_vx2"
          />
        </Box>

        <Customize data-oid="7ut5i3.">
          <PreviewSwitch
            title="Colorful"
            isChecked={colorful}
            onChange={(e) => {
              setColorful(e.target.checked);
            }}
            data-oid="ijac63q"
          />
        </Customize>

        <PropTable data={propData} data-oid="sq2ah5h" />
      </PreviewTab>

      <CodeTab data-oid="hakj_8t">
        <CodeExample codeObject={glassIcons} data-oid="7.trfkr" />
      </CodeTab>

      <CliTab data-oid="cy5a6:k">
        <CliInstallation {...glassIcons} data-oid="9bp03.l" />
      </CliTab>
    </TabbedLayout>
  );
};

export default GlassIconsDemo;
