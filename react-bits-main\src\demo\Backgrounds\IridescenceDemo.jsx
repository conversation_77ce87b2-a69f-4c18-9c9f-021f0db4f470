import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import {
  Box,
  Flex,
  Slider,
  SliderFilledTrack,
  SliderThumb,
  SliderTrack,
  Switch,
  Text,
} from "@chakra-ui/react";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import useForceRerender from "../../hooks/useForceRerender";

import Iridescence from "../../content/Backgrounds/Iridescence/Iridescence";
import { iridescence } from "../../constants/code/Backgrounds/iridescenceCode";

const IridescenceDemo = () => {
  const [colors, setColors] = useState([1, 1, 1]);

  const [speed, setSpeed] = useState(1);
  const [mouseInteraction, setMouseInteraction] = useState(true);

  const [key, forceRerender] = useForceRerender();

  const propData = [
    {
      name: "color",
      type: "Array<number>",
      default: "[0.3, 0.2, 0.5]",
      description:
        "Base color as an array of RGB values (each between 0 and 1).",
    },
    {
      name: "speed",
      type: "number",
      default: "1.0",
      description: "Speed multiplier for the animation.",
    },
    {
      name: "amplitude",
      type: "number",
      default: "0.1",
      description: "Amplitude for the mouse-driven effect.",
    },
    {
      name: "mouseReact",
      type: "boolean",
      default: "false",
      description: "Enable or disable mouse interaction with the shader.",
    },
  ];

  return (
    <TabbedLayout data-oid="sghe1b8">
      <PreviewTab data-oid=":kgq4-4">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          p={0}
          overflow="hidden"
          data-oid="5yuv.jr"
        >
          <Iridescence
            key={key}
            speed={speed}
            color={colors}
            mouseReact={mouseInteraction}
            data-oid="8.ww-eb"
          />
        </Box>

        <div className="preview-options" data-oid="2fuxmmd">
          <h2 className="demo-title-extra" data-oid="xz0ni1b">
            Customize
          </h2>

          <Text fontSize="sm" data-oid="_sdvb_3">
            Colors
          </Text>
          <Flex gap={4} wrap="wrap" data-oid="vs9nos8">
            <Flex
              gap={4}
              align="center"
              mt={2}
              background="#0D0D0D"
              pl={4}
              pr={10}
              py={4}
              borderRadius={16}
              position="relative"
              data-oid="1r5.4qi"
            >
              <Text fontSize="sm" data-oid="knbs_vu">
                R
              </Text>
              <Slider
                min={0}
                max={1}
                step={0.1}
                value={colors[0]}
                onChange={(val) => {
                  setColors((prev) => {
                    const newColors = [...prev];
                    newColors[0] = val;
                    return newColors;
                  });
                }}
                minWidth="60px"
                maxWidth="60px"
                data-oid="q.hflta"
              >
                <SliderTrack data-oid=".mm9-cr">
                  <SliderFilledTrack data-oid="ifmbd64" />
                </SliderTrack>
                <SliderThumb data-oid="7q0511j" />
              </Slider>
              <Text
                position="absolute"
                right={3.5}
                fontSize="sm"
                data-oid="vuywgff"
              >
                {colors[0]}
              </Text>
            </Flex>

            <Flex
              gap={4}
              align="center"
              mt={2}
              background="#0D0D0D"
              pl={4}
              pr={10}
              py={4}
              borderRadius={16}
              position="relative"
              data-oid="0t8sb_k"
            >
              <Text fontSize="sm" data-oid="i671d:x">
                G
              </Text>
              <Slider
                min={0}
                max={1}
                step={0.1}
                value={colors[1]}
                onChange={(val) => {
                  setColors((prev) => {
                    const newColors = [...prev];
                    newColors[1] = val;
                    return newColors;
                  });
                }}
                minWidth="60px"
                maxWidth="60px"
                data-oid="jrt4eec"
              >
                <SliderTrack data-oid="6wcj2_q">
                  <SliderFilledTrack data-oid="koj_d.x" />
                </SliderTrack>
                <SliderThumb data-oid="rou7802" />
              </Slider>
              <Text
                position="absolute"
                right={3.5}
                fontSize="sm"
                data-oid=":tno9mk"
              >
                {colors[1]}
              </Text>
            </Flex>

            <Flex
              gap={4}
              align="center"
              mt={2}
              background="#0D0D0D"
              pl={4}
              pr={10}
              py={4}
              borderRadius={16}
              position="relative"
              data-oid="-m.ullm"
            >
              <Text fontSize="sm" data-oid="b:yn345">
                B
              </Text>
              <Slider
                min={0}
                max={1}
                step={0.1}
                value={colors[2]}
                onChange={(val) => {
                  setColors((prev) => {
                    const newColors = [...prev];
                    newColors[2] = val;
                    return newColors;
                  });
                }}
                minWidth="60px"
                maxWidth="60px"
                data-oid="htbhdmm"
              >
                <SliderTrack data-oid="y6rlkyf">
                  <SliderFilledTrack data-oid="d-he58y" />
                </SliderTrack>
                <SliderThumb data-oid="-25z1u1" />
              </Slider>
              <Text
                position="absolute"
                right={3.5}
                fontSize="sm"
                data-oid="2ussc:r"
              >
                {colors[2]}
              </Text>
            </Flex>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="od599dw">
            <Text fontSize="sm" data-oid="zq_do71">
              Speed
            </Text>
            <Slider
              min={0}
              max={2}
              step={0.1}
              value={speed}
              onChange={(val) => {
                setSpeed(val);
                forceRerender();
              }}
              width="200px"
              data-oid="f3hdlqy"
            >
              <SliderTrack data-oid="ws4.am:">
                <SliderFilledTrack data-oid="aaxwh2n" />
              </SliderTrack>
              <SliderThumb data-oid="an.1dtx" />
            </Slider>
            <Text fontSize="sm" data-oid="j84o2n-">
              {speed}
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="4nf3-r8">
            <Text fontSize="sm" data-oid="vn-8wf6">
              Mouse Interaction
            </Text>
            <Switch
              isChecked={mouseInteraction}
              onChange={(e) => {
                setMouseInteraction(e.target.checked);
                forceRerender();
              }}
              data-oid="b0mfm1w"
            />
          </Flex>
        </div>

        <PropTable data={propData} data-oid="..qs4z3" />
        <Dependencies dependencyList={["ogl"]} data-oid="qcbysr9" />
      </PreviewTab>

      <CodeTab data-oid="yo.92-d">
        <CodeExample codeObject={iridescence} data-oid="20bb-kh" />
      </CodeTab>

      <CliTab data-oid="26mo8vx">
        <CliInstallation {...iridescence} data-oid="62wn.i:" />
      </CliTab>
    </TabbedLayout>
  );
};

export default IridescenceDemo;
