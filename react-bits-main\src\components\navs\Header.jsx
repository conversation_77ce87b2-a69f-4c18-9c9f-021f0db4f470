import { Link } from "react-router-dom";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Di<PERSON>r,
  <PERSON>er,
  <PERSON>er<PERSON>ody,
  Drawer<PERSON>ontent,
  DrawerOverlay,
  Flex,
  Icon,
  IconButton,
  Image,
  Kbd,
  Select,
  Spinner,
  Text,
  useDisclosure,
} from "@chakra-ui/react";
import { ArrowForwardIcon, HamburgerIcon, CloseIcon } from "@chakra-ui/icons";
import { TiStarFullOutline } from "react-icons/ti";
import { useStars } from "../../hooks/useStars";
import { useDeviceOS } from "react-haiku";
import { useSearch } from "../context/SearchContext/useSearch";
import { useLanguage } from "../context/LanguageContext/useLanguage";

import Logo from "../../assets/logos/reactbits-logo.svg";
import BlurText from "../../content/TextAnimations/BlurText/BlurText";
import FadeContent from "../../content/Animations/FadeContent/FadeContent";

const Header = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { toggleSearch } = useSearch();
  const { languagePreset, setLanguagePreset } = useLanguage();
  const stars = useStars();
  const os = useDeviceOS();

  return (
    <Box zIndex={100} className="main-nav" data-oid="0lhcl3m">
      <Flex
        className="nav-items"
        h={20}
        alignItems="center"
        justifyContent="space-between"
        data-oid="3jg7q_-"
      >
        <Link to="/" className="logo" data-oid="am0c5ul">
          <Image src={Logo} alt="Logo" data-oid="w_uih0g" />
        </Link>

        {/* Hamburger menu button for small screens */}
        <IconButton
          size="md"
          icon={<HamburgerIcon data-oid="kkl3z7d" />}
          aria-label="Open Menu"
          display={{ md: "none" }}
          onClick={onOpen}
          data-oid="lsk.hi:"
        />

        {/* Links for larger screens */}
        <Flex
          display={{ base: "none", md: "flex" }}
          alignItems="center"
          gap={2}
          data-oid="1ognj.3"
        >
          <FadeContent blur data-oid="x4wh.fg">
            <Flex
              fontSize="xs"
              h={8}
              border="1px solid #222"
              rounded="xl"
              alignItems="center"
              pr={2}
              pl={4}
              userSelect="none"
              cursor="pointer"
              transition="transform 0.3s"
              _hover={{ transform: "scale(0.98)" }}
              bg="#111"
              onClick={toggleSearch}
              data-oid="9jj_ycq"
            >
              <Text fontSize="xs" fontWeight={600} mr={12} data-oid="m5rocik">
                Search Docs
              </Text>
              {os === "macOS" ? (
                <Kbd data-oid="o0o1skw">⌘ K</Kbd>
              ) : (
                <Kbd data-oid="biulc2z">CTRL K</Kbd>
              )}
            </Flex>
          </FadeContent>
          <FadeContent blur data-oid="h05jjan">
            <Select
              fontSize="xs"
              bg="#111"
              cursor="pointer"
              border="1px solid #222"
              transition="transform 0.3s"
              _hover={{ transform: "scale(0.98)", border: "1px solid #222" }}
              h={8}
              rounded="xl"
              width="fit-content"
              fontWeight={600}
              onChange={(e) => setLanguagePreset(e.target.value)}
              value={languagePreset || "JS"}
              data-oid="z08iikl"
            >
              <option value="JS" data-oid="5d8jya-">
                JS
              </option>
              <option value="TS" data-oid="kcn_pbu">
                TS
              </option>
            </Select>
          </FadeContent>
          <FadeContent blur data-oid="hyk4em2">
            <Button
              border="1px solid #222"
              rounded="xl"
              as="a"
              href="https://github.com/DavidHDev/react-bits"
              rel="noreferrer"
              target="_blank"
              bg="white"
              color="black"
              padding="0 1.2em 0 1em"
              fontSize="xs"
              h={8}
              _hover={{ bg: "white", transform: "scale(0.95)" }}
              data-oid="5vpkgwp"
            >
              <Text fontWeight={600} ml={1} data-oid="quh.wku">
                Star on GitHub
              </Text>
              <Icon ml={2} mr={0.5} as={TiStarFullOutline} data-oid="ywu:iq8" />
              {stars ? (
                <BlurText delay={20} text={String(stars)} data-oid="2prfcmq" />
              ) : (
                <Box data-oid="d7q81t-">
                  <Spinner boxSize={2} data-oid="m:dr2ts" />
                </Box>
              )}
            </Button>
          </FadeContent>
        </Flex>
      </Flex>

      {/* Drawer for mobile navigation */}
      <Drawer
        placement="top"
        onClose={onClose}
        isOpen={isOpen}
        data-oid="_s-xgye"
      >
        <DrawerOverlay display={{ md: "none" }} data-oid="_s-sy89">
          <DrawerContent bg="black" h="100%" data-oid="srzvrxk">
            <DrawerBody px={0} py={0} data-oid="oz.0:oy">
              <Flex direction="column" data-oid="35p-hzc">
                <Flex
                  alignItems="center"
                  justifyContent="space-between"
                  h="57px"
                  mb={6}
                  borderBottom="1px solid #ffffff1c"
                  px={6}
                  data-oid="qmi3lhp"
                >
                  <Image
                    src={Logo}
                    alt="Logo"
                    height="25px"
                    data-oid="x:dvjco"
                  />

                  <IconButton
                    size="md"
                    icon={<CloseIcon boxSize={3} data-oid="8wff104" />}
                    aria-label="Close Menu"
                    display={{ md: "none" }}
                    onClick={onClose}
                    data-oid="kz6_3xc"
                  />
                </Flex>
                <Flex direction="column" px={6} data-oid="pkq04dq">
                  <p data-oid="yzr:tos">Useful Links</p>
                  <Link
                    to="/text-animations/split-text"
                    display="block"
                    mb={2}
                    onClick={onClose}
                    data-oid="-wr-ige"
                  >
                    Docs
                  </Link>
                  <Link
                    to="https://github.com/DavidHDev/react-bits"
                    target="_blank"
                    display="block"
                    mb={2}
                    onClick={onClose}
                    data-oid="kekn562"
                  >
                    GitHub{" "}
                    <ArrowForwardIcon
                      boxSize={7}
                      transform="rotate(-45deg)"
                      position="relative"
                      top="-1px"
                      data-oid="dgdt7kc"
                    />
                  </Link>
                  <Divider data-oid="ujh-g9o" />
                  <p data-oid="90bjc_a">Other</p>
                  <Link
                    to="https://davidhaz.com/"
                    target="_blank"
                    display="block"
                    mb={2}
                    onClick={onClose}
                    data-oid="w21vr1_"
                  >
                    Who made this?
                    <ArrowForwardIcon
                      boxSize={7}
                      transform="rotate(-45deg)"
                      position="relative"
                      top="-1px"
                      data-oid="25gw518"
                    />
                  </Link>
                </Flex>
              </Flex>
            </DrawerBody>
          </DrawerContent>
        </DrawerOverlay>
      </Drawer>
    </Box>
  );
};

export default Header;
