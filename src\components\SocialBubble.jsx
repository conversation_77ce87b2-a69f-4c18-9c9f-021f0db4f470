import React, { useState } from "react";
import {
  Fa<PERSON><PERSON><PERSON><PERSON>,
  FaEnvelope,
  FaInstagram,
  FaPhone,
  FaComments,
  FaTimes,
} from "react-icons/fa";
import "../styles/socialBubble.css";

const SocialBubble = () => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleBubble = () => {
    setIsOpen(!isOpen);
  };

  // WhatsApp message
  const whatsappMessage = encodeURIComponent(
    "Hello, I'm interested in your premium chauffeur services.",
  );
  const whatsappLink = `https://wa.me/4917631454340?text=${whatsappMessage}`;

  // Email details
  const emailSubject = encodeURIComponent(
    "Inquiry about Premium Chauffeur Services",
  );
  const emailBody = encodeURIComponent(
    "Hello,\n\nI'm interested in your premium chauffeur services.\n\nBest regards,",
  );
  const emailLink = `mailto:<EMAIL>?subject=${emailSubject}&body=${emailBody}`;

  // Phone link
  const phoneLink = "tel:+4917631454340";

  // Instagram link
  const instagramLink = "https://instagram.com/";

  return (
    <div className="social-bubble-container" data-oid="9adrbad">
      {/* Main bubble */}
      <div
        className={`main-bubble ${isOpen ? "open" : ""}`}
        onClick={toggleBubble}
        title={isOpen ? "Close" : "Contact Us"}
        data-oid="63lqqkg"
      >
        <span className="bubble-icon" data-oid="s4-p89l">
          {isOpen ? (
            <FaTimes data-oid="jqoxqbc" />
          ) : (
            <FaComments data-oid="_o9tiqr" />
          )}
        </span>
        <span className="bubble-tooltip" data-oid="s6zske3">
          {isOpen ? "Close" : "Contact Us"}
        </span>
      </div>

      {/* Social media icons */}
      <div
        className={`social-icons ${isOpen ? "open" : ""}`}
        data-oid="8_3ak1:"
      >
        <a
          href={whatsappLink}
          target="_blank"
          rel="noopener noreferrer"
          className="social-icon whatsapp"
          title="WhatsApp"
          data-oid="wll7mob"
        >
          <FaWhatsapp data-oid="-g4o_qi" />
        </a>
        <a
          href={emailLink}
          className="social-icon email"
          title="Email"
          data-oid="_x_n3yl"
        >
          <FaEnvelope data-oid="mp87:y1" />
        </a>
        <a
          href={instagramLink}
          target="_blank"
          rel="noopener noreferrer"
          className="social-icon instagram"
          title="Instagram"
          data-oid="ay0r_xm"
        >
          <FaInstagram data-oid="_50cs.j" />
        </a>
        <a
          href={phoneLink}
          className="social-icon phone"
          title="Call Us"
          data-oid="g4qd4_m"
        >
          <FaPhone data-oid="vdjm.5:" />
        </a>
      </div>
    </div>
  );
};

export default SocialBubble;
