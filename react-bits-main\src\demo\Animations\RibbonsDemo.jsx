import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Box, Flex, IconButton, Text } from "@chakra-ui/react";
import { <PERSON><PERSON><PERSON>, FiPlus } from "react-icons/fi";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import PreviewSlider from "../../components/common/PreviewSlider";
import PreviewSwitch from "../../components/common/PreviewSwitch";
import Customize from "../../components/common/Customize";

import Ribbons from "../../content/Animations/Ribbons/Ribbons";
import { ribbons } from "../../constants/code/Animations/ribbonsCode";

const RibbonsDemo = () => {
  const [baseThickness, setBaseThickness] = useState(30);
  const [colors, setColors] = useState(["#00d8ff"]);
  const [speedMultiplier, setSpeedMultiplier] = useState(0.5);
  const [maxAge, setMaxAge] = useState(500);

  const [enableFade, setEnableFade] = useState(false);
  const [enableWaves, setEnableWaves] = useState(false);

  const propData = [
    {
      name: "colors",
      type: "string[]",
      default: "['#00d8ff']",
      description: "An array of color strings to be used for the ribbons.",
    },
    {
      name: "baseSpring",
      type: "number",
      default: "0.03",
      description:
        "Base spring factor for the physics controlling ribbon motion.",
    },
    {
      name: "baseFriction",
      type: "number",
      default: "0.9",
      description: "Base friction factor that dampens the ribbon motion.",
    },
    {
      name: "baseThickness",
      type: "number",
      default: "30",
      description: "The base thickness of the ribbons.",
    },
    {
      name: "offsetFactor",
      type: "number",
      default: "0.02",
      description:
        "A factor to horizontally offset the starting positions of the ribbons.",
    },
    {
      name: "maxAge",
      type: "number",
      default: "500",
      description:
        "Delay in milliseconds controlling how long the ribbon trails extend.",
    },
    {
      name: "pointCount",
      type: "number",
      default: "50",
      description: "The number of points that make up each ribbon.",
    },
    {
      name: "speedMultiplier",
      type: "number",
      default: "0.5",
      description:
        "Multiplier that adjusts how fast trailing points interpolate towards the head.",
    },
    {
      name: "enableFade",
      type: "boolean",
      default: "true",
      description:
        "If true, a fade effect is applied along the length of the ribbon.",
    },
    {
      name: "enableShaderEffect",
      type: "boolean",
      default: "true",
      description:
        "If true, an additional sine-wave shader effect is applied to the ribbons.",
    },
    {
      name: "effectAmplitude",
      type: "number",
      default: "2",
      description: "The amplitude of the shader displacement effect.",
    },
    {
      name: "backgroundColor",
      type: "number[]",
      default: "[0, 0, 0, 0]",
      description: "An RGBA array specifying the clear color for the renderer.",
    },
  ];

  return (
    <TabbedLayout data-oid="kw_x59m">
      <PreviewTab data-oid="l0_pg:6">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          p={0}
          overflow="hidden"
          data-oid="wgj81jo"
        >
          <Text
            position="absolute"
            fontSize="clamp(2rem, 6vw, 6rem)"
            fontWeight={900}
            color="#222"
            data-oid="nkuhmcp"
          >
            Hover Me.
          </Text>
          <Ribbons
            baseThickness={baseThickness}
            colors={colors}
            speedMultiplier={speedMultiplier}
            maxAge={maxAge}
            enableFade={enableFade}
            enableShaderEffect={enableWaves}
            data-oid="7cnn_u1"
          />
        </Box>

        <Customize data-oid="k9s07b3">
          <Flex gap={4} align="center" mt={4} data-oid="1h2fhg.">
            <Text fontSize="sm" data-oid="bcjt:vi">
              Count
            </Text>
            <IconButton
              icon={<FiMinus data-oid="u55-ah5" />}
              onClick={() =>
                colors.length > 1 && setColors(colors.slice(0, -1))
              }
              data-oid="aoqbvj0"
            ></IconButton>
            <Text data-oid="-wf7p_p">{colors.length}</Text>
            <IconButton
              icon={<FiPlus data-oid="bs6_3m." />}
              onClick={() => {
                if (colors.length < 10) {
                  const newColor = `#${Math.floor(Math.random() * 16777215)
                    .toString(16)
                    .padStart(6, "0")}`;
                  setColors([...colors, newColor]);
                }
              }}
              data-oid=":zwc0ia"
            />
          </Flex>

          <PreviewSlider
            title="Thickness"
            min={1}
            max={60}
            step={1}
            value={baseThickness}
            onChange={setBaseThickness}
            data-oid="-b.sjlg"
          />

          <PreviewSlider
            title="Speed"
            min={0.3}
            max={0.7}
            step={0.01}
            value={speedMultiplier}
            onChange={setSpeedMultiplier}
            data-oid="30pqhni"
          />

          <PreviewSlider
            title="Max Age"
            min={300}
            max={1000}
            step={100}
            value={maxAge}
            onChange={setMaxAge}
            data-oid="a5f4t2o"
          />

          <PreviewSwitch
            title="Enable Fade"
            isChecked={enableFade}
            onChange={(e) => setEnableFade(e.target.checked)}
            data-oid="8bg_7s9"
          />

          <PreviewSwitch
            title="Enable Waves"
            isChecked={enableWaves}
            onChange={(e) => setEnableWaves(e.target.checked)}
            data-oid="t14_b2e"
          />
        </Customize>

        <PropTable data={propData} data-oid="r2f_.2." />
        <Dependencies dependencyList={["ogl"]} data-oid="_hjzccp" />
      </PreviewTab>

      <CodeTab data-oid="tqr68qe">
        <CodeExample codeObject={ribbons} data-oid="2kxgdt0" />
      </CodeTab>

      <CliTab data-oid="_40t4qc">
        <CliInstallation {...ribbons} data-oid="rr1ko-7" />
      </CliTab>
    </TabbedLayout>
  );
};

export default RibbonsDemo;
