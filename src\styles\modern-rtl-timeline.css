/* Modern RTL Timeline Styles */

.modern-rtl-timeline {
  position: relative;
  width: 100%;
  max-width: 1170px;
  margin: 0 auto;
  padding: 2em 0;
  direction: rtl;
}

/* Timeline item */
.modern-timeline-item {
  position: relative;
  display: flex;
  margin: 3em 0;
  align-items: flex-start;
}

/* Timeline connector */
.modern-timeline-connector {
  position: relative;
  width: 60px;
  min-width: 60px;
  height: 100%;
  display: flex;
  justify-content: center;
}

.modern-timeline-connector::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: -50px; /* Extend below to connect to next item */
  width: 4px;
  background: #D4AF37;
  right: 50%;
  transform: translateX(50%);
  z-index: 0;
}

/* First and last item connector adjustments */
.modern-timeline-item:first-child .modern-timeline-connector::before {
  top: 30px;
}

.modern-timeline-item:last-child .modern-timeline-connector::before {
  bottom: 0;
  height: auto;
}

/* Add gold glow effect to the timeline line */
@keyframes goldPulse {
  0% { box-shadow: 0 0 5px rgba(212, 175, 55, 0.5); }
  50% { box-shadow: 0 0 10px rgba(212, 175, 55, 0.8); }
  100% { box-shadow: 0 0 5px rgba(212, 175, 55, 0.5); }
}

.modern-timeline-connector::before {
  animation: goldPulse 3s infinite;
}

/* Timeline icon */
.modern-timeline-icon {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #000;
  box-shadow: 0 0 0 4px #D4AF37, inset 0 2px 0 rgba(0, 0, 0, 0.08), 0 3px 0 4px rgba(0, 0, 0, 0.05);
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Timeline content */
.modern-timeline-content {
  flex: 1;
  background: #000000;
  color: #fff;
  border-radius: 10px;
  padding: 1.5em;
  margin-right: 1.5em;
  box-shadow: 0 3px 0 #D4AF37;
  border: 1px solid #D4AF37;
  text-align: right;
  direction: rtl;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.modern-timeline-content:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
}

/* Content arrow */
.modern-timeline-content::before {
  content: '';
  position: absolute;
  top: 24px;
  right: -10px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 10px solid #D4AF37;
}

.modern-timeline-content::after {
  content: '';
  position: absolute;
  top: 24px;
  right: -9px;
  width: 0;
  height: 0;
  border-top: 9px solid transparent;
  border-bottom: 9px solid transparent;
  border-left: 9px solid #000000;
}

/* Timeline header */
.modern-timeline-header {
  position: relative;
  margin-bottom: 1em;
  padding-bottom: 1em;
  border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

/* Timeline date */
.modern-timeline-date {
  margin-top: 0.5em;
  font-size: 0.9em;
  color: #D4AF37;
}

/* Timeline points */
.modern-timeline-points {
  list-style-type: disc;
  padding-right: 1.5em;
  margin: 0;
}

.modern-timeline-point {
  margin-bottom: 0.5em;
  color: #f5f5f5;
  font-size: 14px;
  line-height: 1.6;
}

/* Text color for gold text */
.text-gold {
  color: #D4AF37;
}

/* Responsive styles */
@media only screen and (max-width: 768px) {
  .modern-timeline-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .modern-timeline-connector {
    height: 60px;
    margin-bottom: 1em;
  }

  .modern-timeline-connector::before {
    top: 60px;
    bottom: 0;
    right: 50%;
    height: 100%;
  }

  .modern-timeline-content {
    margin-right: 0;
    width: 100%;
  }

  .modern-timeline-content::before,
  .modern-timeline-content::after {
    display: none;
  }

  .modern-timeline-header,
  .modern-timeline-points {
    text-align: center;
  }

  .modern-timeline-points {
    padding-right: 0;
    list-style-position: inside;
  }
}
