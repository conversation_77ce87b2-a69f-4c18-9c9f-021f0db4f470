import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Box, FormControl, FormLabel, Input, Text } from "@chakra-ui/react";

import Customize from "../../components/common/Customize";
import PreviewSwitch from "../../components/common/PreviewSwitch";
import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import useForceRerender from "../../hooks/useForceRerender";

import TextCursor from "../../content/TextAnimations/TextCursor/TextCursor";
import { textCursor } from "../../constants/code/TextAnimations/textCursorCode";

const TextCursorDemo = () => {
  const [text, setText] = useState("⚛️");
  const [followMouseDirection, setFollowMouseDirection] = useState(true);
  const [randomFloat, setRandomFloat] = useState(true);

  const [key, forceRerender] = useForceRerender();

  const propData = [
    {
      name: "text",
      type: "string",
      default: "⚛️",
      description: "The text string to display as the trail.",
    },
    {
      name: "delay",
      type: "number",
      default: "0.01",
      description:
        "The entry stagger delay in seconds for the fade-out animation.",
    },
    {
      name: "spacing",
      type: "number",
      default: "100",
      description: "The spacing in pixels between each trail point.",
    },
    {
      name: "followMouseDirection",
      type: "boolean",
      default: "true",
      description: "If true, each text rotates to follow the mouse direction.",
    },
    {
      name: "randomFloat",
      type: "boolean",
      default: "true",
      description:
        "If true, enables random floating offsets in position and rotation for a dynamic effect.",
    },
    {
      name: "exitDuration",
      type: "number",
      default: "0.5",
      description:
        "The duration in seconds for the exit animation of each trail item.",
    },
    {
      name: "removalInterval",
      type: "number",
      default: "30",
      description:
        "The interval in milliseconds between removing trail items when the mouse stops moving.",
    },
    {
      name: "maxPoints",
      type: "number",
      default: "5",
      description: "The maximum number of trail points to display.",
    },
  ];

  return (
    <TabbedLayout data-oid="l7e5n9v">
      <PreviewTab data-oid="_qbuqae">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          overflow="hidden"
          data-oid="5ykk4kq"
        >
          <TextCursor
            key={key}
            text={text}
            followMouseDirection={followMouseDirection}
            randomFloat={randomFloat}
            data-oid="cvztcga"
          />

          <Text
            pointerEvents="none"
            position="absolute"
            textAlign="center"
            fontSize="4rem"
            fontWeight={900}
            userSelect="none"
            color="#222"
            data-oid="s7try2i"
          >
            Hover Around!
          </Text>
        </Box>

        <Customize data-oid="s8peh60">
          <FormControl w="200px" data-oid="9gk2oig">
            <FormLabel fontSize="sm" data-oid="hgaklye">
              Text
            </FormLabel>
            <Input
              value={text}
              maxLength={10}
              onChange={(e) => {
                setText(e.target.value);
              }}
              placeholder="Enter text..."
              data-oid="jsi4l_t"
            />
          </FormControl>

          <PreviewSwitch
            title="Follow Mouse Direction"
            isChecked={followMouseDirection}
            onChange={(e) => {
              setFollowMouseDirection(e.target.checked);
              forceRerender();
            }}
            data-oid="z14vatq"
          />

          <PreviewSwitch
            title="Enable Random Floating"
            isChecked={randomFloat}
            onChange={(e) => {
              setRandomFloat(e.target.checked);
              forceRerender();
            }}
            data-oid="od4ywu1"
          />
        </Customize>

        <PropTable data={propData} data-oid="2z7.0i0" />
        <Dependencies dependencyList={["framer-motion"]} data-oid="ugm99fk" />
      </PreviewTab>

      <CodeTab data-oid="c980vho">
        <CodeExample codeObject={textCursor} data-oid="v0rr2jf" />
      </CodeTab>

      <CliTab data-oid="pijf3so">
        <CliInstallation {...textCursor} data-oid="w.ua02a" />
      </CliTab>
    </TabbedLayout>
  );
};

export default TextCursorDemo;
