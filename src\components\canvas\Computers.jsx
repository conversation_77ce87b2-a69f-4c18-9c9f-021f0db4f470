import React, { Suspense, useEffect, useState } from "react";
import { Canvas } from "@react-three/fiber";
import { OrbitControls, Preload, useGLTF } from "@react-three/drei";
import { DRACOLoader } from "three/addons/loaders/DRACOLoader";
import CanvasLoader from "../Loader";

const ComputerModel = ({ isMobile }) => {
  // Create a simple fallback mesh in case the model isn't available
  const createFallbackMesh = () => {
    return (
      <mesh data-oid="w:ka5k1">
        <hemisphereLight
          intensity={0.15}
          groundColor="black"
          data-oid="30fxgqw"
        />

        <spotLight
          position={[-20, 50, 10]}
          angle={0.12}
          penumbra={1}
          intensity={1}
          castShadow
          shadow-mapSize={1024}
          data-oid="xr2:r:4"
        />

        <pointLight intensity={1} data-oid="l59uuo3" />
        <boxGeometry args={[3, 1.5, 5]} data-oid="l68sij5" />
        <meshStandardMaterial color="#915EFF" data-oid=".6h2hb7" />
      </mesh>
    );
  };

  // Create a car model using primitive shapes
  const createCarMesh = () => {
    return (
      <mesh data-oid="y:1ru9-">
        <hemisphereLight
          intensity={0.15}
          groundColor="black"
          data-oid="l:fkwft"
        />

        <spotLight
          position={[-20, 50, 10]}
          angle={0.12}
          penumbra={1}
          intensity={1}
          castShadow
          shadow-mapSize={1024}
          data-oid="oyb6h-m"
        />

        <pointLight intensity={1} data-oid="9-.pkps" />

        {/* Car body */}
        <group
          position={[0, -1.5, 0]}
          rotation={[0, Math.PI / 4, 0]}
          data-oid=".rktwef"
        >
          {/* Main body */}
          <mesh position={[0, 0, 0]} data-oid="zp-mpgi">
            <boxGeometry args={[4, 1, 2]} data-oid="3e-2dub" />
            <meshStandardMaterial
              color="#333333"
              metalness={0.8}
              roughness={0.2}
              data-oid="boxejr6"
            />
          </mesh>

          {/* Roof */}
          <mesh position={[0, 0.75, 0]} data-oid="xyxa4q8">
            <boxGeometry args={[2.5, 0.5, 1.8]} data-oid=".uj18rp" />
            <meshStandardMaterial
              color="#222222"
              metalness={0.8}
              roughness={0.2}
              data-oid="x.vfe35"
            />
          </mesh>

          {/* Hood */}
          <mesh position={[1.5, -0.1, 0]} data-oid=".z2k_58">
            <boxGeometry args={[1, 0.4, 1.8]} data-oid="333-y32" />
            <meshStandardMaterial
              color="#333333"
              metalness={0.8}
              roughness={0.2}
              data-oid="q6xbldm"
            />
          </mesh>

          {/* Trunk */}
          <mesh position={[-1.5, -0.1, 0]} data-oid="ttvgii7">
            <boxGeometry args={[1, 0.4, 1.8]} data-oid="koayuin" />
            <meshStandardMaterial
              color="#333333"
              metalness={0.8}
              roughness={0.2}
              data-oid=":vecpem"
            />
          </mesh>

          {/* Wheels */}
          <mesh
            position={[1.2, -0.6, 1]}
            rotation={[Math.PI / 2, 0, 0]}
            data-oid="934o5yz"
          >
            <cylinderGeometry args={[0.4, 0.4, 0.2, 32]} data-oid="1:bal7m" />
            <meshStandardMaterial color="#111111" data-oid="bk99ih-" />
          </mesh>

          <mesh
            position={[1.2, -0.6, -1]}
            rotation={[Math.PI / 2, 0, 0]}
            data-oid="1z-96yr"
          >
            <cylinderGeometry args={[0.4, 0.4, 0.2, 32]} data-oid="z0lswph" />
            <meshStandardMaterial color="#111111" data-oid="f90r0u6" />
          </mesh>

          <mesh
            position={[-1.2, -0.6, 1]}
            rotation={[Math.PI / 2, 0, 0]}
            data-oid="ovbqivi"
          >
            <cylinderGeometry args={[0.4, 0.4, 0.2, 32]} data-oid="pb32lm6" />
            <meshStandardMaterial color="#111111" data-oid="ni7s1ul" />
          </mesh>

          <mesh
            position={[-1.2, -0.6, -1]}
            rotation={[Math.PI / 2, 0, 0]}
            data-oid="26s-kvf"
          >
            <cylinderGeometry args={[0.4, 0.4, 0.2, 32]} data-oid="g-9l_9t" />
            <meshStandardMaterial color="#111111" data-oid="bea1i:d" />
          </mesh>

          {/* Windows */}
          <mesh
            position={[0.5, 0.75, 0]}
            rotation={[0, 0, Math.PI / 8]}
            data-oid="b_x7kny"
          >
            <boxGeometry args={[0.1, 0.5, 1.7]} data-oid=":hdeue." />
            <meshStandardMaterial
              color="#aaddff"
              metalness={0.9}
              roughness={0.1}
              data-oid="py-m3ns"
            />
          </mesh>

          {/* Headlights */}
          <mesh position={[2, 0, 0.6]} data-oid="6qdw671">
            <boxGeometry args={[0.1, 0.3, 0.3]} data-oid="xiekp7u" />
            <meshStandardMaterial
              color="#ffffff"
              emissive="#ffffff"
              emissiveIntensity={0.5}
              data-oid=".76ces3"
            />
          </mesh>

          <mesh position={[2, 0, -0.6]} data-oid=".:uu8qv">
            <boxGeometry args={[0.1, 0.3, 0.3]} data-oid="sxeo3mr" />
            <meshStandardMaterial
              color="#ffffff"
              emissive="#ffffff"
              emissiveIntensity={0.5}
              data-oid="59a.59."
            />
          </mesh>

          {/* Taillights */}
          <mesh position={[-2, 0, 0.6]} data-oid="b-:8gz:">
            <boxGeometry args={[0.1, 0.3, 0.3]} data-oid="dtd917o" />
            <meshStandardMaterial
              color="#ff0000"
              emissive="#ff0000"
              emissiveIntensity={0.5}
              data-oid="606:k:2"
            />
          </mesh>

          <mesh position={[-2, 0, -0.6]} data-oid="vkakhg3">
            <boxGeometry args={[0.1, 0.3, 0.3]} data-oid="rrgg9l6" />
            <meshStandardMaterial
              color="#ff0000"
              emissive="#ff0000"
              emissiveIntensity={0.5}
              data-oid="yvzes65"
            />
          </mesh>
        </group>
      </mesh>
    );
  };

  try {
    // Try to load the Mercedes S-class model with multiple possible paths
    let modelPath = "./Mercedes_S_class/scene.gltf";
    console.log("Attempting to load 3D model from:", modelPath);

    // Try to load the model, with multiple fallback options
    try {
      var { scene } = useGLTF(modelPath, undefined, (loader) => {
        const dracoLoader = new DRACOLoader();
        loader.setDRACOLoader(dracoLoader);
      });
    } catch (e) {
      // Try alternative file formats
      try {
        modelPath = "./Mercedes_S_class/scene.glb";
        console.log("Attempting to load 3D model from:", modelPath);
        var { scene } = useGLTF(modelPath, undefined, (loader) => {
          const dracoLoader = new DRACOLoader();
          loader.setDRACOLoader(dracoLoader);
        });
      } catch (e2) {
        // Try alternative file names
        try {
          modelPath = "./Mercedes_S_class/car.gltf";
          console.log("Attempting to load 3D model from:", modelPath);
          var { scene } = useGLTF(modelPath, undefined, (loader) => {
            const dracoLoader = new DRACOLoader();
            loader.setDRACOLoader(dracoLoader);
          });
        } catch (e3) {
          // Try alternative file names with glb extension
          try {
            modelPath = "./Mercedes_S_class/car.glb";
            console.log("Attempting to load 3D model from:", modelPath);
            var { scene } = useGLTF(modelPath, undefined, (loader) => {
              const dracoLoader = new DRACOLoader();
              loader.setDRACOLoader(dracoLoader);
            });
          } catch (e4) {
            // Try alternative file names with model extension
            try {
              modelPath = "./Mercedes_S_class/model.gltf";
              console.log("Attempting to load 3D model from:", modelPath);
              var { scene } = useGLTF(modelPath, undefined, (loader) => {
                const dracoLoader = new DRACOLoader();
                loader.setDRACOLoader(dracoLoader);
              });
            } catch (e5) {
              // Try one more path format
              try {
                modelPath = "./Mercedes_S_class/index.gltf";
                console.log("Attempting to load 3D model from:", modelPath);
                var { scene } = useGLTF(modelPath, undefined, (loader) => {
                  const dracoLoader = new DRACOLoader();
                  loader.setDRACOLoader(dracoLoader);
                });
              } catch (e6) {
                // Try with .obj extension
                try {
                  modelPath = "./Mercedes_S_class/scene.obj";
                  console.log("Attempting to load 3D model from:", modelPath);
                  var { scene } = useGLTF(modelPath, undefined, (loader) => {
                    const dracoLoader = new DRACOLoader();
                    loader.setDRACOLoader(dracoLoader);
                  });
                } catch (e7) {
                  // Final fallback to the original model
                  modelPath = "./desktop_pc/scene.gltf";
                  console.log("Falling back to original model:", modelPath);
                  var { scene } = useGLTF(modelPath, undefined, (loader) => {
                    const dracoLoader = new DRACOLoader();
                    loader.setDRACOLoader(dracoLoader);
                  });
                }
              }
            }
          }
        }
      }
    }

    console.log("Successfully loaded 3D model from:", modelPath);

    return (
      <mesh data-oid="szzsfp0">
        <hemisphereLight
          intensity={0.15}
          groundColor="black"
          data-oid="f2-.2m7"
        />

        <spotLight
          position={[-20, 50, 10]}
          angle={0.12}
          penumbra={1}
          intensity={1}
          castShadow
          shadow-mapSize={1024}
          data-oid="9rsqly."
        />

        <pointLight intensity={1} data-oid="yd7nrlw" />
        <primitive
          object={scene}
          scale={isMobile ? 0.4 : 0.5}
          position={isMobile ? [0, -2, -2.2] : [0, -2.25, -1.5]}
          rotation={[0, Math.PI / 4, 0]}
          data-oid="elldqr2"
        />
      </mesh>
    );
  } catch (error) {
    console.error("Error loading 3D model:", error);
    console.log("Using custom car model instead of 3D model");
    return createCarMesh();
  }
};

const MemoizedComputerModel = React.memo(ComputerModel);

// CSS-only fallback car component
const CarFallback = () => {
  return (
    <div className="car-fallback-container" data-oid="lkfnwip">
      <div className="car-fallback" data-oid="xxbru-k">
        <div className="car-body" data-oid="lp7dor:"></div>
        <div className="car-roof" data-oid="3he9sas"></div>
        <div className="car-window" data-oid="caqko8r"></div>
        <div className="car-wheel car-wheel-left" data-oid="t12ls31"></div>
        <div className="car-wheel car-wheel-right" data-oid="-l9j6m5"></div>
        <div className="car-light car-light-front" data-oid="_tpb-2d"></div>
        <div className="car-light car-light-back" data-oid="zco5e2s"></div>
      </div>
    </div>
  );
};

// Error boundary for Canvas component
class CanvasErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.warn("Three.js error caught:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }
    return this.props.children;
  }
}

const ComputersCanvas = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [webGLAvailable, setWebGLAvailable] = useState(true);
  const [errorCount, setErrorCount] = useState(0);

  useEffect(() => {
    // Check if WebGL is available
    try {
      const canvas = document.createElement("canvas");
      const gl =
        canvas.getContext("webgl") || canvas.getContext("experimental-webgl");
      setWebGLAvailable(!!gl);
    } catch (e) {
      console.warn("WebGL not available:", e);
      setWebGLAvailable(false);
    }

    // Check mobile device
    const mediaQuery = window.matchMedia("(max-width: 500px)");
    const handleMediaQueryChange = (event) => {
      setIsMobile(event.matches);
    };
    mediaQuery.addEventListener("change", handleMediaQueryChange);
    return () => {
      mediaQuery.removeEventListener("change", handleMediaQueryChange);
    };
  }, []);

  // If WebGL is not available, use the CSS fallback
  if (!webGLAvailable) {
    return <CarFallback data-oid="_dr-uyj" />;
  }

  // If WebGL is available, try to render the Three.js canvas
  return (
    <CanvasErrorBoundary
      fallback={<CarFallback data-oid="va2-:n4" />}
      data-oid="flimx9e"
    >
      <Canvas
        frameloop="demand"
        shadows
        dpr={[1, 2]}
        camera={{ position: [20, 3, 5], fov: 25 }}
        gl={{ preserveDrawingBuffer: true }}
        onError={(e) => {
          console.warn("Canvas error:", e);
          setErrorCount((prev) => prev + 1);
          if (errorCount > 2) {
            setWebGLAvailable(false);
          }
        }}
        data-oid="vpl4kfa"
      >
        <Suspense
          fallback={<CanvasLoader data-oid="dj07398" />}
          data-oid="0wn0jz7"
        >
          <OrbitControls
            enableZoom={false}
            maxPolarAngle={Math.PI / 2}
            minPolarAngle={Math.PI / 2}
            rotateSpeed={1.5}
            autoRotate={true}
            autoRotateSpeed={2.0}
            data-oid="7p_.boh"
          />

          <MemoizedComputerModel isMobile={isMobile} data-oid="_4p3t:." />
        </Suspense>
        <Preload all data-oid="rsv.51b" />
      </Canvas>
    </CanvasErrorBoundary>
  );
};

export default ComputersCanvas;
