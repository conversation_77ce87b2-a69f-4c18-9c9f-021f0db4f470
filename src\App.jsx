import React, { useEffect, Suspense, lazy } from "react";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Layout from "./components/Layout";
import HomePage from "./pages/HomePage";
import ErrorBoundary from "./components/ErrorBoundary";

// Add global style to ensure black background
const globalStyle = document.createElement("style");
globalStyle.innerHTML = `
  html, body, #root {
    background-color: #000000 !important;
    min-height: 100vh;
    overflow-x: hidden;
  }
`;
document.head.appendChild(globalStyle);

// Carpool pages
import MercedesSClass from "./pages/carpool/MercedesSClass";
import BMW7 from "./pages/carpool/BMW7";
import MercedesVClass from "./pages/carpool/MercedesVClass";
import CarPoolPage from "./pages/CarPoolPage";

// Services pages
import ChauffeurService from "./pages/services/ChauffeurService";
import AirportTransfer from "./pages/services/AirportTransfer";
import VipService from "./pages/services/VipService";

// Tourism pages
import PopularDestinations from "./pages/tourism/PopularDestinations";
import ShoppingTours from "./pages/tourism/ShoppingTours";
import Freizeitparks from "./pages/tourism/Freizeitparks";
import Bauernhofe from "./pages/tourism/Bauernhofe";
import TourismPage from "./pages/TourismPage";

// City pages
import {
  Munich,
  Hamburg,
  Frankfurt,
  Heidelberg,
  BlackForest,
  BadenBaden,
} from "./pages/tourism/cities";
import MunichBlog from "./pages/tourism/cities/MunichBlog";

// Other pages
import ContactPage from "./pages/ContactPage";
import PrivacyPolicy from "./pages/PrivacyPolicy";

// Main section components
import { ServicesPage } from "./components";

// Loading component for Suspense
const Loading = () => (
  <div
    style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100vh",
      backgroundColor: "#000",
      color: "#d4af37",
    }}
    data-oid="mq:h__s"
  >
    <div data-oid="oo:70ob">
      <h2 data-oid="5yngg6u">Loading...</h2>
      <div
        style={{
          width: "50px",
          height: "50px",
          border: "5px solid rgba(212, 175, 55, 0.3)",
          borderRadius: "50%",
          borderTop: "5px solid #d4af37",
          animation: "spin 1s linear infinite",
          margin: "20px auto",
        }}
        data-oid="wg.ls10"
      ></div>
      <style data-oid="tus8y53">{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  </div>
);

const App = () => {
  return (
    <ErrorBoundary data-oid="n6-439d">
      <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
        data-oid="vo6izjj"
      >
        <Routes data-oid="haor5pr">
          <Route
            path="/"
            element={<Layout data-oid="_x9fk7b" />}
            data-oid="97qia8u"
          >
            <Route
              index
              element={
                <ErrorBoundary data-oid="d71vdjl">
                  <Suspense
                    fallback={<Loading data-oid="yd5la8_" />}
                    data-oid="iicom40"
                  >
                    <HomePage data-oid="xik-65z" />
                  </Suspense>
                </ErrorBoundary>
              }
              data-oid="rm-jh:s"
            />

            {/* Carpool routes */}
            <Route path="carpool" data-oid="m_epfvw">
              <Route
                index
                element={<CarPoolPage data-oid="rcy-no:" />}
                data-oid="kpmnu13"
              />

              <Route
                path="mercedes-sclass"
                element={<MercedesSClass data-oid="jrprjan" />}
                data-oid="fky.grt"
              />

              <Route
                path="bmw-7"
                element={<BMW7 data-oid="avdz_wt" />}
                data-oid="ixlywnx"
              />

              <Route
                path="mercedes-vclass"
                element={<MercedesVClass data-oid="nyg975o" />}
                data-oid="l_daxd9"
              />
            </Route>

            {/* Services routes */}
            <Route path="services" data-oid="dc0d.e1">
              <Route
                index
                element={<ServicesPage data-oid="rj6z7mu" />}
                data-oid="yp4nk8n"
              />

              <Route
                path="chauffeurservice"
                element={<ChauffeurService data-oid="fu89ekh" />}
                data-oid="sjyd4ke"
              />

              <Route
                path="airporttransfer"
                element={<AirportTransfer data-oid="gsi9n.f" />}
                data-oid=":g_8o8t"
              />

              <Route
                path="vip-service"
                element={<VipService data-oid="9s8xqyw" />}
                data-oid=".valoen"
              />
            </Route>

            {/* Tourism routes */}
            <Route path="tourism" data-oid="by348z_">
              <Route
                index
                element={<TourismPage data-oid="7t48l4t" />}
                data-oid="y7r-.6r"
              />

              <Route
                path="beliebte-zielorte"
                element={<PopularDestinations data-oid="lmozsk:" />}
                data-oid=":bywg66"
              />

              <Route
                path="shoppingtours"
                element={<ShoppingTours data-oid="rzjqg6q" />}
                data-oid="mwkxuv5"
              />

              <Route
                path="freizeitparks"
                element={<Freizeitparks data-oid="_qvo3oi" />}
                data-oid="jpke336"
              />

              <Route
                path="bauernhofe"
                element={<Bauernhofe data-oid="0sww87c" />}
                data-oid="l4q1t9s"
              />

              {/* City routes */}
              <Route path="cities" data-oid="k4ol8sl">
                <Route
                  path="munich"
                  element={<Munich data-oid="yg0_:q8" />}
                  data-oid="l6w0udt"
                />

                <Route
                  path="munich-blog"
                  element={<MunichBlog data-oid="gz-tzkx" />}
                  data-oid="w6kcc6m"
                />

                <Route
                  path="hamburg"
                  element={<Hamburg data-oid="fnxcsav" />}
                  data-oid="9of0vbv"
                />

                <Route
                  path="frankfurt"
                  element={<Frankfurt data-oid="bmdfxim" />}
                  data-oid="kshpbz:"
                />

                <Route
                  path="heidelberg"
                  element={<Heidelberg data-oid="mfrf5.x" />}
                  data-oid="pl-y0rf"
                />

                <Route
                  path="black-forest"
                  element={<BlackForest data-oid="9xqmwf2" />}
                  data-oid="6520ve_"
                />

                <Route
                  path="baden-baden"
                  element={<BadenBaden data-oid="x8fzy7t" />}
                  data-oid="3-e2qdq"
                />
              </Route>
            </Route>

            {/* Alternative Tourism route for compatibility */}
            <Route
              path="tourism-section"
              element={<Navigate to="/tourism" data-oid="2g2.3c3" />}
              data-oid=":um_.71"
            />

            {/* Contact route */}
            <Route
              path="contact"
              element={<ContactPage data-oid="myg4yzb" />}
              data-oid=".1tojiu"
            />

            {/* Legal routes */}
            <Route
              path="privacy-policy"
              element={<PrivacyPolicy data-oid="umrc7ym" />}
              data-oid="lpalxor"
            />

            {/* Fallback route */}
            <Route
              path="*"
              element={<Navigate to="/" data-oid="16uigi4" />}
              data-oid="hpzpoy6"
            />
          </Route>
        </Routes>
      </BrowserRouter>
    </ErrorBoundary>
  );
};

export default App;
