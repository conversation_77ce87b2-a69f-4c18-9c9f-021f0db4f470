import { Box, Text } from "@chakra-ui/react";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";

import CodeExample from "../../components/code/CodeExample";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import CliInstallation from "../../components/code/CliInstallation";

import DecayCard from "../../content/Components/DecayCard/DecayCard";
import { decayCard } from "../../constants/code/Components/decayCardCode";

const DecayCardDemo = () => {
  const propData = [
    {
      name: "children",
      type: "ReactNode",
      default: "",
      description: "The content (JSX) to be rendered inside the card.",
    },
    {
      name: "width",
      type: "number",
      default: "200",
      description: "The width of the card in pixels.",
    },
    {
      name: "height",
      type: "number",
      default: "300",
      description: "The height of the card in pixels.",
    },
    {
      name: "image",
      type: "string",
      default: "",
      description: "Allows setting the background image of the card.",
    },
  ];

  return (
    <TabbedLayout data-oid="t3vk053">
      <PreviewTab data-oid="47odun6">
        <Box
          position="relative"
          className="demo-container"
          overflow="hidden"
          data-oid="06w2cl1"
        >
          <DecayCard data-oid="2pqob-c">
            <Text mixBlendMode="overlay" data-oid="wlhsf-_">
              Decay
              <br data-oid="b7orojl" />
              Card
            </Text>
          </DecayCard>
        </Box>

        <PropTable data={propData} data-oid="ac-d6uz" />
        <Dependencies dependencyList={["gsap"]} data-oid="y-4:0z8" />
      </PreviewTab>

      <CodeTab data-oid="rtm:q05">
        <CodeExample codeObject={decayCard} data-oid="g78yfpl" />
      </CodeTab>

      <CliTab data-oid="tw89e__">
        <CliInstallation {...decayCard} data-oid="nifzx._" />
      </CliTab>
    </TabbedLayout>
  );
};

export default DecayCardDemo;
