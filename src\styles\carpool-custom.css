/* Custom styles for the carpool section */

/* Radial gradient background */
.bg-radial-gradient {
  background: radial-gradient(circle at center, rgba(212, 175, 55, 0.08) 0%, transparent 70%);
  animation: pulse 4s infinite;
}

@keyframes pulse {
  0% { opacity: 0.5; }
  50% { opacity: 0.8; }
  100% { opacity: 0.5; }
}

/* Feature tags styling */
.feature-tag {
  background: rgba(212, 175, 55, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-tag:hover {
  background: rgba(212, 175, 55, 0.2);
  border-color: rgba(212, 175, 55, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 5px rgba(212, 175, 55, 0.3);
}

.feature-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.2), transparent);
  transition: all 0.5s ease;
  z-index: -1;
}

.feature-tag:hover::before {
  left: 100%;
}

/* Carpool frame styling */
.carpool-frame {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 0 20px rgba(212, 175, 55, 0.1);
  perspective: 1500px;
  transform-style: preserve-3d;
  transition: transform 0.5s ease;
}

.carpool-frame::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #D4AF37, transparent);
}

/* Parallax decorations */
.parallax-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
}

.decoration {
  position: absolute;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.05) 0%, rgba(212, 175, 55, 0.01) 100%);
  border: 1px solid rgba(212, 175, 55, 0.1);
  border-radius: 50%;
  transform-origin: center center;
  animation: float 15s infinite ease-in-out;
}

.decoration:nth-child(1) {
  animation-delay: 0s;
}

.decoration:nth-child(2) {
  animation-delay: -5s;
}

.decoration:nth-child(3) {
  animation-delay: -10s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) rotate(2deg);
  }
  50% {
    transform: translateY(0) rotate(0deg);
  }
  75% {
    transform: translateY(15px) rotate(-2deg);
  }
}
