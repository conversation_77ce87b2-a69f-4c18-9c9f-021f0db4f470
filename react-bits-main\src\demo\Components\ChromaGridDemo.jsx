import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Box } from "@chakra-ui/react";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";

import { chromaGrid } from "../../constants/code/Components/chromaGridCode";
import ChromaGrid from "../../content/Components/ChromaGrid/ChromaGrid";

const ChromaGridDemo = () => {
  const propData = [
    {
      name: "items",
      type: "Array",
      default: "Demo []",
      description: "Array of ChromaItem objects to display in the grid",
    },
    {
      name: "className",
      type: "string",
      default: "''",
      description: "Additional CSS classes to apply to the grid container",
    },
    {
      name: "radius",
      type: "number",
      default: "300",
      description: "Size of the spotlight effect in pixels",
    },
    {
      name: "damping",
      type: "number",
      default: "0.45",
      description: "Cursor follow animation duration in seconds",
    },
    {
      name: "fadeOut",
      type: "number",
      default: "0.6",
      description: "Fade-out animation duration in seconds when mouse leaves",
    },
    {
      name: "ease",
      type: "string",
      default: "'power3.out'",
      description: "GSAP easing function for animations",
    },
  ];

  return (
    <TabbedLayout data-oid="9qh7m32">
      <PreviewTab data-oid="sncy7zh">
        <Box
          position="relative"
          className="demo-container"
          h="auto"
          overflow="hidden"
          p={0}
          data-oid="ulxa31q"
        >
          <ChromaGrid data-oid="2tysbk5" />
        </Box>

        <PropTable data={propData} data-oid="h9bmc8z" />
        <Dependencies dependencyList={["gsap"]} data-oid="oz.bs55" />
      </PreviewTab>

      <CodeTab data-oid="uetorxf">
        <CodeExample codeObject={chromaGrid} data-oid="h8wcgyl" />
      </CodeTab>

      <CliTab data-oid="35dicav">
        <CliInstallation {...chromaGrid} data-oid="z6yez-9" />
      </CliTab>
    </TabbedLayout>
  );
};

export default ChromaGridDemo;
