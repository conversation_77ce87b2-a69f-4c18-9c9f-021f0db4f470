import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Box, FormControl, FormLabel, Input } from "@chakra-ui/react"; // Added Input from Chakra UI

import Customize from "../../components/common/Customize";
import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import PreviewSlider from "../../components/common/PreviewSlider";

import { scrambledTextCode } from "../../constants/code/TextAnimations/scrambledTextCode";
import ScrambledText from "../../content/TextAnimations/ScrambledText/ScrambledText";

const ScrambledTextDemo = () => {
  const [radius, setRadius] = useState(100);
  const [duration, setDuration] = useState(1.2);
  const [speed, setSpeed] = useState(0.5);
  const [scrambleChars, setScrambleChars] = useState(".:");

  const propData = [
    {
      name: "radius",
      type: "number",
      default: "100",
      description:
        "The radius around the mouse pointer within which characters will scramble.",
    },
    {
      name: "duration",
      type: "number",
      default: "1.2",
      description: "The duration of the scramble effect on a character.",
    },
    {
      name: "speed",
      type: "number",
      default: "0.5",
      description: "The speed of the scramble animation.",
    },
    {
      name: "scrambleChars",
      type: "string",
      default: "'.:'",
      description: "The characters used for scrambling.",
    },
    {
      name: "children",
      type: "React.ReactNode",
      default: "",
      description: "The text content to be scrambled.",
    },
    {
      name: "className",
      type: "string",
      default: '""',
      description: "Additional CSS classes for the component.",
    },
    {
      name: "style",
      type: "React.CSSProperties",
      default: "{}",
      description: "Inline styles for the component.",
    },
  ];

  return (
    <TabbedLayout data-oid="cbxibi.">
      <PreviewTab data-oid="jiv.s.m">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          overflow="hidden"
          data-oid="85fnpzb"
        >
          <ScrambledText
            className="scrambled-text-demo"
            radius={radius}
            duration={duration}
            speed={speed}
            scrambleChars={scrambleChars}
            data-oid="4ccgcrz"
          >
            Once you hover over me, you will see the effect in action! You can
            customize the radius, duration, and speed of the scramble effect.
          </ScrambledText>
        </Box>

        <Customize data-oid="ubyfoqw">
          <FormControl w="200px" data-oid="9dwcd5k">
            <FormLabel fontSize="sm" data-oid="748.onb">
              Scramble Characters
            </FormLabel>
            <Input
              value={scrambleChars}
              onChange={(e) => {
                setScrambleChars(e.target.value);
              }}
              placeholder=".: (default)"
              data-oid="2l7ivuu"
            />
          </FormControl>

          <PreviewSlider
            title="Radius"
            min={10}
            max={300}
            step={10}
            value={radius}
            onChange={(val) => setRadius(val)}
            data-oid="b3sh::4"
          />

          <PreviewSlider
            title="Duration"
            min={0.1}
            max={5}
            step={0.1}
            value={duration}
            onChange={(val) => setDuration(val)}
            data-oid="p488dvh"
          />

          <PreviewSlider
            title="Speed"
            min={0.1}
            max={2}
            step={0.1}
            value={speed}
            onChange={(val) => setSpeed(val)}
            data-oid="xa_z-v8"
          />
        </Customize>

        <PropTable data={propData} data-oid="8oas63." />
        <Dependencies dependencyList={["gsap"]} data-oid="6vwhkma" />
      </PreviewTab>

      <CodeTab data-oid="902d19e">
        <CodeExample codeObject={scrambledTextCode} data-oid="eswo6a." />
      </CodeTab>

      <CliTab data-oid="qh5ab2f">
        <CliInstallation {...scrambledTextCode} data-oid="ted58-j" />
      </CliTab>
    </TabbedLayout>
  );
};

export default ScrambledTextDemo;
