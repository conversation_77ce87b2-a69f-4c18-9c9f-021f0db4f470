import { useState } from "react";
import { InfoOutlineIcon } from "@chakra-ui/icons";
import { Box, Flex, Input, Text } from "@chakra-ui/react";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";

import CodeExample from "../../components/code/CodeExample";
import PropTable from "../../components/common/PropTable";
import CliInstallation from "../../components/code/CliInstallation";
import PreviewSlider from "../../components/common/PreviewSlider";
import Customize from "../../components/common/Customize";

import GradientText from "../../content/TextAnimations/GradientText/GradientText";
import { gradientText } from "../../constants/code/TextAnimations/gradientTextCode";

const GradientTextDemo = () => {
  const [colors, setColors] = useState(
    "#40ffaa, #4079ff, #40ffaa, #4079ff, #40ffaa",
  );
  const [speed, setSpeed] = useState(3);

  const gradientPreview = colors.split(",").map((color) => color.trim());

  const propData = [
    {
      name: "children",
      type: "ReactNode",
      default: "-",
      description: "The content to be displayed inside the gradient text.",
    },
    {
      name: "className",
      type: "string",
      default: "''",
      description:
        "Adds custom classes to the root element for additional styling.",
    },
    {
      name: "colors",
      type: "string[]",
      default: `["#40ffaa", "#4079ff", "#40ffaa", "#4079ff", "#40ffaa"]`,
      description: "Defines the gradient colors for the text or border.",
    },
    {
      name: "animationSpeed",
      type: "number",
      default: "8",
      description: "The duration of the gradient animation in seconds.",
    },
    {
      name: "showBorder",
      type: "boolean",
      default: "false",
      description:
        "Determines whether a border with the gradient effect is displayed.",
    },
  ];

  return (
    <TabbedLayout data-oid="6j5gae.">
      <PreviewTab data-oid="90lti-9">
        <h2 className="demo-title-extra" data-oid="n0obkk3">
          Default
        </h2>
        <Box
          position="relative"
          className="demo-container"
          minH={150}
          data-oid="vxr8nc0"
        >
          <Text fontSize={"2rem"} as="div" data-oid="yvoklbn">
            <GradientText
              colors={colors.split(",")}
              animationSpeed={speed}
              showBorder={false}
              data-oid="kts.2qo"
            >
              Add a splash of color!
            </GradientText>
          </Text>
        </Box>

        <h2 className="demo-title-extra" data-oid="chxg14e">
          Border Animation
        </h2>
        <Box
          position="relative"
          className="demo-container"
          minH={150}
          data-oid="584d08-"
        >
          <Text fontSize={"2rem"} as="div" data-oid="rj6-38:">
            <GradientText
              colors={colors.split(",")}
              animationSpeed={speed}
              className="custom-gradient-class"
              data-oid=":g3g.o0"
            >
              Now with a cool border!
            </GradientText>
          </Text>
        </Box>

        <Customize data-oid="g80--o3">
          <h2 className="demo-title-extra" data-oid="qgnj4i_">
            Customize
          </h2>
          <Flex
            gap={6}
            wrap="wrap"
            alignItems="flex-start"
            mt={4}
            direction="column"
            data-oid="6k48mg9"
          >
            {/* Colors Input */}
            <Flex gap={4} align="center" data-oid="1e4d::s">
              <Text fontSize="sm" data-oid="19jx8lc">
                Gradient
              </Text>
              <Input
                fontSize="xs"
                type="text"
                w="280px"
                h={8}
                px={2}
                onChange={(e) => setColors(e.target.value)}
                value={colors}
                data-oid="-0f_aq-"
              />

              <Box
                bg={`linear-gradient(to right, ${gradientPreview.join(", ")})`}
                w="100px"
                h="28px"
                borderRadius="md"
                border="1px solid #ddd"
                data-oid="q2r6b2q"
              />
            </Flex>

            <PreviewSlider
              title="Speed (s)"
              min={1}
              max={10}
              step={0.5}
              value={speed}
              onChange={setSpeed}
              valueUnit="s"
              data-oid=":.5b-ge"
            />
          </Flex>
        </Customize>

        <p
          className="demo-extra-info"
          style={{ marginTop: "1rem" }}
          data-oid="by7lf51"
        >
          <InfoOutlineIcon position="relative" data-oid="d6vouqg" /> For a
          smoother animation, the gradient should start and end with the same
          color.
        </p>

        <PropTable data={propData} data-oid="fa3maan" />
      </PreviewTab>

      <CodeTab data-oid="90:yas_">
        <CodeExample codeObject={gradientText} data-oid="de:s34a" />
      </CodeTab>

      <CliTab data-oid="t-gc30d">
        <CliInstallation {...gradientText} data-oid="zzsoxs5" />
      </CliTab>
    </TabbedLayout>
  );
};

export default GradientTextDemo;
