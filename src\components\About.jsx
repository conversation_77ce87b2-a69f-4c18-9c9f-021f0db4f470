import { motion } from "framer-motion";
import React from "react";
import { Link } from "react-router-dom";
import { services } from "../constants";
import { SectionWrapper } from "../hoc";
import { styles } from "../styles";
import { fadeIn, textVariant } from "../utils/motion";

import { useTranslation } from "react-i18next";
import { useLanguage } from "../contexts/LanguageContext";

// Import custom styles for carpool section
import "../styles/carpool-custom.css";

const ServiceCard = ({ index, title, icon, id }) => {
  const { t } = useTranslation();
  const { language } = useLanguage();

  return (
    <motion.div
      variants={fadeIn("up", "spring", index * 0.2, 0.5)}
      whileHover={{
        y: -8,
        transition: { duration: 0.3 },
      }}
      className="w-full mx-auto"
      data-oid="1_jdjbk"
    >
      <div className="card" data-oid="xt_47j2">
        <Link to={`/carpool/${id}`} className="card-link" data-oid="c77:_78">
          <div className="card-image" data-oid="nj0reu_">
            <motion.img
              src={icon}
              alt={t(`carpool.${id}`)}
              className="w-full h-full object-cover hover:scale-110 transition-transform duration-700"
              loading="eager"
              data-oid="23mnt.k"
            />
          </div>
          <div className="card-content" data-oid="b_wy.h.">
            <h3 className="card-title" data-oid="yf_3g9:">
              {t(`carpool.${id}`)}
            </h3>
            <p className="card-description text-center" data-oid="bszi-tk">
              {language === "ar"
                ? t("carpool.viewDetails")
                : "Click to view details"}
            </p>
          </div>
        </Link>
      </div>
    </motion.div>
  );
};

const CarpoolContent = () => {
  const { t } = useTranslation();
  const { language } = useLanguage();

  return (
    <div className="section-container" data-oid="qu875rc">
      <div className="section-header" data-oid="wtgb288">
        <motion.div variants={textVariant()} data-oid="7sv0o.g">
          <p className="section-subtitle" data-oid=".ilr6t_">
            {language === "ar" ? "أسطول سيارات فاخر" : "LUXURY VEHICLE FLEET"}
          </p>
          <h2 className="section-title" data-oid="0z6:eux">
            {language === "ar" ? "سيارات فاخرة" : "PREMIUM VEHICLES"}
          </h2>
          <div className="section-title-underline" data-oid="l2v8rbi"></div>
        </motion.div>

        <p className="section-description" data-oid="c6-qnu5">
          {language === "ar"
            ? "خدمة سائق فاخرة مع سيارات مرسيدس الفئة S وبي إم دبليو الفئة 7 ومرسيدس الفئة V. نقدم خدمات نقل رجال الأعمال، وخدمة المطار، وخدمة كبار الشخصيات في جميع أنحاء أوروبا. سائق يتحدث العربية متاح على مدار الساعة لتلبية احتياجات النقل الحصرية الخاصة بك."
            : "Erstklassiger Chauffeurservice mit exklusiven Transportlösungen für anspruchsvolle Kunden. Business, Executive und VIP Service mit höchstem Komfort und Diskretion für Geschäftstermine, Flughafentransfers und besondere Anlässe."}
        </p>
      </div>

      <div className="card-grid" data-oid="7-_dxs7">
        {services.map((service, index) => (
          <ServiceCard
            key={service.title}
            index={index}
            {...service}
            data-oid="_o.kt40"
          />
        ))}
      </div>
    </div>
  );
};

const Carpool = () => {
  return (
    <div className="relative z-0" data-oid="pz70yh2">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid="hvzibhq"
      >
        <CarpoolContent data-oid=".78oza-" />
      </div>
    </div>
  );
};

const WrappedCarpool = SectionWrapper(CarpoolContent, "carpool");

// Export both the wrapped version (for the home page) and the standalone version (for the route)
export { Carpool };
export default WrappedCarpool;
