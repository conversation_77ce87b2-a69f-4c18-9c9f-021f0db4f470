import { <PERSON>, Flex, Link } from "@chakra-ui/react";
import FadeContent from "../../content/Animations/FadeContent/FadeContent";
import { FiHeart } from "react-icons/fi";

const LandingFooter = () => (
  <Box mb="8em" mt="4em" data-oid="1m2vba8">
    <FadeContent blur data-oid="4yk8_:a">
      <Flex alignItems="center" data-oid="08tkdj9">
        Made with{" "}
        <Box mx={2} data-oid="nm3ps4f">
          <FiHeart data-oid="z5bwkw0" />
        </Box>{" "}
        by
        <Link
          ml={1}
          href="https://davidhaz.com/"
          target="_blank"
          color="#999"
          data-oid="47-pw-3"
        >
          this guy
        </Link>
      </Flex>
    </FadeContent>
  </Box>
);

export default LandingFooter;
