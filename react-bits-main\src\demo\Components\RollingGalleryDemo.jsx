import { Box, Flex, Text } from "@chakra-ui/react";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";

import CodeExample from "../../components/code/CodeExample";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import CliInstallation from "../../components/code/CliInstallation";

import RollingGallery from "../../content/Components/RollingGallery/RollingGallery";
import { rollingGallery } from "../../constants/code/Components/rollingGalleryCode";

const RollingGalleryDemo = () => {
  const propData = [
    {
      name: "autoplay",
      type: "boolean",
      default: "false",
      description:
        "Controls the autoplay toggle of the carousel. When turned on, it rotates and loops infinitely.",
    },
    {
      name: "pauseOnHover",
      type: "boolean",
      default: "false",
      description:
        "Allows the carousel to be paused on hover when autoplay is turned on.",
    },
  ];

  return (
    <TabbedLayout data-oid="e5w1a18">
      <PreviewTab data-oid="cacc.t8">
        <Box
          position="relative"
          className="demo-container"
          bg={"#060606"}
          overflow="hidden"
          p={0}
          data-oid="hecst36"
        >
          <Flex
            h={"100%"}
            maxW={"600px"}
            alignItems="center"
            justifyContent="center"
            direction="column"
            data-oid="08-3ysn"
          >
            <Text
              textAlign="center"
              position="absolute"
              fontWeight={900}
              top={{ base: "4em", md: "1em" }}
              whiteSpace="nowrap"
              fontSize={{ base: "1.6em", md: "3rem" }}
              data-oid="jnpw893"
            >
              Your trip to Thailand.
            </Text>
            <RollingGallery
              autoplay={true}
              pauseOnHover={true}
              data-oid=":ix_ai1"
            />
          </Flex>
        </Box>

        <PropTable data={propData} data-oid="j4pi2vz" />
        <Dependencies dependencyList={["framer-motion"]} data-oid="ildrfvu" />
      </PreviewTab>

      <CodeTab data-oid="xwvdw0.">
        <CodeExample codeObject={rollingGallery} data-oid="aytm-uh" />
      </CodeTab>

      <CliTab data-oid="41.m2at">
        <CliInstallation {...rollingGallery} data-oid="swak7ms" />
      </CliTab>
    </TabbedLayout>
  );
};

export default RollingGalleryDemo;
