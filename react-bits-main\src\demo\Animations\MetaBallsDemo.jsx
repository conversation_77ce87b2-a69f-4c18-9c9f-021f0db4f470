import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import {
  Box,
  Flex,
  Input,
  Slider,
  SliderFilledTrack,
  SliderThumb,
  SliderTrack,
  Switch,
  Text,
} from "@chakra-ui/react";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";

import { metaBalls } from "../../constants/code/Animations/metaBallsCode";
import MetaBalls from "../../content/Animations/MetaBalls/MetaBalls";
import { useState } from "react";

const MetaBallsDemo = () => {
  const [color, setColor] = useState("#ffffff");
  const [cursorBallColor, setCursorBallColor] = useState("#ffffff");
  const [speed, setSpeed] = useState(0.3);
  const [animationSize, setAnimationSize] = useState(30);
  const [ballCount, setBallCount] = useState(15);
  const [clumpFactor, setClumpFactor] = useState(1);

  const [enableMouseInteraction, setEnableMouseInteraction] = useState(true);
  const [hoverSmoothness, setHoverSmoothness] = useState(0.15);
  const [cursorBallSize, setCursorBallSize] = useState(2);

  const propData = [
    {
      name: "color",
      type: "string",
      default: "#ffffff",
      description: "The base color of the metaballs.",
    },
    {
      name: "speed",
      type: "number",
      default: "0.3",
      description: "Speed multiplier for the animation.",
    },
    {
      name: "enableMouseInteraction",
      type: "boolean",
      default: "true",
      description: "Enables or disables the ball following the mouse.",
    },
    {
      name: "enableTransparency",
      type: "boolean",
      default: "false",
      description:
        "Enables or disables transparency for the container of the animation.",
    },
    {
      name: "hoverSmoothness",
      type: "number",
      default: "0.05",
      description:
        "Smoothness factor for the cursor ball when following the mouse.",
    },
    {
      name: "animationSize",
      type: "number",
      default: "30",
      description: "The size of the world for the animation.",
    },
    {
      name: "ballCount",
      type: "number",
      default: "15",
      description: "Number of metaballs rendered.",
    },
    {
      name: "clumpFactor",
      type: "number",
      default: "1",
      description: "Determines how close together the balls are rendered.",
    },
    {
      name: "cursorBallSize",
      type: "number",
      default: "3",
      description: "Size of the cursor-controlled ball.",
    },
    {
      name: "cursorBallColor",
      type: "string",
      default: "#ff0000",
      description: "Color of the cursor ball.",
    },
  ];

  return (
    <TabbedLayout data-oid="go43euj">
      <PreviewTab data-oid="p4kcqrp">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          p={0}
          overflow="hidden"
          data-oid="lqf5e30"
        >
          <MetaBalls
            color={color}
            cursorBallColor={cursorBallColor}
            cursorBallSize={cursorBallSize}
            ballCount={ballCount}
            animationSize={animationSize}
            enableMouseInteraction={enableMouseInteraction}
            hoverSmoothness={hoverSmoothness}
            clumpFactor={clumpFactor}
            speed={speed}
            data-oid="feyxke6"
          />
        </Box>

        <div className="preview-options" data-oid="13.-sw8">
          <h2 className="demo-title-extra" data-oid="pjzhz.l">
            Customize
          </h2>

          <Flex gap={4} align="center" mt={4} data-oid="s44m-l5">
            <Text fontSize="sm" data-oid="gn_6p2v">
              Color
            </Text>
            <Input
              type="color"
              value={color}
              onChange={(e) => {
                setColor(e.target.value);
                setCursorBallColor(e.target.value);
              }}
              width="50px"
              data-oid="0_sci_5"
            />

            <Text fontSize="sm" data-oid=":d2jgod">
              {color}
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid=":kkm:1l">
            <Text fontSize="sm" data-oid="-12zg3d">
              Ball Count
            </Text>
            <Slider
              min={2}
              max={30}
              step={1}
              value={ballCount}
              onChange={(val) => {
                setBallCount(val);
              }}
              width="150px"
              data-oid="-da5u8_"
            >
              <SliderTrack data-oid="6v415ix">
                <SliderFilledTrack data-oid="nl128r9" />
              </SliderTrack>
              <SliderThumb data-oid="wki.y44" />
            </Slider>
            <Text fontSize="sm" data-oid="zyoliup">
              {ballCount}
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="ye0oed:">
            <Text fontSize="sm" data-oid="a0rwqbl">
              Speed
            </Text>
            <Slider
              min={0.1}
              max={1}
              step={0.1}
              value={speed}
              onChange={(val) => {
                setSpeed(val);
              }}
              width="150px"
              data-oid="hqr.n5i"
            >
              <SliderTrack data-oid="1bn8s:7">
                <SliderFilledTrack data-oid="4-ojkks" />
              </SliderTrack>
              <SliderThumb data-oid="654td.n" />
            </Slider>
            <Text fontSize="sm" data-oid=".nkhhlq">
              {speed}
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="6--3_jk">
            <Text fontSize="sm" data-oid="sdsqyur">
              Size
            </Text>
            <Slider
              min={10}
              max={50}
              step={1}
              value={animationSize}
              onChange={(val) => {
                setAnimationSize(val);
              }}
              width="150px"
              data-oid="w2_-r1m"
            >
              <SliderTrack data-oid="1afor0r">
                <SliderFilledTrack data-oid="s9ermqq" />
              </SliderTrack>
              <SliderThumb data-oid="qs2m4v7" />
            </Slider>
            <Text fontSize="sm" data-oid=":c1:sv:">
              {animationSize}
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="kc3cqod">
            <Text fontSize="sm" data-oid="tgy:_7c">
              Clump Factor
            </Text>
            <Slider
              min={0.1}
              max={2}
              step={0.1}
              value={clumpFactor}
              onChange={(val) => {
                setClumpFactor(val);
              }}
              width="150px"
              data-oid="8sfp9e0"
            >
              <SliderTrack data-oid="8bwflt.">
                <SliderFilledTrack data-oid="n8deoub" />
              </SliderTrack>
              <SliderThumb data-oid="i0faii1" />
            </Slider>
            <Text fontSize="sm" data-oid="0gdjsaj">
              {clumpFactor}
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="yimv27a">
            <Text fontSize="sm" data-oid="rgd9nl5">
              Follow Cursor
            </Text>
            <Switch
              isChecked={enableMouseInteraction}
              onChange={(e) => {
                setEnableMouseInteraction(e.target.checked);
              }}
              data-oid="zcrn2zq"
            />
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="4brog4i">
            <Text fontSize="sm" data-oid="traj6gj">
              Cursor Smoothing
            </Text>
            <Slider
              min={0.001}
              max={0.25}
              step={0.001}
              value={hoverSmoothness}
              onChange={(val) => {
                setHoverSmoothness(val);
              }}
              width="150px"
              data-oid="zv..yla"
            >
              <SliderTrack data-oid="t47.fp3">
                <SliderFilledTrack data-oid="_savwy0" />
              </SliderTrack>
              <SliderThumb data-oid="av7x:0m" />
            </Slider>
            <Text fontSize="sm" data-oid="iw4mo80">
              {hoverSmoothness}
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="m4v.o.5">
            <Text fontSize="sm" data-oid="ow_neg9">
              Cursor Size
            </Text>
            <Slider
              min={1}
              max={5}
              step={1}
              value={cursorBallSize}
              onChange={(val) => {
                setCursorBallSize(val);
              }}
              width="150px"
              data-oid="tponbth"
            >
              <SliderTrack data-oid="vkk:s61">
                <SliderFilledTrack data-oid="17g_7xl" />
              </SliderTrack>
              <SliderThumb data-oid="w31yfh-" />
            </Slider>
            <Text fontSize="sm" data-oid="pznfcu0">
              {cursorBallSize}
            </Text>
          </Flex>
        </div>

        <PropTable data={propData} data-oid="kv3k6mg" />
        <Dependencies dependencyList={["ogl"]} data-oid="pkpvl3t" />
      </PreviewTab>

      <CodeTab data-oid="8knwyh:">
        <CodeExample codeObject={metaBalls} data-oid="d4y6ap8" />
      </CodeTab>

      <CliTab data-oid=".ss9xj7">
        <CliInstallation {...metaBalls} data-oid="031jrlx" />
      </CliTab>
    </TabbedLayout>
  );
};

export default MetaBallsDemo;
