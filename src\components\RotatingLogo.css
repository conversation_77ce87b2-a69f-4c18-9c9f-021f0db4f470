.rotating-logo-container {
  perspective: 1000px;
  width: 60px;
  height: 60px;
  position: relative;
  transform-style: preserve-3d;
  background-color: transparent;
}

.rotating-logo {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s;
  animation: rotate3d 8s infinite linear;
  background-color: transparent;
}

@keyframes rotate3d {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(180deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

.logo-front,
.logo-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.logo-back {
  transform: rotateY(180deg);
}

.logo-front img,
.logo-back img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: transparent;
}
