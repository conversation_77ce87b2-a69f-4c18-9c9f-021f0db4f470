/* Black and Gold Background Styles */

/* Main background with subtle gold patterns */
.black-gold-bg {
  position: relative;
  background-color: #000;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(212, 175, 55, 0.03) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(212, 175, 55, 0.03) 0%, transparent 20%),
    linear-gradient(45deg, rgba(212, 175, 55, 0.02) 25%, transparent 25%, transparent 50%, rgba(212, 175, 55, 0.02) 50%, rgba(212, 175, 55, 0.02) 75%, transparent 75%, transparent);
  background-size: 600px 600px, 600px 600px, 100px 100px;
}

/* Gold pattern overlay */
.black-gold-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
  pointer-events: none;
  z-index: 0;
}



/* Gold border with glow effect */
.gold-border {
  border: 2px solid #D4AF37;
  box-shadow: 0 0 15px rgba(212, 175, 55, 0.2);
  transition: all 0.3s ease;
}

.gold-border:hover {
  box-shadow: 0 0 20px rgba(212, 175, 55, 0.4);
}

/* Gold text with glow effect */
.gold-text {
  color: #D4AF37;
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
}

/* Gold underline effect */
.gold-underline {
  position: relative;
}

.gold-underline::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #D4AF37, transparent);
}

/* Gold gradient background */
.gold-gradient-bg {
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
  position: relative;
}

.gold-gradient-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

/* Gold accent line */
.gold-accent-line {
  height: 1px;
  background: linear-gradient(90deg, transparent, #D4AF37, transparent);
  margin: 20px auto;
  width: 80%;
}

/* Gold shimmer animation */
@keyframes goldShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.gold-shimmer {
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.3), transparent);
  background-size: 200% 100%;
  animation: goldShimmer 3s infinite linear;
}

/* Gold border pulse animation */
@keyframes goldBorderPulse {
  0% {
    border-color: rgba(212, 175, 55, 0.5);
    box-shadow: 0 0 5px rgba(212, 175, 55, 0.3);
  }
  50% {
    border-color: rgba(212, 175, 55, 1);
    box-shadow: 0 0 15px rgba(212, 175, 55, 0.7);
  }
  100% {
    border-color: rgba(212, 175, 55, 0.5);
    box-shadow: 0 0 5px rgba(212, 175, 55, 0.3);
  }
}

.gold-border-pulse {
  border: 2px solid rgba(212, 175, 55, 0.7);
  animation: goldBorderPulse 2s infinite ease-in-out;
}
