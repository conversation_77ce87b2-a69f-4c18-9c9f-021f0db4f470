import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { styles } from "../../styles";
import { fadeIn, textVariant } from "../../utils/motion";
import { StarsCanvas } from "../../components/canvas";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../../contexts/LanguageContext";

// Define the farms using translations
const getFarms = (t) => [
  {
    name: t(`tourism-pages.farms.farms.ferienhof-konig.name`),
    description: t(`tourism-pages.farms.farms.ferienhof-konig.description`),
    image: "https://via.placeholder.com/400x300?text=Ferienhof+Konig",
  },
  {
    name: t(`tourism-pages.farms.farms.wohlfuhlhof-zeh.name`),
    description: t(`tourism-pages.farms.farms.wohlfuhlhof-zeh.description`),
    image: "https://via.placeholder.com/400x300?text=Wohlfuhlhof+Zeh",
  },
  {
    name: t(`tourism-pages.farms.farms.staller-ferienhof.name`),
    description: t(`tourism-pages.farms.farms.staller-ferienhof.description`),
    image: "https://via.placeholder.com/400x300?text=Staller+Ferienhof",
  },
  {
    name: t(`tourism-pages.farms.farms.huberhof.name`),
    description: t(`tourism-pages.farms.farms.huberhof.description`),
    image: "https://via.placeholder.com/400x300?text=Huberhof",
  },
  {
    name: t(`tourism-pages.farms.farms.marchenbauernhof.name`),
    description: t(`tourism-pages.farms.farms.marchenbauernhof.description`),
    image: "https://via.placeholder.com/400x300?text=Marchenbauernhof",
  },
  {
    name: t(`tourism-pages.farms.farms.biohof-schlossberg.name`),
    description: t(`tourism-pages.farms.farms.biohof-schlossberg.description`),
    image: "https://via.placeholder.com/400x300?text=Biohof+Schlossberg",
  },
];

const FarmCard = ({ name, description, image, index }) => {
  const { t } = useTranslation();
  const { dir } = useLanguage();

  return (
    <motion.div
      variants={fadeIn("up", "spring", index * 0.5, 0.75)}
      className="bg-black p-5 rounded-2xl sm:w-[360px] w-full border border-[#D4AF37]"
      data-oid="dx7l:bb"
    >
      <div className="relative w-full h-[230px]" data-oid="ojoc:ad">
        <img
          src={image}
          alt={name}
          className="w-full h-full object-cover rounded-2xl border border-[#D4AF37]"
          data-oid="i5:hth0"
        />
      </div>

      <div
        className="mt-5"
        style={{
          textAlign: dir === "rtl" ? "right" : "left",
          minHeight: "120px",
        }}
        data-oid=":qvy8sb"
      >
        <h3 className="text-[#D4AF37] font-bold text-[24px]" data-oid="9uy76a_">
          {name}
        </h3>
        <p className="mt-2 text-white text-[14px]" data-oid="2.8m6vp">
          {description}
        </p>
      </div>

      <div className="mt-4 flex justify-center" data-oid="hkqrd12">
        <Link
          to="/contact"
          className="bg-black py-2 px-4 rounded-xl outline-none w-fit text-[#D4AF37] text-[14px] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
          data-oid="_giym2u"
        >
          {t("common.bookTour")}
        </Link>
      </div>
    </motion.div>
  );
};

const Bauernhofe = () => {
  const { t } = useTranslation();
  const { dir } = useLanguage();

  // Get the farms with translations
  const farms = getFarms(t);

  return (
    <div className="relative w-full h-auto mx-auto" data-oid="h6geu:2">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid="n0ame-d"
      >
        <motion.div variants={textVariant()} data-oid="6043wlt">
          <p
            className={`${styles.sectionSubText} text-center text-[#D4AF37]`}
            data-oid="pvq.lsp"
          >
            {t("tourism-pages.farms.subtitle")}
          </p>
          <h2
            className={`${styles.sectionHeadText} text-center`}
            data-oid="4otes7-"
          >
            {t("tourism-pages.farms.title")}
          </h2>
        </motion.div>

        <motion.p
          variants={fadeIn("", "", 0.1, 1)}
          className="mt-4 text-white text-[17px] max-w-3xl leading-[30px] text-center mx-auto"
          style={{ direction: dir }}
          data-oid="f9ifoai"
        >
          {t("tourism-pages.farms.description")}
        </motion.p>

        <div
          className="mt-20 flex flex-wrap gap-7 justify-center"
          data-oid="tylslgq"
        >
          {farms.map((farm, index) => (
            <FarmCard
              key={`farm-${index}`}
              index={index}
              {...farm}
              data-oid="pwvb8a0"
            />
          ))}
        </div>

        <div
          className="mt-10 bg-black rounded-2xl p-8 border border-[#D4AF37]"
          data-oid="7jhvijj"
        >
          <h3
            className="text-[#D4AF37] font-bold text-[24px] text-center"
            data-oid="n71g3yc"
          >
            {t("tourism-pages.farms.custom-tours")}
          </h3>
          <p
            className="mt-4 text-white text-[17px] max-w-3xl leading-[30px] text-center mx-auto"
            style={{ direction: dir }}
            data-oid="j3v7-e:"
          >
            {t("tourism-pages.farms.custom-tours-description")}
          </p>
          <div className="mt-5 flex justify-center" data-oid="sn5oq0j">
            <Link
              to="/contact"
              className="bg-black py-3 px-8 rounded-xl outline-none w-fit text-[#D4AF37] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
              data-oid="_qklkzk"
            >
              {t("common.planCustomTour")}
            </Link>
          </div>
        </div>
      </div>
      <StarsCanvas data-oid="tnuebnn" />
    </div>
  );
};

export default Bauernhofe;
