import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Flex, Text, FormControl, FormLabel, Select } from "@chakra-ui/react";

import useForceRerender from "../../hooks/useForceRerender";
import PreviewSlider from "../../components/common/PreviewSlider";
import Customize from "../../components/common/Customize";
import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";

import FallingText from "../../content/TextAnimations/FallingText/FallingText";
import { fallingText } from "../../constants/code/TextAnimations/fallingTextCode";

const FallingTextDemo = () => {
  const [gravity, setGravity] = useState(0.56);
  const [mouseConstraintStiffness, setMouseConstraintStiffness] = useState(0.9);
  const [trigger, setTrigger] = useState("hover");

  const [key, forceRerender] = useForceRerender();

  const propData = [
    {
      name: "text",
      type: "string",
      default: "",
      description: "The text content to display and eventually animate.",
    },
    {
      name: "highlightWords",
      type: "string[]",
      default: "[]",
      description: "List of words or substrings to apply a highlight style.",
    },
    {
      name: "highlightClass",
      type: "string",
      default: `"highlighted"`,
      description: "CSS class name for highlighted words.",
    },
    {
      name: "trigger",
      type: "'click' | 'hover' | 'auto' | 'scroll'",
      default: `"click"`,
      description: "Defines how the falling effect is activated.",
    },
    {
      name: "backgroundColor",
      type: "string",
      default: `"transparent"`,
      description: "Canvas background color for the physics world.",
    },
    {
      name: "wireframes",
      type: "boolean",
      default: "false",
      description: "Whether to render the physics bodies in wireframe mode.",
    },
    {
      name: "gravity",
      type: "number",
      default: "1",
      description: "Vertical gravity factor for the physics engine.",
    },
    {
      name: "mouseConstraintStiffness",
      type: "number",
      default: "0.2",
      description: "Stiffness for the mouse drag constraint.",
    },
    {
      name: "fontSize",
      type: "string",
      default: `"1rem"`,
      description: "Font size applied to the text before it falls.",
    },
    {
      name: "wordSpacing",
      type: "string",
      default: `"2px"`,
      description: "Horizontal spacing between each word.",
    },
  ];

  return (
    <TabbedLayout data-oid="52sy5t_">
      <PreviewTab data-oid="vjjkykd">
        <Flex
          position="relative"
          className="demo-container"
          h={400}
          overflow="hidden"
          justifyContent="center"
          alignItems="center"
          p={0}
          data-oid="i:tdxyl"
        >
          <FallingText
            key={key}
            text={`React Bits is a library of animated and interactive React components designed to streamline UI development and simplify your workflow.`}
            highlightWords={[
              "React",
              "Bits",
              "animated",
              "components",
              "simplify",
            ]}
            highlightClass="highlighted"
            trigger={trigger}
            gravity={gravity}
            fontSize="2rem"
            mouseConstraintStiffness={mouseConstraintStiffness}
            data-oid="9pp-6cl"
          />

          <Text
            color="#222"
            fontSize="4rem"
            fontWeight={900}
            position="absolute"
            zIndex={0}
            userSelect="none"
            data-oid="ph-cw_2"
          >
            {trigger === "hover"
              ? "Hover Me"
              : trigger === "click"
                ? "Click Me"
                : "Auto Start"}
          </Text>
        </Flex>

        <Customize data-oid="a_ml.am">
          <FormControl width="auto" data-oid=":oxgrhq">
            <Flex alignItems={"center"} gap={2} mb={4} data-oid=".2mmgq:">
              <FormLabel fontSize="sm" margin={0} data-oid="u7kfpz9">
                Trigger
              </FormLabel>
              <Select
                width="150px"
                value={trigger}
                onChange={(e) => {
                  setTrigger(e.target.value);
                  forceRerender();
                }}
                data-oid="nee644x"
              >
                <option value="hover" data-oid="ukz1:1_">
                  Hover
                </option>
                <option value="click" data-oid="tnw2s2d">
                  Click
                </option>
                <option value="auto" data-oid="-trr3ov">
                  Auto
                </option>
                <option value="scroll" data-oid="auluejj">
                  Scroll
                </option>
              </Select>
            </Flex>
          </FormControl>

          <PreviewSlider
            title="Gravity"
            min={0.1}
            max={2}
            step={0.01}
            value={gravity}
            onChange={(val) => {
              setGravity(val);
              forceRerender();
            }}
            data-oid="20jr0ho"
          />

          <PreviewSlider
            title="Mouse Constraint Stiffness"
            min={0.1}
            max={2}
            step={0.1}
            value={mouseConstraintStiffness}
            onChange={(val) => {
              setMouseConstraintStiffness(val);
              forceRerender();
            }}
            data-oid="9sgeuff"
          />
        </Customize>

        <PropTable data={propData} data-oid="s_-1fak" />
        <Dependencies dependencyList={["matter-js"]} data-oid="55a817d" />
      </PreviewTab>

      <CodeTab data-oid="irjnai-">
        <CodeExample codeObject={fallingText} data-oid="mx9-w6w" />
      </CodeTab>

      <CliTab data-oid="d8-3:kk">
        <CliInstallation {...fallingText} data-oid="gn03ov3" />
      </CliTab>
    </TabbedLayout>
  );
};

export default FallingTextDemo;
