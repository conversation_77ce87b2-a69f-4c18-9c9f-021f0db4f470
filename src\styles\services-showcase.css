/* Elegant Services Showcase Styles */

.services-showcase {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  width: 100%;
  max-width: 1170px;
  margin: 0 auto;
  padding: 2em 0;
}

/* Service Card */
.service-card {
  position: relative;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid #D4AF37;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  transition: transform 0.3s ease, box-shadow 0.3s ease, scale 0.3s ease;
  width: 100%;
  max-width: 380px;
  height: 450px; /* Fixed height to match other cards */
  display: flex;
  flex-direction: column;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.7), 0 0 20px rgba(212, 175, 55, 0.3);
}

/* Service Header */
.service-header {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.9), rgba(212, 175, 55, 0.1));
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

[dir="rtl"] .service-header {
  background: linear-gradient(to left, rgba(0, 0, 0, 0.9), rgba(212, 175, 55, 0.1));
}



/* Service Icon */
.service-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0 0 4px #D4AF37, inset 0 2px 0 rgba(0, 0, 0, 0.08), 0 3px 0 4px rgba(0, 0, 0, 0.05);
  margin-right: 2rem;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

[dir="rtl"] .service-icon {
  margin-right: 0;
  margin-left: 2rem;
}

.service-icon-img {
  width: 60%;
  height: 60%;
  object-fit: contain;
}

/* Service Title */
.service-title {
  flex: 1;
}

.service-name {
  color: #D4AF37;
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 0.5rem;
}

.service-company {
  color: #fff;
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 0.5rem;
}

.service-date {
  color: #D4AF37;
  font-size: 14px;
  font-weight: bold;
}

/* Service Content */
.service-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-points {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0;
  flex: 1;
}

[dir="rtl"] .service-points {
  padding-left: 0;
  padding-right: 1.5rem;
  text-align: right;
}

.service-point {
  color: #f5f5f5;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 1rem;
  position: relative;
  transition: color 0.3s ease;
}

.service-point::before {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: #D4AF37;
  transition: width 0.3s ease;
}

[dir="rtl"] .service-point::before {
  left: auto;
  right: 0;
}

.service-point:hover::before {
  width: 100%;
}

/* Gold Accent Line */
.service-accent {
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, transparent, #D4AF37, transparent);
  margin-top: auto;
}

/* Card hover effect */
.service-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(212, 175, 55, 0.2) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.service-card:hover::after,
.service-card-expanded::after {
  opacity: 1;
}

/* Responsive styles */
@media only screen and (max-width: 768px) {
  .service-header {
    flex-direction: column;
    text-align: center;
  }

  .service-icon {
    margin-right: 0;
    margin-bottom: 1.5rem;
  }

  [dir="rtl"] .service-icon {
    margin-left: 0;
    margin-bottom: 1.5rem;
  }

  .service-title {
    text-align: center;
  }

  [dir="rtl"] .service-points,
  .service-points {
    padding-left: 1rem;
    padding-right: 1rem;
    text-align: center;
  }

  .service-point::before {
    left: 50%;
    transform: translateX(-50%);
    width: 0;
  }

  [dir="rtl"] .service-point::before {
    right: 50%;
    left: auto;
    transform: translateX(50%);
  }

  .service-point:hover::before {
    width: 80%;
  }
}

/* Animation for gold accent */
@keyframes shimmer {
  0% { background-position: -100% 0; }
  100% { background-position: 200% 0; }
}

.service-accent {
  background: linear-gradient(to right, transparent, #D4AF37, transparent);
  background-size: 200% 100%;
  animation: shimmer 3s infinite;
}

/* View toggle styles */
.view-toggle-container {
  display: flex;
  gap: 1rem;
  background: rgba(0, 0, 0, 0.6);
  padding: 0.5rem;
  border-radius: 30px;
  border: 1px solid #D4AF37;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.view-toggle-btn {
  padding: 0.5rem 1.2rem;
  border-radius: 20px;
  background: transparent;
  color: #fff;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.view-toggle-btn.active {
  background: rgba(212, 175, 55, 0.2);
  color: #D4AF37;
  box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
}

.view-toggle-btn:hover {
  background: rgba(212, 175, 55, 0.1);
  transform: translateY(-2px);
}

.view-toggle-icon {
  font-size: 1.2rem;
}
