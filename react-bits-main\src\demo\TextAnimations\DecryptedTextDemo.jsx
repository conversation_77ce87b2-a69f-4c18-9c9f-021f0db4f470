import { useState } from "react";
import {
  Box,
  Flex,
  FormControl,
  FormLabel,
  Select,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Switch,
  Text,
} from "@chakra-ui/react";
import {
  TabbedLayout,
  PreviewTab,
  CodeTab,
  CliTab,
} from "../../components/common/TabbedLayout";
import { toast } from "sonner";

import CliInstallation from "../../components/code/CliInstallation";
import CodeExample from "../../components/code/CodeExample";
import Dependencies from "../../components/code/Dependencies";
import PropTable from "../../components/common/PropTable";
import RefreshButton from "../../components/common/RefreshButton";
import useForceRerender from "../../hooks/useForceRerender";

import DecryptedText from "../../content/TextAnimations/DecryptedText/DecryptedText";
import { decryptedText } from "../../constants/code/TextAnimations/decryptedTextCode";

const DecryptedTextDemo = () => {
  const [speed, setspeed] = useState(60);
  const [maxIterations, setMaxIterations] = useState(10);
  const [sequential, setSequential] = useState(true);
  const [revealDirection, setRevealDirection] = useState("start");
  const [useOriginalCharsOnly, setUseOriginalCharsOnly] = useState(false);
  const [animateOn, setAnimateOn] = useState("view");

  const [key, forceRerender] = useForceRerender();

  const propData = [
    {
      name: "text",
      type: "string",
      default: '""',
      description: "The text content to decrypt.",
    },
    {
      name: "speed",
      type: "number",
      default: "50",
      description: "Time in ms between each iteration.",
    },
    {
      name: "maxIterations",
      type: "number",
      default: "10",
      description: "Max # of random iterations (non-sequential mode).",
    },
    {
      name: "sequential",
      type: "boolean",
      default: "false",
      description: "Whether to reveal one character at a time in sequence.",
    },
    {
      name: "revealDirection",
      type: `"start" | "end" | "center"`,
      default: `"start"`,
      description:
        "From which position characters begin to reveal in sequential mode.",
    },
    {
      name: "useOriginalCharsOnly",
      type: "boolean",
      default: "false",
      description:
        "Restrict scrambling to only the characters already in the text.",
    },
    {
      name: "className",
      type: "string",
      default: '""',
      description: "CSS class for revealed characters.",
    },
    {
      name: "parentClassName",
      type: "string",
      default: '""',
      description: "CSS class for the main characters container.",
    },
    {
      name: "encryptedClassName",
      type: "string",
      default: '""',
      description: "CSS class for encrypted characters.",
    },
    {
      name: "animateOn",
      type: `"view" | "hover"`,
      default: `"hover"`,
      description: "Trigger scrambling on hover or scroll-into-view.",
    },
  ];

  return (
    <TabbedLayout data-oid="gwv:ie7">
      <PreviewTab data-oid="wb628ty">
        <Box
          position="relative"
          justifyContent="flex-start"
          py={{ md: 6, sm: 4 }}
          className="demo-container"
          overflow="hidden"
          data-oid="22uzr0q"
        >
          <RefreshButton onClick={forceRerender} data-oid="txi10t4" />
          <Flex
            pl={{ md: 6, sm: 3 }}
            m={{ md: 8, sm: 2 }}
            direction="column"
            key={key}
            data-oid="fqi-.k3"
          >
            <DecryptedText
              speed={speed}
              text="Ahoy, matey!"
              maxIterations={maxIterations}
              sequential={sequential}
              revealDirection={revealDirection}
              parentClassName="decrypted-text"
              useOriginalCharsOnly={useOriginalCharsOnly}
              animateOn={animateOn}
              data-oid="v7sk0-4"
            />

            <DecryptedText
              speed={speed}
              text="Set yer eyes on this"
              maxIterations={maxIterations}
              sequential={sequential}
              revealDirection={revealDirection}
              parentClassName="decrypted-text"
              useOriginalCharsOnly={useOriginalCharsOnly}
              animateOn={animateOn}
              data-oid="b2dvn3g"
            />

            <DecryptedText
              speed={speed}
              text="And try tinkerin’ round’"
              maxIterations={maxIterations}
              sequential={sequential}
              revealDirection={revealDirection}
              parentClassName="decrypted-text"
              useOriginalCharsOnly={useOriginalCharsOnly}
              animateOn={animateOn}
              data-oid="7nckwpq"
            />

            <DecryptedText
              speed={speed}
              text="with these here props, arr!"
              maxIterations={maxIterations}
              sequential={sequential}
              revealDirection={revealDirection}
              parentClassName="decrypted-text"
              useOriginalCharsOnly={useOriginalCharsOnly}
              animateOn={animateOn}
              onAnimationComplete={() => toast("✅ Animation Finished!")}
              data-oid="e-3d29l"
            />
          </Flex>
        </Box>

        <Flex
          direction="column"
          alignItems="flex-start"
          gap={4}
          my={4}
          style={{ maxWidth: "100%", overflow: "auto" }}
          data-oid="88qcxuf"
        >
          <h2 className="demo-title-extra" data-oid=".8sgqgp">
            Customize
          </h2>
          <Flex wrap="wrap" gap={4} mb={4} data-oid="e:i7trw">
            <FormControl width="auto" data-oid="n8yah1n">
              <FormLabel mb="2" data-oid="1d8_:07">
                Animate On
              </FormLabel>
              <Select
                width="auto"
                value={animateOn}
                onChange={(e) => {
                  setAnimateOn(e.target.value);
                  forceRerender();
                }}
                data-oid="zf1iher"
              >
                <option value="hover" data-oid="l5sq453">
                  hover
                </option>
                <option value="view" data-oid="61konhf">
                  view
                </option>
              </Select>
            </FormControl>

            <FormControl width="auto" data-oid="2wssw5i">
              <FormLabel mb="2" data-oid="d601b33">
                Direction
              </FormLabel>
              <Select
                width="auto"
                value={revealDirection}
                onChange={(e) => {
                  setRevealDirection(e.target.value);
                  forceRerender();
                }}
                data-oid="s6d_wey"
              >
                <option value="start" data-oid="6r75:vu">
                  start
                </option>
                <option value="end" data-oid="i504ib3">
                  end
                </option>
                <option value="center" data-oid="8_9:ks-">
                  center
                </option>
              </Select>
            </FormControl>
          </Flex>

          <Flex wrap="wrap" gap={4} mb={4} data-oid="i5ss8e9">
            <FormControl width="auto" data-oid=":0c3g9f">
              <FormLabel mb="0" data-oid="bzg0ivd">
                Speed
              </FormLabel>
              <Flex alignItems="center" gap={2} data-oid="pb:33k-">
                <Slider
                  min={10}
                  max={200}
                  step={10}
                  width="100px"
                  value={speed}
                  onChange={(val) => {
                    setspeed(val);
                    forceRerender();
                  }}
                  data-oid="gvk6_ee"
                >
                  <SliderTrack data-oid="3q96hn1">
                    <SliderFilledTrack data-oid="yr9bwcw" />
                  </SliderTrack>
                  <SliderThumb data-oid="2jq9pev" />
                </Slider>
                <Text data-oid="-wd61mf">{speed}ms</Text>
              </Flex>
            </FormControl>

            <FormControl width="auto" data-oid="uqhiy-8">
              <FormLabel mb="0" data-oid=":qh61jm">
                Iterations
              </FormLabel>
              <Flex alignItems="center" gap={2} data-oid="a2gkkhb">
                <Slider
                  min={1}
                  max={50}
                  step={1}
                  width="100px"
                  value={maxIterations}
                  onChange={(val) => {
                    setMaxIterations(val);
                    forceRerender();
                  }}
                  data-oid="ny-49gc"
                >
                  <SliderTrack data-oid="ns.y71l">
                    <SliderFilledTrack data-oid="prt5mt2" />
                  </SliderTrack>
                  <SliderThumb data-oid="-70gil3" />
                </Slider>
                <Text ml={2} data-oid="9k561fs">
                  {maxIterations}
                </Text>
              </Flex>
            </FormControl>
          </Flex>

          <Flex wrap="wrap" gap={4} mb={4} data-oid="ca6j-h5">
            <FormControl
              width="auto"
              display="flex"
              alignItems="center"
              data-oid="ffvp50g"
            >
              <FormLabel mb="0" mr={2} data-oid=":-7jarj">
                Sequential
              </FormLabel>
              <Switch
                mr={4}
                isChecked={sequential}
                onChange={() => {
                  setSequential(!sequential);
                  forceRerender();
                }}
                data-oid="2dq2yep"
              />
            </FormControl>

            <FormControl
              width="auto"
              display="flex"
              alignItems="center"
              data-oid="mqp2nqp"
            >
              <FormLabel mb="0" mr={2} data-oid="lnhiw9-">
                Original Chars
              </FormLabel>
              <Switch
                isChecked={useOriginalCharsOnly}
                onChange={() => {
                  setUseOriginalCharsOnly(!useOriginalCharsOnly);
                  forceRerender();
                }}
                data-oid="on2i7f4"
              />
            </FormControl>
          </Flex>
        </Flex>

        <PropTable data={propData} data-oid="_fczz_g" />
        <Dependencies dependencyList={["framer-motion"]} data-oid="blzu1bn" />
      </PreviewTab>

      <CodeTab data-oid="05aotn4">
        <CodeExample codeObject={decryptedText} data-oid="gsy0dat" />
      </CodeTab>

      <CliTab data-oid="4m:624j">
        <CliInstallation {...decryptedText} data-oid="9qx2nx-" />
      </CliTab>
    </TabbedLayout>
  );
};

export default DecryptedTextDemo;
