import { useState } from "react";
import { toast } from "sonner";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import {
  Box,
  Button,
  Flex,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Text,
} from "@chakra-ui/react";

import RefreshButton from "../../components/common/RefreshButton";
import CodeExample from "../../components/code/CodeExample";
import Dependencies from "../../components/code/Dependencies";
import useForceRerender from "../../hooks/useForceRerender";
import PropTable from "../../components/common/PropTable";
import CliInstallation from "../../components/code/CliInstallation";

import BlurText from "../../content/TextAnimations/BlurText/BlurText";
import { blurText } from "../../constants/code/TextAnimations/blurTextCode";

const BlurTextDemo = () => {
  const [animateBy, setAnimateBy] = useState("words");
  const [direction, setDirection] = useState("top");
  const [delay, setDelay] = useState(200);

  const [key, forceRerender] = useForceRerender();

  const propData = [
    {
      name: "text",
      type: "string",
      default: '""',
      description: "The text content to animate.",
    },
    {
      name: "animateBy",
      type: "string",
      default: '"words"',
      description: "Determines whether to animate by 'words' or 'letters'.",
    },
    {
      name: "direction",
      type: "string",
      default: '"top"',
      description:
        "Direction from which the words/letters appear ('top' or 'bottom').",
    },
    {
      name: "delay",
      type: "number",
      default: "200",
      description: "Delay between animations for each word/letter (in ms).",
    },
    {
      name: "stepDuration",
      type: "number",
      default: "0.35",
      description:
        "The time taken for each letter/word to animate (in seconds).",
    },
    {
      name: "threshold",
      type: "number",
      default: "0.1",
      description: "Intersection threshold for triggering the animation.",
    },
    {
      name: "rootMargin",
      type: "string",
      default: '"0px"',
      description: "Root margin for the intersection observer.",
    },
    {
      name: "onAnimationComplete",
      type: "function",
      default: "undefined",
      description: "Callback function triggered when all animations complete.",
    },
  ];

  return (
    <TabbedLayout data-oid="81egm-7">
      <PreviewTab data-oid="063m538">
        <Box
          position="relative"
          className="demo-container"
          minH={400}
          overflow="hidden"
          data-oid="ecn:1ru"
        >
          <RefreshButton onClick={forceRerender} data-oid="nozzl_t" />
          <BlurText
            key={key}
            text="Isn't this so cool?!"
            animateBy={animateBy}
            direction={direction}
            delay={delay}
            onAnimationComplete={() => toast("✅ Animation Finished!")}
            className="blur-text-demo"
            data-oid="ndct5hu"
          />
        </Box>

        <div className="preview-options" data-oid="5ep72lm">
          <h2 className="demo-title-extra" data-oid="7a2fqzg">
            Customize
          </h2>
          <Flex gap={4} wrap="wrap" data-oid="7t.ch3u">
            <Button
              fontSize="xs"
              h={8}
              onClick={() => {
                setAnimateBy(animateBy === "words" ? "letters" : "words");
                forceRerender();
              }}
              data-oid="xrz9vwo"
            >
              Animate By:{" "}
              <Text color={"#a1a1aa"} data-oid="4x5cb_8">
                &nbsp;{animateBy}
              </Text>
            </Button>
            <Button
              fontSize="xs"
              h={8}
              onClick={() => {
                setDirection(direction === "top" ? "bottom" : "top");
                forceRerender();
              }}
              data-oid="0datorw"
            >
              Direction:{" "}
              <Text color={"#a1a1aa"} data-oid="f7za4ua">
                &nbsp;{direction}
              </Text>
            </Button>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="5v:wzhm">
            <Text fontSize="sm" data-oid="w_z4sin">
              Delay (ms):
            </Text>
            <Slider
              min={50}
              max={500}
              step={10}
              value={delay}
              onChange={(val) => {
                setDelay(val);
                forceRerender();
              }}
              width="200px"
              data-oid="18wl3dz"
            >
              <SliderTrack data-oid="mcuqbku">
                <SliderFilledTrack data-oid="skq3m.k" />
              </SliderTrack>
              <SliderThumb data-oid="9:aw3_m" />
            </Slider>
            <Text fontSize="sm" data-oid="s92gsqt">
              {delay}ms
            </Text>
          </Flex>
        </div>

        <PropTable data={propData} data-oid="iow35f9" />
        <Dependencies dependencyList={["framer-motion"]} data-oid="-zud5br" />
      </PreviewTab>

      <CodeTab data-oid="4opw1pm">
        <CodeExample codeObject={blurText} data-oid="ssz.sim" />
      </CodeTab>

      <CliTab data-oid="mg0g68b">
        <CliInstallation {...blurText} data-oid="8-g4k.6" />
      </CliTab>
    </TabbedLayout>
  );
};

export default BlurTextDemo;
