import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { styles } from "../../styles";
import { fadeIn, textVariant } from "../../utils/motion";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../../contexts/LanguageContext";
import RollingGallery from "../../components/ReactBits/RollingGallery";
import ModelViewer from "../../components/ReactBits/ModelViewer";

import "../../styles/carpool.css";



const BMW7 = () => {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const [isMobile, setIsMobile] = useState(false);
  const [activeImage, setActiveImage] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Using images from the BMW7 folder
  const images = [
    "/src/assets/BMW7/BMW1.jpg",
    "/src/assets/BMW7/BMW2.jpg",
    "/src/assets/BMW7/BMW3.jpg",
    "/src/assets/BMW7/BMW4.jpg",
  ];

  // Fallback image in case the original images don't load
  const fallbackImage =
    "https://images.unsplash.com/photo-1523983388277-336a66bf9bcd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80";

  // Toggle fullscreen view
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  useEffect(() => {
    // Add a listener for changes to the screen size
    const mediaQuery = window.matchMedia("(max-width: 500px)");

    // Set the initial value of the `isMobile` state variable
    setIsMobile(mediaQuery.matches);

    // Define a callback function to handle changes to the media query
    const handleMediaQueryChange = (event) => {
      setIsMobile(event.matches);
    };

    // Add the callback function as a listener for changes to the media query
    mediaQuery.addEventListener("change", handleMediaQueryChange);

    // Remove the listener when the component is unmounted
    return () => {
      mediaQuery.removeEventListener("change", handleMediaQueryChange);
    };
  }, []);

  return (
    <div className="carpool-container black-gold-bg" data-oid="se69v76">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid="pl4bamh"
      >
        <motion.div
          variants={fadeIn("", "", 0.1, 1)}
          className="carpool-frame"
          data-oid="0t9x3sw"
        >
          <motion.div
            variants={textVariant()}
            className="carpool-header"
            data-oid="7i18uch"
          >
            <p className="carpool-subtitle" data-oid="mg8h4q:">
              {language === "ar"
                ? t("common.premiumVehicle")
                : "Premium Vehicle"}
            </p>
            <h2 className="carpool-title" data-oid=":z804jk">
              {language === "ar" ? t("vehicles.bmw-7.title") : "BMW 7 Series"}
            </h2>
            <p className="carpool-description" data-oid="scu5lyl">
              {language === "ar"
                ? t("vehicles.bmw-7.description")
                : "The perfect fusion of luxury and driving dynamics with exceptional comfort."}
            </p>
          </motion.div>

          {/* 3D Model Viewer Section */}
          <div className="model-viewer-section" data-oid="quo57dt">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="model-viewer-wrapper"
            >
              <ModelViewer
                url="/Bmw_7/scene.gltf"
                width="100%"
                height={500}
                modelXOffset={0}
                modelYOffset={0}
                defaultRotationX={-10}
                defaultRotationY={30}
                defaultZoom={0.8}
                minZoomDistance={0.5}
                maxZoomDistance={2}
                enableManualRotation={true}
                enableManualZoom={true}
                enableMouseParallax={false}
                enableHoverRotation={false}
                autoRotate={true}
                autoRotateSpeed={0.25}
                environmentPreset="studio"
                ambientIntensity={0.7}
                keyLightIntensity={1.8}
                fillLightIntensity={1.0}
                rimLightIntensity={1.2}
                fadeIn={false}
                showScreenshotButton={true}
                autoFrame={true}
              />
            </motion.div>
          </div>
        </motion.div>

        {/* Rolling Gallery Section */}
        <motion.div
          variants={fadeIn("", "", 0.2, 1)}
          className="rolling-gallery-section"
          data-oid="gallery-section"
        >
          <h3 className="section-title" data-oid="gallery-title">
            {language === "ar"
              ? t("vehicles.bmw-7.gallery")
              : "Vehicle Gallery"}
          </h3>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="rolling-gallery-wrapper"
          >
            <RollingGallery
              images={images}
              autoplay={true}
              pauseOnHover={true}
            />
          </motion.div>
        </motion.div>

        {/* New Modern Gallery */}
        <motion.div
          variants={fadeIn("", "", 0.2, 1)}
          className="car-info-section"
          data-oid="hzxusqn"
        >
          <h3 className="section-title" data-oid="44.l4y9">
            {language === "ar" ? t("vehicles.bmw-7.gallery") : "Gallery"}
          </h3>
          <div className="modern-gallery-container" data-oid="v5.-rx3">
            {/* Main large image */}
            <div
              className="modern-gallery-main"
              onClick={toggleFullscreen}
              data-oid="cmh.nge"
            >
              <motion.img
                key={activeImage}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                src={images[activeImage]}
                alt={
                  language === "ar"
                    ? t("vehicles.bmw-7.title")
                    : "BMW 7 Series Featured"
                }
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = fallbackImage;
                }}
                data-oid="_tx7fos"
              />
            </div>

            {/* Thumbnails row */}
            <div className="modern-gallery-thumbnails" data-oid="z.kj96c">
              {images.map((image, index) => (
                <div
                  key={index}
                  className={`modern-gallery-thumb ${activeImage === index ? "active" : ""}`}
                  onClick={() => setActiveImage(index)}
                  data-oid="-bpb9wq"
                >
                  <img
                    src={image}
                    alt={
                      language === "ar"
                        ? `${t("vehicles.bmw-7.title")} ${index + 1}`
                        : `BMW 7 Series ${index + 1}`
                    }
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = fallbackImage;
                    }}
                    data-oid="ddmth._"
                  />
                </div>
              ))}
            </div>

            {/* Fullscreen Modal */}
            {isFullscreen && (
              <motion.div
                className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                data-oid="8t8iokn"
              >
                <div className="relative w-full max-w-6xl" data-oid="tcrcw9z">
                  <button
                    className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full w-10 h-10 flex items-center justify-center border border-[#D4AF37] hover:bg-opacity-70 z-10"
                    onClick={toggleFullscreen}
                    data-oid="ybc9zxy"
                  >
                    ✕
                  </button>
                  <img
                    src={images[activeImage]}
                    alt={
                      language === "ar"
                        ? t("vehicles.bmw-7.title")
                        : "BMW 7 Series Fullscreen"
                    }
                    className="max-h-[80vh] w-auto mx-auto object-cover p-2"
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = fallbackImage;
                    }}
                    data-oid="in6.i_1"
                  />

                  <div
                    className="flex justify-center mt-4 gap-2"
                    data-oid="gvq18n2"
                  >
                    {images.map((image, index) => (
                      <div
                        key={`fullscreen-thumb-${index}`}
                        className={`w-32 h-24 cursor-pointer rounded-md overflow-hidden border-2 ${activeImage === index ? "border-[#D4AF37]" : "border-gray-600"} flex items-center justify-center p-1`}
                        onClick={(e) => {
                          e.stopPropagation();
                          setActiveImage(index);
                        }}
                        data-oid="lyqr:ao"
                      >
                        <img
                          src={image}
                          alt={
                            language === "ar"
                              ? `${t("vehicles.bmw-7.title")} ${index + 1}`
                              : `BMW 7 Series Thumbnail ${index + 1}`
                          }
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = fallbackImage;
                          }}
                          data-oid="sqidnsz"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>

        <div className="cta-container" data-oid="_r.aaa8">
          <div className="car-info-section" data-oid="rqi0og4">
            <h3 className="section-title" data-oid="ce6ezao">
              {language === "ar"
                ? t("vehicles.bmw-7.premium-services")
                : "Premium Services & Driving Luxury"}
            </h3>
            <div className="specs-grid mb-6" data-oid="pjp455n">
              <div className="specs-category" data-oid="k_78jqq">
                <ul className="specs-list" data-oid="._:4qvz">
                  {language === "ar" ? (
                    // Arabic services list (first half)
                    t("vehicles.bmw-7.services-list", { returnObjects: true })
                      .slice(0, 4)
                      .map((service, index) => (
                        <li
                          key={index}
                          className="specs-item"
                          data-oid="39xltxc"
                        >
                          <span className="specs-item-icon" data-oid="nc4_4_z">
                            •
                          </span>{" "}
                          {service}
                        </li>
                      ))
                  ) : (
                    // English services list (first half)
                    <>
                      <li className="specs-item" data-oid="50du.b8">
                        <span className="specs-item-icon" data-oid="ep01vqc">
                          •
                        </span>{" "}
                        Professional chauffeur service
                      </li>
                      <li className="specs-item" data-oid="zyrooge">
                        <span className="specs-item-icon" data-oid="46a66kj">
                          •
                        </span>{" "}
                        Complimentary Wi-Fi onboard
                      </li>
                      <li className="specs-item" data-oid="7f4_c15">
                        <span className="specs-item-icon" data-oid="jjj9zwt">
                          •
                        </span>{" "}
                        Premium refreshments
                      </li>
                      <li className="specs-item" data-oid="aslekg:">
                        <span className="specs-item-icon" data-oid="o:s88c0">
                          •
                        </span>{" "}
                        Daily newspapers and magazines
                      </li>
                    </>
                  )}
                </ul>
              </div>
              <div className="specs-category" data-oid="_3jcn-n">
                <ul className="specs-list" data-oid="-w8i-zq">
                  {language === "ar" ? (
                    // Arabic services list (second half)
                    t("vehicles.bmw-7.services-list", { returnObjects: true })
                      .slice(4)
                      .map((service, index) => (
                        <li
                          key={index + 4}
                          className="specs-item"
                          data-oid="oj.dyea"
                        >
                          <span className="specs-item-icon" data-oid="1bd7hx9">
                            •
                          </span>{" "}
                          {service}
                        </li>
                      ))
                  ) : (
                    // English services list (second half)
                    <>
                      <li className="specs-item" data-oid="e-_34wb">
                        <span className="specs-item-icon" data-oid="jt992-c">
                          •
                        </span>{" "}
                        BMW iDrive system with gesture control
                      </li>
                      <li className="specs-item" data-oid="ctwj5jf">
                        <span className="specs-item-icon" data-oid="g619j__">
                          •
                        </span>{" "}
                        Bowers & Wilkins Diamond Surround Sound System
                      </li>
                      <li className="specs-item" data-oid="r:_pm30">
                        <span className="specs-item-icon" data-oid="0ar1fbo">
                          •
                        </span>{" "}
                        Panoramic Sky Lounge LED roof
                      </li>
                      <li className="specs-item" data-oid="x:g7690">
                        <span className="specs-item-icon" data-oid="32fy4lj">
                          •
                        </span>{" "}
                        Rear-seat entertainment system with touch command tablet
                      </li>
                    </>
                  )}
                </ul>
              </div>
            </div>
            <p className="section-content" data-oid="y4ig0nd">
              {language === "ar"
                ? t("vehicles.bmw-7.book-cta")
                : "Book your premium BMW 7 Series chauffeur service today and enjoy the perfect blend of performance and luxury."}
            </p>
            <div className="flex justify-center mt-6" data-oid="17g_1in">
              <Link to="/contact" className="cta-button" data-oid="b_86xy3">
                {language === "ar" ? t("common.bookNow") : "Book Now"}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BMW7;
