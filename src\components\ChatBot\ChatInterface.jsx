import React, { useState, useRef, useEffect } from "react";
import { useChat } from "../../contexts/ChatContext";

const ChatInterface = () => {
  const { isChatOpen, messages, sendMessage, isLoading, clearChat } = useChat();
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Scroll to bottom of messages when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Focus input when chat is opened
  useEffect(() => {
    if (isChatOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isChatOpen]);

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    if (inputValue.trim() && !isLoading) {
      sendMessage(inputValue.trim());
      setInputValue("");
    }
  };

  // If chat is not open, don't render anything
  if (!isChatOpen) {
    return null;
  }

  return (
    <div
      className="fixed bottom-24 right-6 z-40 w-96 sm:w-[450px] md:w-[500px] h-[600px] bg-white rounded-lg shadow-xl flex flex-col overflow-hidden border border-gray-200"
      data-oid="exl-3fr"
    >
      {/* Chat header */}
      <div
        className="flex items-center justify-between p-4 bg-blue-600 text-white"
        data-oid="kv:tjik"
      >
        <h3 className="font-medium" data-oid="6vxcsg6">
          Chat Support
        </h3>
        <div className="flex space-x-2" data-oid="obmgc42">
          <button
            onClick={clearChat}
            className="p-1 hover:bg-blue-700 rounded"
            aria-label="Clear chat"
            title="Clear chat"
            data-oid="xvrka1i"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              data-oid="9bm30wm"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                data-oid="ocudo6v"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Chat messages */}
      <div className="flex-1 p-4 overflow-y-auto bg-gray-50" data-oid="aho-r1p">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`mb-4 ${message.role === "user" ? "text-right" : "text-left"}`}
            data-oid="19w4r0f"
          >
            <div
              className={`inline-block p-3 rounded-lg max-w-[80%] ${
                message.role === "user"
                  ? "bg-blue-600 text-white rounded-br-none"
                  : "bg-gray-200 text-gray-800 rounded-bl-none"
              }`}
              data-oid="g1f3dsi"
            >
              {message.content}
            </div>
          </div>
        ))}

        {/* Loading indicator */}
        {isLoading && (
          <div className="text-left mb-4" data-oid="vcw97hm">
            <div
              className="inline-block p-3 rounded-lg max-w-[80%] bg-gray-200 text-gray-800 rounded-bl-none"
              data-oid="jhn:9om"
            >
              <div className="flex space-x-2" data-oid="372m0q0">
                <div
                  className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"
                  data-oid="1c:8zvs"
                ></div>
                <div
                  className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"
                  style={{ animationDelay: "0.2s" }}
                  data-oid="aks8t_l"
                ></div>
                <div
                  className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"
                  style={{ animationDelay: "0.4s" }}
                  data-oid="q978ino"
                ></div>
              </div>
            </div>
          </div>
        )}

        {/* Invisible element to scroll to */}
        <div ref={messagesEndRef} data-oid="v11fqdl" />
      </div>

      {/* Chat input */}
      <form
        onSubmit={handleSubmit}
        className="p-4 border-t border-gray-200 bg-white"
        data-oid="s-hx2qv"
      >
        <div className="flex space-x-2" data-oid="l2kub.f">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Type your message..."
            className="flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isLoading}
            data-oid="na5txvk"
          />

          <button
            type="submit"
            className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            disabled={!inputValue.trim() || isLoading}
            data-oid="byuiree"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              data-oid="b:wngbz"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                data-oid="u_z2.ny"
              />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};

export default ChatInterface;
