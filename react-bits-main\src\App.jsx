import { Route, BrowserRouter as Router, Routes } from "react-router-dom";
import { SearchProvider } from "./components/context/SearchContext/SearchContext";
import { LanguageProvider } from "./components/context/LanguageContext/LanguageContext";
import { useEffect } from "react";
import { Toaster } from "sonner";
import { forceChakraDarkTheme } from "./utils/utils";
import { toastStyles } from "./utils/customTheme";

import Header from "./components/navs/Header";
import Sidebar from "./components/navs/Sidebar";

import LandingPage from "./pages/LandingPage";
import CategoryPage from "./pages/CategoryPage";
import ShowcasePage from "./pages/ShowcasePage";

export default function App() {
  useEffect(() => {
    forceChakraDarkTheme();
  }, []);

  return (
    <Router data-oid="6x9:17c">
      <Routes data-oid="hf83ct_">
        <Route
          exact
          path="/"
          element={<LandingPage data-oid="yh8h159" />}
          data-oid="wb5tgru"
        />

        <Route
          exact
          path="/showcase"
          element={<ShowcasePage data-oid="ztyps.d" />}
          data-oid="s2llzs8"
        />

        <Route
          path="/:category/:subcategory"
          element={
            <SearchProvider data-oid="ui5wrun">
              <LanguageProvider data-oid="xjprxqd">
                <main className="app-container" data-oid="kyx3qqn">
                  <Header data-oid="4-s22ub" />
                  <section className="category-wrapper" data-oid="ipikk75">
                    <Sidebar data-oid="mw.5lg7" />
                    <CategoryPage data-oid="pwgsel-" />
                  </section>
                  <Toaster
                    toastOptions={toastStyles}
                    position="bottom-right"
                    visibleToasts={1}
                    data-oid=":6s7q7o"
                  />
                </main>
              </LanguageProvider>
            </SearchProvider>
          }
          data-oid="uk1-qtm"
        />
      </Routes>
    </Router>
  );
}
