import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  motion,
  AnimatePresence,
  useScroll,
  useTransform,
} from "framer-motion";
import { styles } from "../styles";
import Slideshow from "./Slideshow";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../contexts/LanguageContext";

const Hero = () => {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(true);

  // Get the text phrases from translations
  const textPhrases = t("hero.typewriter", { returnObjects: true });

  // Effect to cycle through text phrases
  useEffect(() => {
    if (!textPhrases || textPhrases.length === 0) return;

    const interval = setInterval(() => {
      setIsAnimating(false);
      setTimeout(() => {
        setCurrentTextIndex(
          (prevIndex) => (prevIndex + 1) % textPhrases.length,
        );
        setIsAnimating(true);
      }, 500); // Wait for exit animation to complete
    }, 4000); // Change text every 4 seconds

    return () => clearInterval(interval);
  }, [textPhrases]);
  return (
    <section
      className="relative w-full h-screen mx-auto hero-section overflow-hidden"
      data-oid="wakcmz-"
    >
      {/* Slideshow background for hero section */}
      <div className="absolute inset-0 z-0 w-full h-full" data-oid="zt9h0gr">
        <Slideshow data-oid="7ef0ckh" />
      </div>

      {/* Gradient overlay at the bottom for smooth transition */}
      <div
        className="absolute bottom-0 left-0 w-full h-10 bg-gradient-to-t from-black to-transparent z-10"
        data-oid="o:u1sad"
      ></div>

      {/* Hero content */}
      <div
        className={`relative pt-[100px] max-w-7xl mx-auto ${styles.paddingX} flex flex-row items-start gap-5 z-20`}
        data-oid="mjyaa36"
      >
        <div
          className="flex flex-col justify-center items-center mt-5"
          data-oid="n8beoe7"
        >
          <div
            className="w-5 h-5 rounded-full bg-secondary"
            data-oid="561b6o0"
          />

          <div className="w-1 sm:h-80 h-40 gold-gradient" data-oid="mlq_pb5" />
        </div>

        <div data-oid="61bj0t2">
          <h1
            className={`font-black mt-2 drop-shadow-[0_5px_5px_rgba(0,0,0,0.5)] tracking-wider uppercase leading-[40px] sm:leading-[50px] md:leading-[60px]`}
            data-oid=".fi:aih"
          >
            {language === "ar" ? (
              // Arabic title with the same color scheme
              <span
                className="flex flex-row items-center flex-wrap"
                data-oid="5q0ynos"
              >
                <span
                  className="text-white text-[30px] sm:text-[35px] md:text-[40px] lg:text-[45px]"
                  data-oid="7_q766u"
                >
                  سائق
                </span>{" "}
                <span
                  className="text-[#D4AF37] text-[30px] sm:text-[35px] md:text-[40px] lg:text-[45px]"
                  data-oid="jh.hnf3"
                >
                  فاخر
                </span>
              </span>
            ) : (
              // English title with SEO keywords
              <span
                className="flex flex-row items-center flex-wrap"
                data-oid="pcc2n2w"
              >
                <span
                  className="text-white text-[30px] sm:text-[35px] md:text-[40px] lg:text-[45px]"
                  data-oid="sy:zgb3"
                >
                  PREMIUM
                </span>
                <span
                  className="text-[#D4AF37] ml-2 text-[30px] sm:text-[35px] md:text-[40px] lg:text-[45px]"
                  data-oid="echf9h7"
                >
                  CHAUFFEUR
                </span>
              </span>
            )}
          </h1>
          <h2
            className={`${styles.heroSubText} mt-2 text-white-100 drop-shadow-[0_2px_2px_rgba(0,0,0,0.5)] flex flex-col sm:flex-row`}
            data-oid="7xb3lic"
          >
            <span
              className={language === "ar" ? "ml-2" : "mr-2"}
              data-oid="v-y-.9_"
            >
              {t("hero.subtitle")}
            </span>
            <div
              className="text-animation-container mt-2 sm:mt-0"
              data-oid="ss5.wth"
            >
              <AnimatePresence mode="wait" data-oid="ep.u-cu">
                {isAnimating && textPhrases && textPhrases.length > 0 && (
                  <motion.span
                    key={currentTextIndex}
                    className="text-[#D4AF37] font-semibold modern-text-animation"
                    initial={{ y: 20, opacity: 0 }}
                    animate={{
                      y: 0,
                      opacity: 1,
                      transition: {
                        type: "spring",
                        stiffness: 100,
                        damping: 12,
                      },
                    }}
                    exit={{
                      y: -20,
                      opacity: 0,
                      transition: {
                        duration: 0.3,
                        ease: "easeInOut",
                      },
                    }}
                    data-oid="_s8ma23"
                  >
                    {textPhrases[currentTextIndex]}
                  </motion.span>
                )}
              </AnimatePresence>
            </div>
          </h2>

          {/* SEO-optimized description paragraph */}
          <p
            className="mt-4 text-white-100 max-w-3xl text-[15px] sm:text-[18px] leading-relaxed drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]"
            data-oid="c6xvepv"
          >
            {language === "ar"
              ? "خدمة سائق فاخر في أوروبا مع سيارات مرسيدس الفئة S وبي إم دبليو الفئة 7 ومرسيدس الفئة V. نقدم خدمات نقل رجال الأعمال، وخدمة نقل من المطار، وخدمة نقل كبار الشخصيات في جميع أنحاء أوروبا."
              : language === "de"
                ? "Erstklassiger Chauffeurservice mit exklusiven Transportlösungen für anspruchsvolle Kunden. Business, Executive und VIP Service mit höchstem Komfort und Diskretion für Geschäftstermine, Flughafentransfers und besondere Anlässe."
                : "We offer first-class chauffeur service and exclusive transportation solutions for discerning clients. Our Business Chauffeur Service, Executive Transfer Service, and VIP Chauffeur Service guarantee the highest comfort and discretion. With our professional drivers and luxury vehicles, you'll experience a stylish and comfortable journey."}
          </p>

          {/* Removed "Our Vehicles" button as requested */}
        </div>
      </div>
    </section>
  );
};

export default Hero;
