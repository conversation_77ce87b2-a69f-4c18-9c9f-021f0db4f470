import { Box, Icon } from "@chakra-ui/react";
import { FaPlusCircle, FaMinusCircle } from "react-icons/fa";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";

import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";

import ElasticSlider from "../../content/Components/ElasticSlider/ElasticSlider";
import { elasticSlider } from "../../constants/code/Components/elasticSliderCode";

const ElasticSliderDemo = () => {
  const propData = [
    {
      name: "defaultValue",
      type: "number",
      default: 50,
      description:
        "The initial value of the slider. It can be less than startingValue or greater than maxValue.",
    },
    {
      name: "startingValue",
      type: "number",
      default: 0,
      description:
        "The starting point for the slider's range, e.g., startingValue=100 allows the slider to start at 100.",
    },
    {
      name: "maxValue",
      type: "number",
      default: 100,
      description: "The maximum value the slider can reach.",
    },
    {
      name: "className",
      type: "string",
      default: "",
      description: "Allows passing custom class names to style the component.",
    },
    {
      name: "isStepped",
      type: "boolean",
      default: false,
      description: "Enables or disables stepped increments on the slider.",
    },
    {
      name: "stepSize",
      type: "number",
      default: 1,
      description:
        "The size of the increments for the slider when isStepped is enabled.",
    },
    {
      name: "leftIcon",
      type: "JSX.Element",
      default: "<>-</>",
      description:
        "Custom JSX or HTML code to display on the left side of the slider.",
    },
    {
      name: "rightIcon",
      type: "JSX.Element",
      default: "<>+</>",
      description:
        "Custom JSX or HTML code to display on the right side of the slider.",
    },
  ];

  return (
    <TabbedLayout data-oid="-mojn..">
      <PreviewTab data-oid="0pcfivs">
        <h2 className="demo-title-extra" data-oid="v563z53">
          Default
        </h2>
        <Box
          position="relative"
          className="demo-container"
          minH={200}
          data-oid="f6myrhk"
        >
          <ElasticSlider data-oid="7tavj:s" />
        </Box>

        <h2 className="demo-title-extra" data-oid="krfr8h0">
          Steps
        </h2>
        <Box
          position="relative"
          className="demo-container"
          minH={200}
          data-oid="1tpov4e"
        >
          <ElasticSlider isStepped stepSize={10} data-oid="vj_p7up" />
        </Box>

        <h2 className="demo-title-extra" data-oid="j0tofq4">
          Custom Values & Icons
        </h2>
        <Box
          position="relative"
          className="demo-container"
          minH={200}
          data-oid="nqy:g.w"
        >
          <ElasticSlider
            leftIcon={<Icon as={FaMinusCircle} data-oid="k7t-5s9" />}
            rightIcon={<Icon as={FaPlusCircle} data-oid="sl4zlut" />}
            startingValue={500}
            defaultValue={750}
            maxValue={1000}
            data-oid="zqg2rgk"
          />
        </Box>

        <PropTable data={propData} data-oid="z22b8rf" />
        <Dependencies dependencyList={["framer-motion"]} data-oid="0kidgcw" />
      </PreviewTab>

      <CodeTab data-oid="dhya7.h">
        <CodeExample codeObject={elasticSlider} data-oid=".4bh2ji" />
      </CodeTab>

      <CliTab data-oid="lj8it.k">
        <CliInstallation {...elasticSlider} data-oid="yvxq4eb" />
      </CliTab>
    </TabbedLayout>
  );
};

export default ElasticSliderDemo;
