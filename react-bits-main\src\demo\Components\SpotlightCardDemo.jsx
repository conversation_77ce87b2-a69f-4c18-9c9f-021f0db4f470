import { Box, Button, Flex, Icon, Text } from "@chakra-ui/react";
import { VscSparkleFilled } from "react-icons/vsc";
import { FaLock } from "react-icons/fa6";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";

import CodeExample from "../../components/code/CodeExample";
import PropTable from "../../components/common/PropTable";
import CliInstallation from "../../components/code/CliInstallation";

import SpotlightCard from "../../content/Components/SpotlightCard/SpotlightCard";
import { spotlightCard } from "../../constants/code/Components/spotlightCardCode";

const SpotlightCardDemo = () => {
  const propData = [
    {
      name: "spotlightColor",
      type: "string",
      default: "rgba(255, 255, 255, 0.25)",
      description:
        "Controls the color of the radial gradient used for the spotlight effect.",
    },
    {
      name: "className",
      type: "string",
      default: "",
      description: "Allows adding custom classes to the component.",
    },
  ];

  return (
    <TabbedLayout data-oid="bh-._ji">
      <PreviewTab data-oid="a1vef9n">
        <Box
          position="relative"
          className="demo-container"
          py={10}
          data-oid="de-d4-u"
        >
          <SpotlightCard className="custom-spotlight-card" data-oid="ct-ln1p">
            <Flex
              h={"100%"}
              direction="column"
              alignItems="flex-start"
              justifyContent="center"
              data-oid="1k7d-1e"
            >
              <Icon
                mb={3}
                boxSize={12}
                as={VscSparkleFilled}
                data-oid="avxg0jj"
              />

              <Text
                fontWeight={600}
                fontSize={"1.4rem"}
                letterSpacing={"-.5px"}
                data-oid="41ey0i-"
              >
                Boost Your Experience
              </Text>
              <Text
                color="#a1a1aa"
                fontSize={"14px"}
                mt={1}
                mb={8}
                data-oid="p025kdo"
              >
                Get exclusive benefits, features & 24/7 support as a permanent
                club member.
              </Text>
              <Button
                border={"1px solid #222"}
                background={"linear-gradient(to bottom, #222, #111)"}
                _hover={{
                  background: "linear-gradient(to bottom, #222, #111)",
                }}
                rounded="xl"
                px={6}
                data-oid="h-p0jw_"
              >
                Join now
              </Button>
            </Flex>
          </SpotlightCard>
        </Box>

        <h2 className="demo-title-extra" data-oid="n1u6axp">
          Custom Color
        </h2>
        <Box
          position="relative"
          className="demo-container"
          py={10}
          data-oid="sl5f9bk"
        >
          <SpotlightCard
            className="custom-spotlight-card"
            spotlightColor="rgba(0, 229, 255, 0.2)"
            data-oid="54yxwx1"
          >
            <Flex
              h={"100%"}
              direction="column"
              alignItems="flex-start"
              justifyContent="center"
              data-oid="yi_z7-0"
            >
              <Icon mb={3} boxSize={8} as={FaLock} data-oid="o8tdk8j" />
              <Text
                fontWeight={600}
                fontSize={"1.4rem"}
                letterSpacing={"-.5px"}
                data-oid="4ighkr9"
              >
                Enhanced Security
              </Text>
              <Text
                color="#a1a1aa"
                fontSize={"14px"}
                mt={1}
                mb={8}
                data-oid="gvubmm9"
              >
                Our state of the art software offers peace of mind through
                strict security measures.
              </Text>
              <Button
                border={"1px solid #222"}
                background={"linear-gradient(to bottom, #222, #111)"}
                _hover={{
                  background: "linear-gradient(to bottom, #222, #111)",
                }}
                rounded="xl"
                px={6}
                data-oid="ss0760-"
              >
                Learn more
              </Button>
            </Flex>
          </SpotlightCard>
        </Box>

        <PropTable data={propData} data-oid="oyrzhpb" />
      </PreviewTab>

      <CodeTab data-oid=".:-a3ie">
        <CodeExample codeObject={spotlightCard} data-oid="h51w8a-" />
      </CodeTab>

      <CliTab data-oid="scxdnb8">
        <CliInstallation {...spotlightCard} data-oid="g_ebr9c" />
      </CliTab>
    </TabbedLayout>
  );
};

export default SpotlightCardDemo;
