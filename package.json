{"name": "Premium-Chauffeur", "private": true, "version": "0.0.0", "type": "module", "homepage": "/", "scripts": {"dev": "vite", "update-sitemap": "node scripts/update-sitemap.js", "prebuild": "npm run update-sitemap", "build": "vite build", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "preview-deploy": "npm run build && vite preview", "test-build": "vite build && vite preview", "analyze": "vite build --mode analyze"}, "dependencies": {"@emailjs/browser": "^3.11.0", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@react-three/drei": "^9.88.2", "@react-three/fiber": "^8.14.5", "framer-motion": "^10.12.16", "gsap": "^3.13.0", "i18next": "^25.1.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "maath": "^0.10.4", "ogl": "^1.0.11", "openai": "^4.98.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.1", "react-icons": "^4.12.0", "react-router-dom": "^6.13.0", "react-social-media-embed": "^2.5.18", "react-tilt": "^1.0.2", "react-use-measure": "^2.1.7", "react-vertical-timeline-component": "^3.6.0", "typewriter-effect": "^2.20.1"}, "devDependencies": {"@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.0.0", "eslint-plugin-react": "^7.26.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "postcss": "^8.4.24", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.3.2", "terser": "^5.39.2", "three": "^0.153.0", "vite": "^4.3.9", "vite-plugin-imagemin": "^0.6.1"}}