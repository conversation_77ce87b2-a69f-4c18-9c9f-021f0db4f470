{"accessors": [{"bufferView": 2, "componentType": 5126, "count": 126, "max": [0.7131269574165344, 0.3816457986831665, 0.1471545696258545], "min": [-0.7131309509277344, -0.4262540340423584, -0.13917136192321777], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1512, "componentType": 5126, "count": 126, "max": [0.2066671997308731, -0.2511242926120758, -0.9161128997802734], "min": [-0.20285800099372864, -0.3772488832473755, -0.967841625213623], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 126, "max": [0.9953731298446655, 0.5844839215278625], "min": [0.5644810199737549, 0.27559220790863037], "type": "VEC2"}, {"bufferView": 0, "componentType": 5125, "count": 576, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3024, "componentType": 5126, "count": 66, "max": [0.07029420137405396, 0.47249743342399597, 0.236586332321167], "min": [-0.11138533055782318, -0.43953219056129456, -0.18247461318969727], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3816, "componentType": 5126, "count": 66, "max": [-0.8717824816703796, 0.031026609241962433, -0.36558741331100464], "min": [-0.9307768940925598, 0.00041401301859878004, -0.4898107647895813], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1008, "componentType": 5126, "count": 66, "max": [0.5201572775840759, 0.19815970957279205], "min": [0.04673122987151146, 0.022400250658392906], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2304, "componentType": 5125, "count": 192, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4608, "componentType": 5126, "count": 68, "max": [0.11169741302728653, 0.4724980592727661, 0.23658418655395508], "min": [-0.07029426097869873, -0.43953263759613037, -0.1824737787246704], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5424, "componentType": 5126, "count": 68, "max": [0.931118369102478, 0.031170861795544624, -0.3647163212299347], "min": [0.870337724685669, 0.0007619028328917921, -0.4923842251300812], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1536, "componentType": 5126, "count": 68, "max": [0.5201579332351685, 0.19815920293331146], "min": [0.04673122987151146, 0.022400200366973877], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3072, "componentType": 5125, "count": 192, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6240, "componentType": 5126, "count": 30, "max": [0.06956583261489868, 0.3061618208885193, 0.19197118282318115], "min": [-0.09473621845245361, -0.3753350079059601, -0.15742075443267822], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6600, "componentType": 5126, "count": 30, "max": [-0.8772031664848328, 0.010170549154281616, -0.36210718750953674], "min": [-0.9320811033248901, -0.01852254383265972, -0.4801178276538849], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2080, "componentType": 5126, "count": 30, "max": [0.963410496711731, 0.22018590569496155], "min": [0.5668368339538574, 0.01872706040740013], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3840, "componentType": 5125, "count": 120, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6960, "componentType": 5126, "count": 54, "max": [0.037238240242004395, 0.20808911323547363, 0.14618158340454102], "min": [-0.06444305181503296, -0.14673089981079102, -0.07247841358184814], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7608, "componentType": 5126, "count": 54, "max": [-0.9031680822372437, -0.00433655409142375, -0.38888874650001526], "min": [-0.920711874961853, -0.03613133728504181, -0.4292650818824768], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2320, "componentType": 5126, "count": 54, "max": [0.9506363868713379, 0.2462342083454132], "min": [0.5827721357345581, 0.019491909071803093], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4320, "componentType": 5125, "count": 105, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8256, "componentType": 5126, "count": 33, "max": [0.0937662422657013, 0.30616170167922974, 0.19197165966033936], "min": [-0.07036349922418594, -0.3753350079059601, -0.15742135047912598], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8652, "componentType": 5126, "count": 33, "max": [0.9324595332145691, 0.010028714314103127, -0.3611353039741516], "min": [0.8770939707756042, -0.019809788092970848, -0.4803181290626526], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2752, "componentType": 5126, "count": 33, "max": [0.963410496711731, 0.22018590569496155], "min": [0.5668370127677917, 0.0187261700630188], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4740, "componentType": 5125, "count": 120, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 9048, "componentType": 5126, "count": 62, "max": [0.06444346904754639, 0.2080897092819214, 0.1461794376373291], "min": [-0.03723776340484619, -0.14673137664794922, -0.0724785327911377], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 9792, "componentType": 5126, "count": 62, "max": [0.9207137227058411, -0.004293176345527172, -0.3888852894306183], "min": [0.9031558036804199, -0.03613295778632164, -0.42929157614707947], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3016, "componentType": 5126, "count": 62, "max": [0.9506378173828125, 0.24623289704322815], "min": [0.5827721357345581, 0.019490540027618408], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5220, "componentType": 5125, "count": 105, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 10536, "componentType": 5126, "count": 117, "max": [0.8027686476707458, 0.43347471952438354, 0.19329404830932617], "min": [-0.802768886089325, -0.40751928091049194, -0.1982109546661377], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 11940, "componentType": 5126, "count": 117, "max": [0.19132325053215027, 0.4890158176422119, -0.862510621547699], "min": [-0.19131749868392944, 0.34970760345458984, -0.9249175190925598], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3512, "componentType": 5126, "count": 117, "max": [0.4465194046497345, 0.7396757006645203], "min": [0.03340750187635422, 0.35412999987602234], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5640, "componentType": 5125, "count": 576, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 13344, "componentType": 5126, "count": 60, "max": [0.248240664601326, 0.0005309581756591797, 0.05475723743438721], "min": [-0.24824054539203644, -0.0005319118499755859, -0.05475735664367676], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 14064, "componentType": 5126, "count": 60, "max": [0.0009892730740830302, -0.999966561794281, -0.008124375715851784], "min": [2.8559010388562456e-05, -0.9999669790267944, -0.008162044920027256], "type": "VEC3"}, {"bufferView": 3, "componentType": 5126, "count": 60, "max": [1.0, 1.0, 1.0, 1.0], "min": [1.0, 1.0, 1.0, 1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 4448, "componentType": 5126, "count": 60, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7944, "componentType": 5125, "count": 120, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 14784, "componentType": 5126, "count": 14390, "max": [0.256460964679718, 0.035570383071899414, 0.06827494502067566], "min": [-0.2630727291107178, -0.03785252571105957, -0.07327604293823242], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 187464, "componentType": 5126, "count": 14390, "max": [1.0, 1.0, 0.9999998211860657], "min": [-1.0, -1.0, -0.9999996423721313], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4928, "componentType": 5126, "count": 14390, "max": [1.0120999813079834, 100.12559509277344], "min": [-1408.5860595703125, -104.82209777832031], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 120048, "componentType": 5126, "count": 14390, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 8424, "componentType": 5125, "count": 20388, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 360144, "componentType": 5126, "count": 544, "max": [0.12908777594566345, -0.03796553611755371, -0.05319264531135559], "min": [-0.13018903136253357, -0.038083553314208984, -0.0659712553024292], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 366672, "componentType": 5126, "count": 544, "max": [0.003547330154106021, -0.9998852014541626, 0.015141032636165619], "min": [-0.00284333317540586, -1.0, 0.0003075630229432136], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 235168, "componentType": 5126, "count": 544, "max": [0.589084804058075, 0.6838312149047852], "min": [0.588607907295227, 0.683357834815979], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 239520, "componentType": 5126, "count": 544, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 89976, "componentType": 5125, "count": 546, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 373200, "componentType": 5126, "count": 54, "max": [0.2477945238351822, 0.013936996459960938, 0.05053126811981201], "min": [-0.2477944791316986, -0.013936996459960938, -0.05053102970123291], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 373848, "componentType": 5126, "count": 54, "max": [0.00045507214963436127, 0.9640058875083923, 0.2659057080745697], "min": [-0.0003368801553733647, 0.9639989733695984, 0.26588112115859985], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 960, "componentType": 5126, "count": 54, "max": [1.0, 1.0, 1.0, 1.0], "min": [1.0, 1.0, 1.0, 1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 243872, "componentType": 5126, "count": 54, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 92160, "componentType": 5125, "count": 120, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 374496, "componentType": 5126, "count": 1004, "max": [0.3423590064048767, 0.03600902482867241, 0.03269742429256439], "min": [-0.42598748207092285, -0.022841855883598328, -0.06745637208223343], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 386544, "componentType": 5126, "count": 1004, "max": [0.9999632835388184, 0.9999179244041443, 0.9995790123939514], "min": [-0.9999682903289795, -0.9999517798423767, -0.9998723864555359], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 244304, "componentType": 5126, "count": 1004, "max": [0.9790000319480896, 0.9939000010490417], "min": [0.9716999530792236, 0.9777001142501831], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 92640, "componentType": 5125, "count": 2994, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 398592, "componentType": 5126, "count": 1003, "max": [0.42116260528564453, 0.07433503121137619, 0.030624350532889366], "min": [-0.4115142822265625, -0.09156681597232819, -0.05767592787742615], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 410628, "componentType": 5126, "count": 1003, "max": [1.0, 0.9976479411125183, 0.9999143481254578], "min": [-0.999714195728302, -0.9990066289901733, -0.9998176097869873], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 252336, "componentType": 5126, "count": 1003, "max": [0.9790000319480896, 0.9938001036643982], "min": [0.9726999998092651, 0.9774001240730286], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 104616, "componentType": 5125, "count": 3084, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 422664, "componentType": 5126, "count": 21, "max": [0.48626458644866943, 0.08414483070373535, 0.1808653473854065], "min": [-0.4878652095794678, -0.09796810150146484, -0.15228906273841858], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 422916, "componentType": 5126, "count": 21, "max": [0.12273689359426498, -0.8171033263206482, 0.5764616131782532], "min": [-0.11044185608625412, -0.9253518581390381, 0.37901756167411804], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 260360, "componentType": 5126, "count": 21, "max": [2.4098060131073, 1.576220989227295], "min": [-3.3686349391937256, -0.18212659657001495], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 260528, "componentType": 5126, "count": 21, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 116952, "componentType": 5125, "count": 72, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 423168, "componentType": 5126, "count": 3854, "max": [0.948556125164032, 0.28894221782684326, 0.0883905291557312], "min": [-0.950518012046814, -0.2735116481781006, -0.0488622784614563], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 469416, "componentType": 5126, "count": 3854, "max": [0.9999401569366455, 0.9988101720809937, 1.0], "min": [-0.9999403357505798, -0.9989810585975647, -1.0], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 1824, "componentType": 5126, "count": 3854, "max": [1.0, 1.0, 1.0, 1.0], "min": [1.0, 1.0, 1.0, 1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 260696, "componentType": 5126, "count": 3854, "max": [91.63179779052734, 24.209199905395508], "min": [-90.63179779052734, -23.216598510742188], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 291528, "componentType": 5126, "count": 3854, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 117240, "componentType": 5125, "count": 17220, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 515664, "componentType": 5126, "count": 19033, "max": [0.978423535823822, 1.882338285446167, 0.49299728870391846], "min": [-0.9656797051429749, -3.1712148189544678, -0.798155665397644], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 744060, "componentType": 5126, "count": 19033, "max": [1.0, 0.9999736547470093, 0.9999927878379822], "min": [-1.0, -0.9999766945838928, -0.999999463558197], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 322360, "componentType": 5126, "count": 19033, "max": [54.40272903442383, 52.10470962524414], "min": [-57.37584686279297, -61.54372024536133], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 186120, "componentType": 5125, "count": 51231, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 972456, "componentType": 5126, "count": 2851, "max": [0.8792712092399597, 1.288177251815796, 0.41890430450439453], "min": [-0.8665273785591125, -1.883664846420288, 0.007318258285522461], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1006668, "componentType": 5126, "count": 2851, "max": [1.0, 0.9999726414680481, 0.9998564124107361], "min": [-1.0, -0.9999739527702332, -0.9999064803123474], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 474624, "componentType": 5126, "count": 2851, "max": [52.513790130615234, 52.41205978393555], "min": [-57.37584686279297, -135.45748901367188], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 391044, "componentType": 5125, "count": 8250, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1040880, "componentType": 5126, "count": 126, "max": [0.6690197587013245, 0.08850908279418945, 0.05347433686256409], "min": [-0.669019877910614, -0.09694099426269531, -0.060364753007888794], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1042392, "componentType": 5126, "count": 126, "max": [0.9107801914215088, 0.999973714351654, 0.999973714351654], "min": [-0.9107834696769714, -0.9162431359291077, -0.9999736547470093], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 497432, "componentType": 5126, "count": 126, "max": [0.9969255924224854, 0.16775169968605042], "min": [0.8915799260139465, 0.00492745591327548], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 498440, "componentType": 5126, "count": 126, "max": [0.9957607388496399, 0.9821298122406006], "min": [0.9758718013763428, 0.9622411131858826], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 424044, "componentType": 5125, "count": 240, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1043904, "componentType": 5126, "count": 4161, "max": [0.9210718274116516, 1.7059454917907715, 0.451244056224823], "min": [-0.8538697361946106, -1.6967711448669434, -0.18581217527389526], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1093836, "componentType": 5126, "count": 4161, "max": [0.9989684224128723, 0.9960854053497314, 0.9999887347221375], "min": [-0.9999790191650391, -0.8808805346488953, -0.9999887943267822], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 499448, "componentType": 5126, "count": 4161, "max": [0.5908696055412292, 0.6847661137580872], "min": [0.5900841355323792, 0.6841115951538086], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 425004, "componentType": 5125, "count": 15756, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1143768, "componentType": 5126, "count": 6818, "max": [0.8260046243667603, 0.6160111427307129, 0.12710309028625488], "min": [-0.8240804076194763, -0.8766101598739624, -0.2739834785461426], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1225584, "componentType": 5126, "count": 6818, "max": [0.9999424815177917, 0.9999973177909851, 0.9999819993972778], "min": [-0.9999427199363708, -0.9999972581863403, -0.9999963641166687], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 532736, "componentType": 5126, "count": 6818, "max": [0.3229939937591553, 0.6199764013290405], "min": [0.10556109994649887, 0.3802799880504608], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 488028, "componentType": 5125, "count": 38214, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1307400, "componentType": 5126, "count": 3595, "max": [0.8245871067047119, 0.6380928754806519, 0.10654616355895996], "min": [-0.8226609826087952, -0.8213781118392944, -0.2247166633605957], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1350540, "componentType": 5126, "count": 3595, "max": [1.0, 0.996353030204773, 0.998629093170166], "min": [-0.9997146725654602, -0.9933430552482605, -0.9999286532402039], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 587280, "componentType": 5126, "count": 3595, "max": [3.0078799724578857, 3.74330997467041], "min": [-2.43612003326416, 0.057398971170186996], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 640884, "componentType": 5125, "count": 19248, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1393680, "componentType": 5126, "count": 773, "max": [0.8198974132537842, 0.583033561706543, 0.09793806076049805], "min": [-0.8179734945297241, -0.7679752111434937, -0.2004532814025879], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1402956, "componentType": 5126, "count": 773, "max": [0.9987080097198486, 0.9967473149299622, 0.9968670010566711], "min": [-0.9986989498138428, -0.9936612844467163, -0.9987581968307495], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 616040, "componentType": 5126, "count": 773, "max": [0.9387001395225525, 1.110700011253357], "min": [-0.670199990272522, 0.453000009059906], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 717876, "componentType": 5125, "count": 4005, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1412232, "componentType": 5126, "count": 68, "max": [0.5820983052253723, 0.3847857713699341, 0.06955516338348389], "min": [-0.5801776051521301, 0.14423882961273193, 0.042997539043426514], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1413048, "componentType": 5126, "count": 68, "max": [0.05321653559803963, 0.13101649284362793, 0.9914276599884033], "min": [-0.05321653559803963, -0.13101650774478912, -0.9914276599884033], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 622224, "componentType": 5126, "count": 68, "max": [3.0340559482574463, 1.0533130168914795], "min": [-2.1397080421447754, -0.025890229269862175], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 733896, "componentType": 5125, "count": 144, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1413864, "componentType": 5126, "count": 1442, "max": [0.7419783473014832, 0.3312101364135742, 0.2118995189666748], "min": [-0.6938092708587646, -0.39525294303894043, -0.23541992902755737], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1431168, "componentType": 5126, "count": 1442, "max": [0.9947755336761475, 0.999986469745636, 0.9997695684432983], "min": [-0.9947753548622131, -0.9999892115592957, -0.9999427199363708], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 622768, "componentType": 5126, "count": 1442, "max": [0.8956063985824585, 0.655426025390625], "min": [0.6708068251609802, 0.33816391229629517], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 734472, "componentType": 5125, "count": 7386, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1448472, "componentType": 5126, "count": 544, "max": [0.526521623134613, 0.32067394256591797, 0.017068803310394287], "min": [-0.47744646668434143, 0.20818710327148438, -0.22037583589553833], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1455000, "componentType": 5126, "count": 544, "max": [0.96857750415802, 0.9998841285705566, 0.999956488609314], "min": [-0.968430757522583, -0.9996765851974487, -0.9996501803398132], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 634304, "componentType": 5126, "count": 544, "max": [0.5892772078514099, 0.6839413046836853], "min": [0.5879468321800232, 0.6836262345314026], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 764016, "componentType": 5125, "count": 3219, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1461528, "componentType": 5126, "count": 527, "max": [0.5858235359191895, 0.3199899196624756, 0.12979161739349365], "min": [-0.5523802042007446, -0.2169198989868164, -0.23413288593292236], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1467852, "componentType": 5126, "count": 527, "max": [0.9972312450408936, 0.9980372190475464, 0.9929689764976501], "min": [-0.9991718530654907, -0.9987325072288513, -0.9999770522117615], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 638656, "componentType": 5126, "count": 527, "max": [0.9660999774932861, 0.9581001400947571], "min": [0.8966999650001526, 0.8948999643325806], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 776892, "componentType": 5125, "count": 2445, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1474176, "componentType": 5126, "count": 229, "max": [0.650212824344635, 0.3222489356994629, 0.20356154441833496], "min": [-0.6020434498786926, -0.2834360599517822, -0.22105520963668823], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1476924, "componentType": 5126, "count": 229, "max": [0.9081563949584961, 0.02633434347808361, -0.0072661833837628365], "min": [-0.9081541895866394, -0.9999735951423645, -0.9990381002426147], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 642872, "componentType": 5126, "count": 229, "max": [2.744277000427246, 2.5882489681243896], "min": [-1.2168819904327393, -2.0177509784698486], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 786672, "componentType": 5125, "count": 756, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1479672, "componentType": 5126, "count": 2251, "max": [0.07531929016113281, 0.49703869223594666, 0.2540627717971802], "min": [-0.061712801456451416, -0.7107617259025574, -0.5152390003204346], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1506684, "componentType": 5126, "count": 2251, "max": [0.9999908208847046, 0.9928857684135437, 0.9994518160820007], "min": [-0.9999983310699463, -0.9999257326126099, -0.9996626377105713], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 644704, "componentType": 5126, "count": 2251, "max": [0.5482152104377747, 0.17056810855865479], "min": [0.3240146040916443, 0.03154820203781128], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 789696, "componentType": 5125, "count": 12921, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1533696, "componentType": 5126, "count": 957, "max": [0.07220661640167236, 0.48692071437835693, 0.2654157876968384], "min": [-0.06119948625564575, -0.6587443351745605, -0.1020703911781311], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1545180, "componentType": 5126, "count": 957, "max": [0.9967808127403259, 0.9996238350868225, 0.9992942810058594], "min": [-0.9999716877937317, -0.9970955848693848, -0.9935989379882812], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 662712, "componentType": 5126, "count": 957, "max": [0.5906350016593933, 0.6851099133491516], "min": [0.5904101133346558, 0.6848675012588501], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 841380, "componentType": 5125, "count": 4293, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1556664, "componentType": 5126, "count": 1606, "max": [0.068916916847229, 0.48693108558654785, 0.3814687728881836], "min": [-0.12724924087524414, -0.802250325679779, -0.493517130613327], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1575936, "componentType": 5126, "count": 1606, "max": [0.9999552965164185, 0.9999821782112122, 0.9985634684562683], "min": [-0.9996777772903442, -0.999855101108551, -0.9993583559989929], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 670368, "componentType": 5126, "count": 1606, "max": [1292.4000244140625, 10.18571949005127], "min": [-18.8189697265625, -122.05999755859375], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 858552, "componentType": 5125, "count": 7137, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1595208, "componentType": 5126, "count": 487, "max": [0.03730189800262451, 0.058167099952697754, 0.01712632179260254], "min": [-0.10457056760787964, -0.0868426263332367, -0.017856597900390625], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1601052, "componentType": 5126, "count": 487, "max": [0.9992952942848206, 0.9990686178207397, 0.9919900298118591], "min": [-0.5588459372520447, -0.9802716970443726, -0.9959669709205627], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 683216, "componentType": 5126, "count": 487, "max": [0.23156699538230896, 0.39910098910331726], "min": [0.02171272039413452, 0.3290669918060303], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 887100, "componentType": 5125, "count": 2622, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1606896, "componentType": 5126, "count": 2255, "max": [0.059965431690216064, 0.5060942769050598, 0.2562139630317688], "min": [-0.07706648111343384, -0.7017048597335815, -0.5130867958068848], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1633956, "componentType": 5126, "count": 2255, "max": [0.9999983310699463, 0.9928843379020691, 0.9994370937347412], "min": [-0.9999908208847046, -0.9999256730079651, -0.9996634125709534], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 687112, "componentType": 5126, "count": 2255, "max": [0.5482155084609985, 0.9700692296028137], "min": [0.3240146040916443, 0.831049919128418], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 897588, "componentType": 5125, "count": 12918, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1661016, "componentType": 5126, "count": 852, "max": [0.05945229530334473, 0.4959772825241089, 0.267566978931427], "min": [-0.004175007343292236, -0.649687647819519, -0.09991919994354248], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1671240, "componentType": 5126, "count": 852, "max": [0.9999718070030212, 0.9996233582496643, 0.9975616335868835], "min": [-0.9818795919418335, -0.9971188902854919, -0.9935992360115051], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 705152, "componentType": 5126, "count": 852, "max": [0.5908591151237488, 0.685077428817749], "min": [0.5906394124031067, 0.6848406195640564], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 949260, "componentType": 5125, "count": 3702, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1681464, "componentType": 5126, "count": 1609, "max": [0.12550193071365356, 0.4959888756275177, 0.3836199641227722], "min": [-0.07066518068313599, -0.7931936383247375, -0.4913659393787384], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1700772, "componentType": 5126, "count": 1609, "max": [0.9996885657310486, 0.9999819397926331, 0.9985605478286743], "min": [-0.9999551177024841, -0.9998561143875122, -0.9993579387664795], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 711968, "componentType": 5126, "count": 1609, "max": [1292.4000244140625, 10.18571949005127], "min": [-18.8189697265625, -122.05999755859375], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 964068, "componentType": 5125, "count": 7137, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1720080, "componentType": 5126, "count": 489, "max": [0.10457020998001099, 0.058166563510894775, 0.01712620258331299], "min": [-0.03730189800262451, -0.08684304356575012, -0.017856836318969727], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1725948, "componentType": 5126, "count": 489, "max": [0.557428240776062, 0.9990798234939575, 0.9920058846473694], "min": [-0.9992952942848206, -0.9802711009979248, -0.9959201216697693], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 724840, "componentType": 5126, "count": 489, "max": [0.23156699538230896, 0.39910098910331726], "min": [0.02171272039413452, 0.3290669918060303], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 992616, "componentType": 5125, "count": 2622, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1731816, "componentType": 5126, "count": 2085, "max": [0.09340447187423706, 0.5800843238830566, 0.26856112480163574], "min": [-0.07445883750915527, -0.6073553562164307, -0.5104717016220093], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1756836, "componentType": 5126, "count": 2085, "max": [0.9999818205833435, 0.9914138317108154, 0.9999097585678101], "min": [-0.9999776482582092, -0.998899519443512, -0.9998006820678711], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 728752, "componentType": 5126, "count": 2085, "max": [0.7572993040084839, 0.17273220419883728], "min": [0.535298228263855, 0.03162996843457222], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1003104, "componentType": 5125, "count": 12141, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1781856, "componentType": 5126, "count": 780, "max": [-0.012358307838439941, 0.5498464107513428, 0.27957916259765625], "min": [-0.07649880647659302, -0.5404045581817627, -0.0948939323425293], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1791216, "componentType": 5126, "count": 780, "max": [0.9748517274856567, 0.9962195754051208, 0.998328685760498], "min": [-0.9966953992843628, -0.9964104294776917, -0.9998648762702942], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 745432, "componentType": 5126, "count": 780, "max": [0.5904998779296875, 0.6849666237831116], "min": [0.5896831154823303, 0.6845074892044067], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1051668, "componentType": 5125, "count": 3210, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1800576, "componentType": 5126, "count": 1652, "max": [0.08953297138214111, 0.5498653650283813, 0.48113417625427246], "min": [-0.15733695030212402, -0.6922234296798706, -0.48784613609313965], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1820400, "componentType": 5126, "count": 1652, "max": [0.9997174143791199, 0.9986814856529236, 0.999579906463623], "min": [-0.9998497366905212, -0.9993439316749573, -0.9998279809951782], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 751672, "componentType": 5126, "count": 1652, "max": [1350.1099853515625, 54.788307189941406], "min": [-47.38712692260742, -129.59300231933594], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1064508, "componentType": 5125, "count": 7296, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1840224, "componentType": 5126, "count": 202, "max": [-0.037209928035736084, 0.529206395149231, 0.26665210723876953], "min": [-0.07576340436935425, -0.5501316785812378, -0.4805954396724701], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1842648, "componentType": 5126, "count": 202, "max": [0.9930302500724792, 0.6836155652999878, 0.6822336912155151], "min": [-0.9999867081642151, -0.39802026748657227, -0.5528426170349121], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 764888, "componentType": 5126, "count": 202, "max": [0.15375889837741852, 0.3126065135002136], "min": [0.004931271076202393, 0.03367989882826805], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1093692, "componentType": 5125, "count": 723, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1845072, "componentType": 5126, "count": 2148, "max": [0.07654565572738647, 0.5801190733909607, 0.2680278420448303], "min": [-0.09262222051620483, -0.6073203682899475, -0.5110061168670654], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1870848, "componentType": 5126, "count": 2148, "max": [0.9999710917472839, 0.9913221597671509, 0.9999067187309265], "min": [-0.9999818205833435, -0.9988998174667358, -0.9998041391372681], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 766504, "componentType": 5126, "count": 2148, "max": [0.7572993040084839, 0.9699876308441162], "min": [0.5352982878684998, 0.8288853168487549], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1096584, "componentType": 5125, "count": 12510, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1896624, "componentType": 5126, "count": 739, "max": [0.0772811770439148, 0.5498811602592468, 0.27904385328292847], "min": [0.013140678405761719, -0.5403695106506348, -0.09542936086654663], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1905492, "componentType": 5126, "count": 739, "max": [0.9966971278190613, 0.9962272644042969, 0.9983234405517578], "min": [-0.9748327732086182, -0.9964039325714111, -0.9998639822006226], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 783688, "componentType": 5126, "count": 739, "max": [0.5907586216926575, 0.6849066019058228], "min": [0.5904858112335205, 0.6846199035644531], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1146624, "componentType": 5125, "count": 3210, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1914360, "componentType": 5126, "count": 1655, "max": [0.15811926126480103, 0.5499030947685242, 0.4805998206138611], "min": [-0.08875072002410889, -0.6921896934509277, -0.4883793890476227], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1934220, "componentType": 5126, "count": 1655, "max": [0.9998502731323242, 0.9986838102340698, 0.9995802640914917], "min": [-0.9997174143791199, -0.9993459582328796, -0.9998283386230469], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 789600, "componentType": 5126, "count": 1655, "max": [1350.1099853515625, 54.788307189941406], "min": [-47.38712692260742, -129.59300231933594], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1159464, "componentType": 5125, "count": 7302, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1954080, "componentType": 5126, "count": 190, "max": [0.07217270135879517, 0.047355055809020996, 0.04131722450256348], "min": [-0.0557405948638916, -0.04651498794555664, -0.046381473541259766], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1956360, "componentType": 5126, "count": 190, "max": [0.9369238615036011, -0.2993505597114563, 0.6288378834724426], "min": [-0.8505035638809204, -0.9999738931655884, -0.6663968563079834], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 802840, "componentType": 5126, "count": 190, "max": [0.9800549745559692, 0.7927160263061523], "min": [0.023919880390167236, 0.46856698393821716], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1188672, "componentType": 5125, "count": 954, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1958640, "componentType": 5126, "count": 126, "max": [0.05339241027832031, 0.040651559829711914, 0.04124176502227783], "min": [-0.074521005153656, -0.05321645736694336, -0.04645580053329468], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1960152, "componentType": 5126, "count": 126, "max": [0.8505092859268188, -0.2993733584880829, 0.5501380562782288], "min": [-0.9369321465492249, -0.9999735951423645, -0.6663970947265625], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 804360, "componentType": 5126, "count": 126, "max": [0.9800549745559692, 0.7927160263061523], "min": [0.023919880390167236, 0.46856698393821716], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1192488, "componentType": 5125, "count": 570, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1961664, "componentType": 5126, "count": 640, "max": [0.06435245275497437, 0.01906442642211914, 0.0638725757598877], "min": [-0.06446486711502075, -0.039777517318725586, -0.0643509030342102], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1969344, "componentType": 5126, "count": 640, "max": [0.9999176263809204, 0.9999622106552124, 0.9999919533729553], "min": [-0.9999171495437622, -0.9999650120735168, -0.9998618960380554], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 805368, "componentType": 5126, "count": 640, "max": [0.8764830231666565, 0.7459489703178406], "min": [0.628367006778717, 0.49665698409080505], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1194768, "componentType": 5125, "count": 3618, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1977024, "componentType": 5126, "count": 640, "max": [0.06446486711502075, 0.019065141677856445, 0.0637783408164978], "min": [-0.06435239315032959, -0.03992795944213867, -0.06444215774536133], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1984704, "componentType": 5126, "count": 640, "max": [0.9999171495437622, 0.9999374151229858, 0.9999984502792358], "min": [-0.9999175071716309, -0.9999464154243469, -0.9998987317085266], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 810488, "componentType": 5126, "count": 640, "max": [0.8764830231666565, 0.7459489703178406], "min": [0.628367006778717, 0.49665698409080505], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1209240, "componentType": 5125, "count": 3618, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1992384, "componentType": 5126, "count": 508, "max": [0.16680961847305298, 0.2634392976760864, 0.15193188190460205], "min": [-0.1964137852191925, -0.16628170013427734, -0.10891568660736084], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1998480, "componentType": 5126, "count": 508, "max": [0.9994916319847107, 0.8130436539649963, 0.9850631356239319], "min": [-0.906162440776825, -0.9700436592102051, -0.7824061512947083], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 815608, "componentType": 5126, "count": 508, "max": [0.16244719922542572, 0.45926469564437866], "min": [0.11061470210552216, 0.30697059631347656], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1223712, "componentType": 5125, "count": 2832, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2004576, "componentType": 5126, "count": 505, "max": [0.1964135766029358, 0.2634392976760864, 0.151932954788208], "min": [-0.16680961847305298, -0.16628170013427734, -0.10891592502593994], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2010636, "componentType": 5126, "count": 505, "max": [0.90572190284729, 0.813285231590271, 0.983375072479248], "min": [-0.9995150566101074, -0.9708337783813477, -0.7877926230430603], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 819672, "componentType": 5126, "count": 505, "max": [0.16896399855613708, 0.6069890260696411], "min": [0.11333560198545456, 0.4645642042160034], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1235040, "componentType": 5125, "count": 2832, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2016696, "componentType": 5126, "count": 1172, "max": [1.5608710050582886, 0.12745881080627441, 0.20363283157348633], "min": [-0.2182549238204956, -0.34983015060424805, -0.03924614191055298], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2030760, "componentType": 5126, "count": 1172, "max": [0.9974932670593262, 0.9619002342224121, 0.961615264415741], "min": [-0.9974932074546814, -0.09870000928640366, -0.9540830850601196], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 823712, "componentType": 5126, "count": 1172, "max": [0.8765069842338562, 0.2069060057401657], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 833088, "componentType": 5126, "count": 1172, "max": [0.530634880065918, 0.2503019869327545], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 842464, "componentType": 5126, "count": 1172, "max": [0.779017984867096, 0.3821510076522827], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 851840, "componentType": 5126, "count": 1172, "max": [0.399865984916687, 0.28193798661231995], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 861216, "componentType": 5126, "count": 1172, "max": [0.399865984916687, 0.28193798661231995], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 870592, "componentType": 5126, "count": 1172, "max": [0.530634880065918, 0.2503019869327545], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 879968, "componentType": 5126, "count": 1172, "max": [0.779017984867096, 0.3821510076522827], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 889344, "componentType": 5126, "count": 1172, "max": [0.8765069842338562, 0.2069060057401657], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1246368, "componentType": 5125, "count": 4854, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2044824, "componentType": 5126, "count": 3, "max": [0.001248419051989913, 0.010247468948364258, 0.0002633333206176758], "min": [-0.0007189509924501181, -0.005124568939208984, -0.00022536516189575195], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2044860, "componentType": 5126, "count": 3, "max": [-0.10478437691926956, -0.03289274498820305, 0.9939509034156799], "min": [-0.10478437691926956, -0.03289274498820305, 0.9939509034156799], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 898720, "componentType": 5126, "count": 3, "max": [0.36916598677635193, 0.6817399859428406], "min": [0.35187599062919617, 0.6812130212783813], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1265784, "componentType": 5125, "count": 3, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2044896, "componentType": 5126, "count": 3141, "max": [0.13210612535476685, 0.06601583957672119, 0.09684228897094727], "min": [-0.16630083322525024, -0.2564312815666199, -0.10582268238067627], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2082588, "componentType": 5126, "count": 3141, "max": [0.9983632564544678, 0.9996898770332336, 0.9996161460876465], "min": [-0.9969378709793091, -0.9985671639442444, -0.9999165534973145], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 898744, "componentType": 5126, "count": 3141, "max": [0.5788682103157043, 0.7071664333343506], "min": [0.5130584239959717, 0.6624712944030762], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1265796, "componentType": 5125, "count": 15765, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2120280, "componentType": 5126, "count": 3244, "max": [0.16218751668930054, 0.06888628005981445, 0.09784948825836182], "min": [-0.13621902465820312, -0.25356101989746094, -0.10481452941894531], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2159208, "componentType": 5126, "count": 3244, "max": [0.9969158172607422, 0.9997068047523499, 0.9996156096458435], "min": [-0.9984139204025269, -0.9985580444335938, -0.9999164938926697], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 923872, "componentType": 5126, "count": 3244, "max": [0.5034639239311218, 0.7071667313575745], "min": [0.4376541078090668, 0.6624718308448792], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1328856, "componentType": 5125, "count": 15768, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2198136, "componentType": 5126, "count": 151, "max": [0.04404401779174805, 0.018822431564331055, 0.04426229000091553], "min": [-0.044043004512786865, -0.016407489776611328, -0.04399430751800537], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2199948, "componentType": 5126, "count": 151, "max": [0.9769846200942993, -0.17909178137779236, 0.9811549186706543], "min": [-0.97700035572052, -0.9999735951423645, -0.9838324785232544], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 949824, "componentType": 5126, "count": 151, "max": [0.9874281287193298, 0.9875591397285461], "min": [0.9181749820709229, 0.9193440079689026], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1391928, "componentType": 5125, "count": 810, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2201760, "componentType": 5126, "count": 151, "max": [0.044043004512786865, 0.018822431564331055, 0.04426306486129761], "min": [-0.04404407739639282, -0.016407489776611328, -0.04399353265762329], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2203572, "componentType": 5126, "count": 151, "max": [0.9769955277442932, -0.17905262112617493, 0.9811421632766724], "min": [-0.9769812822341919, -0.9999735355377197, -0.9838395118713379], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 951032, "componentType": 5126, "count": 151, "max": [0.9874281287193298, 0.9875591397285461], "min": [0.9181749820709229, 0.9193440079689026], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1395168, "componentType": 5125, "count": 810, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2205384, "componentType": 5126, "count": 5482, "max": [0.23127180337905884, 0.19350087642669678, 0.14604473114013672], "min": [-0.13296210765838623, -0.2847461700439453, -0.11615437269210815], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2271168, "componentType": 5126, "count": 5482, "max": [0.9985312223434448, 0.9859529137611389, 0.9999529123306274], "min": [-0.9999933838844299, -0.9983509182929993, -0.9999883770942688], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 952240, "componentType": 5126, "count": 5482, "max": [2.266702890396118, 2.1716361045837402], "min": [-1.783869981765747, -1.8557339906692505], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 996096, "componentType": 5126, "count": 5482, "max": [0.7124478816986084, 0.6984071135520935], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1398408, "componentType": 5125, "count": 19848, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2336952, "componentType": 5126, "count": 5482, "max": [0.1329616904258728, 0.19350159168243408, 0.1460445523262024], "min": [-0.23127222061157227, -0.284745454788208, -0.11615455150604248], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2402736, "componentType": 5126, "count": 5482, "max": [0.9999933242797852, 0.9871631264686584, 0.9999529123306274], "min": [-0.9985311031341553, -0.9983495473861694, -0.9999883770942688], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1039952, "componentType": 5126, "count": 5482, "max": [2.266702890396118, 2.1716361045837402], "min": [-1.783869981765747, -1.8557339906692505], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1083808, "componentType": 5126, "count": 5482, "max": [0.7124478816986084, 0.6984071135520935], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1477800, "componentType": 5125, "count": 19848, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2468520, "componentType": 5126, "count": 91, "max": [0.047063350677490234, 0.06824010610580444, 0.017269611358642578], "min": [-0.08749216794967651, -0.07196730375289917, -0.016863346099853516], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2469612, "componentType": 5126, "count": 91, "max": [0.9988152980804443, 0.9972609877586365, 0.3261900842189789], "min": [0.039387185126543045, -0.9447977542877197, 0.00982032809406519], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1127664, "componentType": 5126, "count": 91, "max": [0.24682089686393738, 0.3976365029811859], "min": [0.013862489722669125, 0.33140361309051514], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1557192, "componentType": 5125, "count": 243, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2470704, "componentType": 5126, "count": 91, "max": [0.08749240636825562, 0.06823980808258057, 0.017269253730773926], "min": [-0.04706299304962158, -0.0719674825668335, -0.016863703727722168], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2471796, "componentType": 5126, "count": 91, "max": [-0.03943117335438728, 0.9972646832466125, 0.32621243596076965], "min": [-0.9988154172897339, -0.9447745084762573, 0.009817848913371563], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1128392, "componentType": 5126, "count": 91, "max": [0.23198610544204712, 0.3955919146537781], "min": [0.012175729498267174, 0.32884281873703003], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1558164, "componentType": 5125, "count": 243, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2472888, "componentType": 5126, "count": 3379, "max": [0.461390882730484, 0.08010578155517578, 0.10750561952590942], "min": [-0.4614681303501129, -0.07562923431396484, -0.0978996753692627], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2513436, "componentType": 5126, "count": 3379, "max": [0.9996210932731628, 0.9994574189186096, 0.9999955296516418], "min": [-0.9996214509010315, -0.99934321641922, -0.9999701380729675], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1129120, "componentType": 5126, "count": 3379, "max": [21.809099197387695, 22.56744956970215], "min": [-18.818988800048828, -18.937870025634766], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1559136, "componentType": 5125, "count": 11820, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2553984, "componentType": 5126, "count": 627, "max": [0.45298537611961365, 0.07842683792114258, 0.10217130184173584], "min": [-0.453413724899292, -0.04191422462463379, -0.10308998823165894], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2561508, "componentType": 5126, "count": 627, "max": [0.8291815519332886, -0.49216926097869873, 0.8680164813995361], "min": [-0.8291122317314148, -0.9998350739479065, -0.7779373526573181], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1156152, "componentType": 5126, "count": 627, "max": [0.588317334651947, 0.6821230053901672], "min": [0.5878562331199646, 0.6820188164710999], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1606416, "componentType": 5125, "count": 1956, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2569032, "componentType": 5126, "count": 1694, "max": [0.10951226949691772, 0.6432186365127563, 0.33509719371795654], "min": [-0.07113820314407349, -0.737574577331543, -0.4142688512802124], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2589360, "componentType": 5126, "count": 1694, "max": [0.9999983906745911, 0.9997580051422119, 0.9999292492866516], "min": [-0.9999983906745911, -0.9997580051422119, -0.9999292492866516], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1161168, "componentType": 5126, "count": 1694, "max": [0.3455792963504791, 0.970577597618103], "min": [0.08728616684675217, 0.8322018384933472], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1614240, "componentType": 5125, "count": 8856, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2609688, "componentType": 5126, "count": 9536, "max": [1.0595299005508423, 2.1129209995269775, 0.6537572741508484], "min": [-0.9551562070846558, -2.8778040409088135, -0.6524150371551514], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2724120, "componentType": 5126, "count": 9536, "max": [0.9999591112136841, 0.9999489784240723, 0.9998977780342102], "min": [-0.9999358057975769, -0.9986366033554077, -0.9999970197677612], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1174720, "componentType": 5126, "count": 9536, "max": [0.969619631767273, 0.9921287298202515], "min": [0.04028046131134033, 0.009488998912274837], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1649664, "componentType": 5125, "count": 51174, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2838552, "componentType": 5126, "count": 8, "max": [0.1718156486749649, 0.002470254898071289, 0.012508869171142578], "min": [-0.17182216048240662, -0.0037987232208251953, -0.014644145965576172], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2838648, "componentType": 5126, "count": 8, "max": [0.03372200205922127, 0.9996150732040405, 0.1863691508769989], "min": [-0.03407341241836548, 0.9818887710571289, 0.024875089526176453], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1251008, "componentType": 5126, "count": 8, "max": [0.9982749819755554, 0.3633440136909485], "min": [0.9500241279602051, 0.0024390218313783407], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1854360, "componentType": 5125, "count": 12, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2838744, "componentType": 5126, "count": 320, "max": [0.09665811061859131, 0.02922198176383972, 0.0647658109664917], "min": [-0.09604817628860474, -0.03082519769668579, -0.051531195640563965], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2842584, "componentType": 5126, "count": 320, "max": [0.9955054521560669, 0.972310483455658, 0.9996031522750854], "min": [-0.9968639612197876, -0.6307032108306885, -0.9998265504837036], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1251072, "componentType": 5126, "count": 320, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1854408, "componentType": 5125, "count": 1344, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2846424, "componentType": 5126, "count": 308, "max": [0.09604799747467041, 0.029221534729003906, 0.0647658109664917], "min": [-0.09665805101394653, -0.030825674533843994, -0.05153012275695801], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2850120, "componentType": 5126, "count": 308, "max": [0.9968649744987488, 0.9723264575004578, 0.9996023178100586], "min": [-0.9955140352249146, -0.6305787563323975, -0.9998264908790588], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1253632, "componentType": 5126, "count": 308, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1859784, "componentType": 5125, "count": 1344, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2853816, "componentType": 5126, "count": 54, "max": [0.07406903058290482, 0.34130823612213135, 0.0217512845993042], "min": [-0.07405535876750946, -0.2539580464363098, -0.0419391393661499], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2854464, "componentType": 5126, "count": 54, "max": [0.00815348420292139, 0.03408181667327881, 0.9999954700469971], "min": [-0.008702555671334267, -0.27038177847862244, 0.96272212266922], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1256096, "componentType": 5126, "count": 54, "max": [2.303421974182129, -2.4326298236846924], "min": [0.5158644318580627, -11.173150062561035], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1865160, "componentType": 5125, "count": 192, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2855112, "componentType": 5126, "count": 207, "max": [0.030373090878129005, 0.0029866695404052734, 0.030314087867736816], "min": [-0.03036832995712757, -0.002155303955078125, -0.030310988426208496], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2857596, "componentType": 5126, "count": 207, "max": [0.9980024099349976, 0.9999945163726807, 0.9997585415840149], "min": [-0.998065710067749, -1.0, -0.999617338180542], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1256528, "componentType": 5126, "count": 207, "max": [0.588652491569519, 0.684033215045929], "min": [0.5885720252990723, 0.683952808380127], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1865928, "componentType": 5125, "count": 1230, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2860080, "componentType": 5126, "count": 421, "max": [0.02613213285803795, 0.0245208740234375, 0.010432183742523193], "min": [-0.026131896302103996, -0.024451017379760742, -0.010897219181060791], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2865132, "componentType": 5126, "count": 421, "max": [0.9807907938957214, 0.9985204339027405, 0.99522864818573], "min": [-0.9810019731521606, -0.9827780723571777, -0.9991468191146851], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1258184, "componentType": 5126, "count": 421, "max": [0.8556238412857056, 0.9937235713005066], "min": [0.17634129524230957, 0.017034947872161865], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1870848, "componentType": 5125, "count": 2430, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2870184, "componentType": 5126, "count": 869, "max": [0.07113820314407349, 0.6432186365127563, 0.33509719371795654], "min": [-0.10951226949691772, -0.737574577331543, -0.4142688512802124], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2880612, "componentType": 5126, "count": 869, "max": [0.9999983906745911, 0.9985091090202332, 0.983241617679596], "min": [-0.9994684457778931, -0.9997580051422119, -0.9999292492866516], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1261552, "componentType": 5126, "count": 869, "max": [0.3455792963504791, 0.169582799077034], "min": [0.08728610724210739, 0.031207019463181496], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1880568, "componentType": 5125, "count": 4488, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2891040, "componentType": 5126, "count": 520, "max": [0.40447625517845154, 0.028018951416015625, 0.22173181176185608], "min": [-0.3725784420967102, -0.08358526229858398, -0.19546020030975342], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2897280, "componentType": 5126, "count": 520, "max": [1.0, 0.9999180436134338, 1.0], "min": [-1.0, -0.9998205304145813, -1.0], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 63488, "componentType": 5126, "count": 520, "max": [1.0, 1.0, 1.0, 1.0], "min": [0.3019607961177826, 0.3019607961177826, 0.3019607961177826, 1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 1268504, "componentType": 5126, "count": 520, "max": [44.267696380615234, 12.753565788269043], "min": [-43.1861686706543, -11.824347496032715], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1898520, "componentType": 5125, "count": 2040, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2903520, "componentType": 5126, "count": 238, "max": [0.6848340034484863, 0.5397568941116333, 0.08412553369998932], "min": [-0.6848340034484863, -0.3755791187286377, -0.05970728397369385], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2906376, "componentType": 5126, "count": 238, "max": [0.8387386202812195, 0.8545539379119873, 0.9980239868164062], "min": [-0.8387386202812195, -0.9765693545341492, -0.997927188873291], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1272664, "componentType": 5126, "count": 238, "max": [0.9802128076553345, 0.7402451038360596], "min": [0.32024890184402466, 0.23488520085811615], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1906680, "componentType": 5125, "count": 936, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2909232, "componentType": 5126, "count": 24, "max": [0.9216374158859253, 0.024227142333984375, 0.14520320296287537], "min": [-0.9216374158859253, -0.04445493221282959, -0.13308939337730408], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2909520, "componentType": 5126, "count": 24, "max": [0.045665107667446136, 0.9922756552696228, 0.45075786113739014], "min": [-0.045665107667446136, 0.8926326632499695, 0.11534201353788376], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 71808, "componentType": 5126, "count": 24, "max": [1.0, 1.0, 1.0, 1.0], "min": [1.0, 1.0, 1.0, 1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 1274568, "componentType": 5126, "count": 24, "max": [0.08582233637571335, 2.7893590927124023], "min": [-0.2742535173892975, 0.4227170944213867], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1274760, "componentType": 5126, "count": 24, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1910424, "componentType": 5125, "count": 60, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2909808, "componentType": 5126, "count": 294, "max": [0.6541423201560974, 0.0654294490814209, 0.13294470310211182], "min": [-0.6541423201560974, -0.09125661849975586, -0.1652998924255371], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2913336, "componentType": 5126, "count": 294, "max": [0.999221920967102, 0.9507064819335938, 0.9836885929107666], "min": [-0.9992220401763916, -0.9693741798400879, -0.9542310237884521], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1274952, "componentType": 5126, "count": 294, "max": [0.9638131260871887, 0.8030508160591125], "min": [0.06316190212965012, 0.0011689659440889955], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1910664, "componentType": 5125, "count": 624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2916864, "componentType": 5126, "count": 1461, "max": [12.458320617675781, 10.644363403320312, 37.14674758911133], "min": [-5.003700256347656, -16.938644409179688, -19.66608238220215], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2934396, "componentType": 5126, "count": 1461, "max": [1.0, 0.982860267162323, 0.9952582716941833], "min": [-1.0, -0.9997853636741638, -0.9952738881111145], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 72192, "componentType": 5126, "count": 1461, "max": [1.0, 1.0, 1.0, 1.0], "min": [0.7607843279838562, 0.7607843279838562, 0.7607843279838562, 1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 1277304, "componentType": 5126, "count": 1461, "max": [0.996066689491272, 0.9969815015792847], "min": [0.008053182624280453, 0.002448617946356535], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1913160, "componentType": 5125, "count": 2742, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2951928, "componentType": 5126, "count": 411, "max": [0.2456004023551941, 0.4449206590652466, 0.2998954951763153], "min": [-0.12919682264328003, -0.5288203954696655, -0.32002872228622437], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2956860, "componentType": 5126, "count": 411, "max": [0.9906615614891052, 0.9938146471977234, 0.978843092918396], "min": [-0.9906586408615112, -0.9936517477035522, -0.9556846022605896], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 95568, "componentType": 5126, "count": 411, "max": [0.9607843160629272, 0.9607843160629272, 0.9607843160629272, 1.0], "min": [0.12156862765550613, 0.12156862765550613, 0.12156862765550613, 1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 1288992, "componentType": 5126, "count": 411, "max": [0.9674990177154541, 0.8731430172920227], "min": [0.27727729082107544, 0.14735199511051178], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1924128, "componentType": 5125, "count": 1722, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2961792, "componentType": 5126, "count": 277, "max": [0.019149640575051308, 0.005074262619018555, 0.027797162532806396], "min": [-0.016789451241493225, -0.004509687423706055, -0.017613768577575684], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2965116, "componentType": 5126, "count": 277, "max": [0.9414086937904358, 0.991484522819519, 0.9480910301208496], "min": [-0.9435219168663025, -0.9916331171989441, -0.8421008586883545], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 102144, "componentType": 5126, "count": 277, "max": [1.0, 1.0, 1.0, 1.0], "min": [0.9960784316062927, 0.9960784316062927, 0.9960784316062927, 0.9960784316062927], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 1292280, "componentType": 5126, "count": 277, "max": [0.8593134880065918, 0.9993022084236145], "min": [0.07357119768857956, 0.0007513449527323246], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1294496, "componentType": 5126, "count": 277, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1931016, "componentType": 5125, "count": 546, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2968440, "componentType": 5126, "count": 2790, "max": [0.9764021039009094, 2.5730631351470947, 0.4088423550128937], "min": [-0.9764263033866882, -2.14320707321167, -0.28900954127311707], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3001920, "componentType": 5126, "count": 2790, "max": [1.0, 0.9995251297950745, 1.0], "min": [-1.0, -0.9998292922973633, -1.0], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 106576, "componentType": 5126, "count": 2790, "max": [1.0, 1.0, 1.0, 1.0], "min": [0.0, 0.0, 0.0, 0.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 1296712, "componentType": 5126, "count": 2790, "max": [0.9640799164772034, 0.9839550256729126], "min": [0.002445637946948409, 0.002444684039801359], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1933200, "componentType": 5125, "count": 5556, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3035400, "componentType": 5126, "count": 411, "max": [0.12919706106185913, 0.4449462890625, 0.2999288737773895], "min": [-0.24560004472732544, -0.5287947654724121, -0.31999534368515015], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3040332, "componentType": 5126, "count": 411, "max": [0.9906586408615112, 0.9938147068023682, 0.9722029566764832], "min": [-0.9906614422798157, -0.9936517477035522, -0.9556846022605896], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 151216, "componentType": 5126, "count": 411, "max": [0.9607843160629272, 0.9607843160629272, 0.9607843160629272, 1.0], "min": [0.12156862765550613, 0.12156862765550613, 0.12156862765550613, 1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 1319032, "componentType": 5126, "count": 411, "max": [0.9674990177154541, 0.8731430172920227], "min": [0.27727729082107544, 0.14735199511051178], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1955424, "componentType": 5125, "count": 1722, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3045264, "componentType": 5126, "count": 278, "max": [0.3094279170036316, 0.49403393268585205, 0.3453160524368286], "min": [-0.14994141459465027, -0.5260913372039795, -0.3099978268146515], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3048600, "componentType": 5126, "count": 278, "max": [0.9991849660873413, 0.9949272871017456, 0.9727442860603333], "min": [-0.9991714358329773, -0.9948849081993103, -0.9159316420555115], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 157792, "componentType": 5126, "count": 278, "max": [1.0, 1.0, 1.0, 1.0], "min": [0.12156862765550613, 0.12156862765550613, 0.12156862765550613, 1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 1322320, "componentType": 5126, "count": 278, "max": [0.6902571320533752, 0.9763029217720032], "min": [0.036260899156332016, 0.003974973689764738], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1962312, "componentType": 5125, "count": 1164, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3051936, "componentType": 5126, "count": 278, "max": [0.15019825100898743, 0.4804518222808838, 0.314969539642334], "min": [-0.29727745056152344, -0.5135383605957031, -0.2822403311729431], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3055272, "componentType": 5126, "count": 278, "max": [0.9991257190704346, 0.9976164102554321, 0.975201427936554], "min": [-0.9991389513015747, -0.9976655840873718, -0.9575707316398621], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 162240, "componentType": 5126, "count": 278, "max": [1.0, 1.0, 1.0, 1.0], "min": [0.12156862765550613, 0.12156862765550613, 0.12156862765550613, 1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 1324544, "componentType": 5126, "count": 278, "max": [0.6902571320533752, 0.9763029217720032], "min": [0.036260899156332016, 0.003974973689764738], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1966968, "componentType": 5125, "count": 1164, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3058608, "componentType": 5126, "count": 94, "max": [0.0905638337135315, 0.2579317092895508, 0.21633827686309814], "min": [-0.08807337284088135, -0.4817383289337158, -0.35148122906684875], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3059736, "componentType": 5126, "count": 94, "max": [0.6842310428619385, 0.9959167838096619, 0.9704707264900208], "min": [-0.7797234058380127, -0.9959251284599304, -0.9302124977111816], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 166688, "componentType": 5126, "count": 94, "max": [0.12156862765550613, 0.12156862765550613, 0.12156862765550613, 1.0], "min": [0.12156862765550613, 0.12156862765550613, 0.12156862765550613, 1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 1326768, "componentType": 5126, "count": 94, "max": [0.6535444259643555, 0.9763029217720032], "min": [0.1159031018614769, 0.002801417838782072], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1971624, "componentType": 5125, "count": 252, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3060864, "componentType": 5126, "count": 433, "max": [0.6505505442619324, 0.08363056182861328, 0.03157639503479004], "min": [-0.6505497097969055, -0.10254931449890137, -0.040152668952941895], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3066060, "componentType": 5126, "count": 433, "max": [0.9481670260429382, 0.9868397116661072, 0.9997346997261047], "min": [-0.9481615424156189, -0.6869500279426575, -0.7286204695701599], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1327520, "componentType": 5126, "count": 433, "max": [0.9975999593734741, 0.8571999669075012], "min": [0.8584001064300537, 0.35839998722076416], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1330984, "componentType": 5126, "count": 433, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1972632, "componentType": 5125, "count": 2169, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3071256, "componentType": 5126, "count": 14458, "max": [0.2625780999660492, 0.029847383499145508, 0.0662723183631897], "min": [-0.25591760873794556, -0.022604703903198242, -0.06925922632217407], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3244752, "componentType": 5126, "count": 14458, "max": [1.0, 0.9950317144393921, 0.9605063796043396], "min": [-1.0, -0.9982955455780029, -0.9994105696678162], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1334448, "componentType": 5126, "count": 14458, "max": [1.0120999813079834, 100.12559509277344], "min": [-1408.5860595703125, -104.82209777832031], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1450112, "componentType": 5126, "count": 14458, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1981308, "componentType": 5125, "count": 20388, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3418248, "componentType": 5126, "count": 544, "max": [0.13081468641757965, 0.029046297073364258, -0.04767131805419922], "min": [-0.12845511734485626, 0.025084257125854492, -0.061276912689208984], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3424776, "componentType": 5126, "count": 544, "max": [0.0015732760075479746, 0.9614576697349548, 0.28182297945022583], "min": [-0.0014127769973129034, 0.9594664573669434, 0.27495312690734863], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1565776, "componentType": 5126, "count": 544, "max": [0.589084804058075, 0.6838312149047852], "min": [0.588607907295227, 0.683357834815979], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1570128, "componentType": 5126, "count": 544, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2062860, "componentType": 5125, "count": 546, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3431304, "componentType": 5126, "count": 606, "max": [0.0748838484287262, 0.02033090591430664, 0.008195161819458008], "min": [-0.07320225238800049, -0.0280611515045166, -0.007584810256958008], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3438576, "componentType": 5126, "count": 606, "max": [0.9996356964111328, 0.9979923367500305, 0.997433602809906], "min": [-0.9999481439590454, -0.15242692828178406, -0.9969754815101624], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1574480, "componentType": 5126, "count": 606, "max": [0.5895450115203857, 0.683268129825592], "min": [0.5879126787185669, 0.6830942034721375], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1579328, "componentType": 5126, "count": 606, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2065044, "componentType": 5125, "count": 2475, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3445848, "componentType": 5126, "count": 2740, "max": [0.9700931906700134, 0.515640139579773, 0.30074554681777954], "min": [-0.9714488387107849, -0.16461896896362305, -0.2086591273546219], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3478728, "componentType": 5126, "count": 2740, "max": [0.9992455840110779, 0.9999889731407166, 0.9999380707740784], "min": [-0.9992446899414062, -0.9998431205749512, -0.9954769611358643], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1584176, "componentType": 5126, "count": 2740, "max": [0.09367769211530685, 0.687664806842804], "min": [0.020312780514359474, 0.31400230526924133], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1606096, "componentType": 5126, "count": 2740, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2074944, "componentType": 5125, "count": 11580, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3511608, "componentType": 5126, "count": 28181, "max": [0.7087326645851135, 0.05330920219421387, 0.034894973039627075], "min": [-0.7135056257247925, -0.14193987846374512, -0.1967095285654068], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3849780, "componentType": 5126, "count": 28181, "max": [0.875774621963501, 0.997123122215271, 0.9999647736549377], "min": [-0.875774621963501, -0.997123122215271, -0.9999648332595825], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1628016, "componentType": 5126, "count": 28181, "max": [0.9745999574661255, 0.9921000003814697], "min": [0.3876953125, 0.7626953125], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1853464, "componentType": 5126, "count": 28181, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2121264, "componentType": 5125, "count": 36054, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4187952, "componentType": 5126, "count": 5184, "max": [0.7895490527153015, 0.17155909538269043, 0.028037160634994507], "min": [-0.7919772267341614, -0.1122748851776123, -0.09586381912231445], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4250160, "componentType": 5126, "count": 5184, "max": [0.9990048408508301, 0.9999839663505554, 0.9997631311416626], "min": [-0.9990048408508301, -0.999984085559845, -0.9997631311416626], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2078912, "componentType": 5126, "count": 5184, "max": [0.5907025337219238, 0.688096821308136], "min": [0.5906521081924438, 0.6859641075134277], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2120384, "componentType": 5126, "count": 5184, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2265480, "componentType": 5125, "count": 17826, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4312368, "componentType": 5126, "count": 3220, "max": [0.9829952716827393, 0.4496924877166748, 0.1394343376159668], "min": [-0.9829943776130676, -0.5731016397476196, -0.385803759098053], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4351008, "componentType": 5126, "count": 3220, "max": [0.9991193413734436, 1.0, 0.9996308088302612], "min": [-0.9991188645362854, -0.9999934434890747, -0.9856091737747192], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2161856, "componentType": 5126, "count": 3220, "max": [0.9753928184509277, 0.6621097326278687], "min": [0.8995468020439148, 0.3365150988101959], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2187616, "componentType": 5126, "count": 3220, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2336784, "componentType": 5125, "count": 17808, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4389648, "componentType": 5126, "count": 676, "max": [0.7344321012496948, 0.06340694427490234, 0.08392965793609619], "min": [-0.7332066297531128, -0.19937610626220703, -0.1396905779838562], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4397760, "componentType": 5126, "count": 676, "max": [0.9980766773223877, 0.9976080656051636, 0.9545304179191589], "min": [-0.9980753064155579, -0.976584792137146, -0.9983677864074707], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2213376, "componentType": 5126, "count": 676, "max": [0.8481451272964478, 0.17285199463367462], "min": [0.2624509930610657, 0.006347655784338713], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2218784, "componentType": 5126, "count": 676, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2408016, "componentType": 5125, "count": 2748, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4405872, "componentType": 5126, "count": 105, "max": [0.08898045122623444, 0.02718377113342285, 0.08895248174667358], "min": [-0.08898511528968811, -0.027077198028564453, -0.08860182762145996], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4407132, "componentType": 5126, "count": 105, "max": [0.0004904048983007669, -0.9562736749649048, 0.29247337579727173], "min": [-0.0004901437205262482, -0.9564838409423828, 0.2917852997779846], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2224192, "componentType": 5126, "count": 105, "max": [1.057081937789917, 1.1119019985198975], "min": [-0.05861181020736694, -0.0037918088492006063], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2419008, "componentType": 5125, "count": 105, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4408392, "componentType": 5126, "count": 61, "max": [0.042532503604888916, 0.0033075809478759766, 0.04155987501144409], "min": [-0.04302459955215454, -0.004621267318725586, -0.0416085422039032], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4409124, "componentType": 5126, "count": 61, "max": [0.22570300102233887, -0.9735711812973022, 0.16105090081691742], "min": [-0.09507343173027039, -0.9983053207397461, -0.1696474552154541], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2225032, "componentType": 5126, "count": 61, "max": [0.17562679946422577, 0.5499337315559387], "min": [0.05329316109418869, 0.34438639879226685], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2225520, "componentType": 5126, "count": 61, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2419428, "componentType": 5125, "count": 240, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4409856, "componentType": 5126, "count": 106, "max": [0.04086190462112427, 0.1119081974029541, 0.040381550788879395], "min": [-0.04271489381790161, -0.025874614715576172, -0.040114760398864746], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4411128, "componentType": 5126, "count": 106, "max": [0.99750816822052, 0.0680268332362175, 0.9999518990516663], "min": [-0.9969709515571594, -0.9997916221618652, -0.9976651668548584], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2226008, "componentType": 5126, "count": 106, "max": [0.9128760099411011, 0.9594699740409851], "min": [0.7412459850311279, 0.7902902364730835], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2226856, "componentType": 5126, "count": 106, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2420388, "componentType": 5125, "count": 456, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4412400, "componentType": 5126, "count": 106, "max": [0.04271489381790161, 0.1119077205657959, 0.04038149118423462], "min": [-0.04086160659790039, -0.025875091552734375, -0.04011482000350952], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4413672, "componentType": 5126, "count": 106, "max": [0.9969715476036072, 0.06802769750356674, 0.9999521374702454], "min": [-0.9975090622901917, -0.9997904300689697, -0.9976633787155151], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2227704, "componentType": 5126, "count": 106, "max": [0.9122495055198669, 0.9610859155654907], "min": [0.7415459752082825, 0.7896841168403625], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2228552, "componentType": 5126, "count": 106, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2422212, "componentType": 5125, "count": 456, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4414944, "componentType": 5126, "count": 61, "max": [0.04302448034286499, 0.003307342529296875, 0.04155987501144409], "min": [-0.04253280162811279, -0.0046215057373046875, -0.0416085422039032], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4415676, "componentType": 5126, "count": 61, "max": [0.09507663547992706, -0.9735661149024963, 0.16105040907859802], "min": [-0.22570611536502838, -0.9983054399490356, -0.1696697324514389], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2229400, "componentType": 5126, "count": 61, "max": [0.17599540948867798, 0.5499337315559387], "min": [0.05366116017103195, 0.34438639879226685], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2229888, "componentType": 5126, "count": 61, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2424036, "componentType": 5125, "count": 240, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4416408, "componentType": 5126, "count": 207, "max": [-0.03988013416528702, 0.023962123319506645, 0.029311643913388252], "min": [-0.0401710644364357, -0.034933216869831085, -0.02925342693924904], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4418892, "componentType": 5126, "count": 207, "max": [1.0, 0.11755228787660599, 0.11235949397087097], "min": [0.9930157661437988, -0.112766794860363, -0.11297082155942917], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2230376, "componentType": 5126, "count": 207, "max": [1.0002520084381104, 1.0007799863815308], "min": [-6.783010030630976e-05, 0.006074010860174894], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2424996, "componentType": 5125, "count": 1056, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4421376, "componentType": 5126, "count": 20097, "max": [0.06934200972318649, 0.2720867395401001, 0.277377188205719], "min": [-0.23856303095817566, -0.283051460981369, -0.27731773257255554], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4662540, "componentType": 5126, "count": 20097, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2232032, "componentType": 5126, "count": 20097, "max": [0.9853109121322632, 0.9633392095565796], "min": [0.02205180935561657, 0.3000394105911255], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2429220, "componentType": 5125, "count": 57624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4903704, "componentType": 5126, "count": 424, "max": [0.06345251202583313, 0.0022170543670654297, 0.010703682899475098], "min": [-0.004630565643310547, -0.014932870864868164, -0.01312631368637085], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4908792, "componentType": 5126, "count": 424, "max": [0.9954850673675537, 0.9994182586669922, 0.9935023784637451], "min": [-0.9956610202789307, -0.3554699122905731, -0.9925320744514465], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2392808, "componentType": 5126, "count": 424, "max": [0.5890253782272339, 0.6841952204704285], "min": [0.5882750153541565, 0.6839324831962585], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2396200, "componentType": 5126, "count": 424, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2659716, "componentType": 5125, "count": 1905, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4913880, "componentType": 5126, "count": 327, "max": [-0.01052597165107727, 0.011089086532592773, 0.012012720108032227], "min": [-0.05334088206291199, -0.0011429786682128906, -0.006852269172668457], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4917804, "componentType": 5126, "count": 327, "max": [0.9931724667549133, 0.9981715083122253, 0.9919145703315735], "min": [-0.9965185523033142, -0.29084914922714233, -0.9855557084083557], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2399592, "componentType": 5126, "count": 327, "max": [0.5062999725341797, 0.9027999639511108], "min": [0.36890000104904175, 0.8494001030921936], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2402208, "componentType": 5126, "count": 327, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2667336, "componentType": 5125, "count": 1539, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4921728, "componentType": 5126, "count": 720, "max": [0.15170884132385254, 0.10447907447814941, 0.054229736328125], "min": [-0.14713305234909058, -0.26521992683410645, -0.05645236372947693], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4930368, "componentType": 5126, "count": 720, "max": [0.9999068975448608, 0.9976692199707031, 0.9995579123497009], "min": [-0.9999476075172424, -0.7403186559677124, -0.9995660781860352], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2404824, "componentType": 5126, "count": 720, "max": [26.505599975585938, 7.051699638366699], "min": [0.0503997802734375, 0.03390001878142357], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2410584, "componentType": 5126, "count": 720, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2673492, "componentType": 5125, "count": 4080, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4939008, "componentType": 5126, "count": 160, "max": [0.11704564094543457, 0.08302807807922363, 0.03466722369194031], "min": [-0.1256016492843628, -0.0532839298248291, -0.032857269048690796], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4940928, "componentType": 5126, "count": 160, "max": [0.9880723357200623, 0.9892257452011108, 0.9975393414497375], "min": [-0.9907561540603638, -0.18428707122802734, -0.9972130656242371], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2416344, "componentType": 5126, "count": 160, "max": [26.479799270629883, 7.026599884033203], "min": [0.07749997824430466, 0.058899808675050735], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2417624, "componentType": 5126, "count": 160, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2689812, "componentType": 5125, "count": 828, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4942848, "componentType": 5126, "count": 720, "max": [0.1467973291873932, 0.10451841354370117, 0.05430489778518677], "min": [-0.15203601121902466, -0.26599574089050293, -0.05637720227241516], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4951488, "componentType": 5126, "count": 720, "max": [0.9999476075172424, 0.9976692795753479, 0.9978863596916199], "min": [-0.9999067783355713, -0.7403127551078796, -0.9983226656913757], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2418904, "componentType": 5126, "count": 720, "max": [0.06546103209257126, 0.6531810164451599], "min": [0.0369185209274292, 0.617792010307312], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2424664, "componentType": 5126, "count": 720, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2693124, "componentType": 5125, "count": 4080, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4960128, "componentType": 5126, "count": 160, "max": [0.12526661157608032, 0.08306741714477539, 0.034742385149002075], "min": [-0.1173740029335022, -0.053244590759277344, -0.03278210759162903], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4962048, "componentType": 5126, "count": 160, "max": [0.9907567501068115, 0.9892283082008362, 0.9975398182868958], "min": [-0.9880746603012085, -0.1842721849679947, -0.9976381659507751], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2430424, "componentType": 5126, "count": 160, "max": [26.479799270629883, 7.026599884033203], "min": [0.07749997824430466, 0.058899808675050735], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2431704, "componentType": 5126, "count": 160, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2709444, "componentType": 5125, "count": 828, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4963968, "componentType": 5126, "count": 6684, "max": [0.3883117139339447, 0.2503212094306946, 0.38555437326431274], "min": [-0.38402143120765686, -0.0785524770617485, -0.3867405652999878], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5044176, "componentType": 5126, "count": 6684, "max": [0.9971345663070679, 0.9998342990875244, 0.9982646703720093], "min": [-0.9978967308998108, -0.9998297095298767, -0.9983161091804504], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2432984, "componentType": 5126, "count": 6684, "max": [0.9896999597549438, 0.9669117331504822], "min": [0.04797062277793884, 0.011577246710658073], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2486456, "componentType": 5126, "count": 6684, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2539928, "componentType": 5126, "count": 6684, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2712756, "componentType": 5125, "count": 38343, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5124384, "componentType": 5126, "count": 620, "max": [0.19936160743236542, 0.17527948319911957, 0.19697609543800354], "min": [-0.19387568533420563, 0.12971393764019012, -0.1970442682504654], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5131824, "componentType": 5126, "count": 620, "max": [0.996924102306366, 0.9993769526481628, 0.9993768334388733], "min": [-0.9969239234924316, -0.6549900770187378, -0.9993768334388733], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2593400, "componentType": 5126, "count": 620, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2598360, "componentType": 5126, "count": 620, "max": [0.9259564280509949, 0.9238094091415405], "min": [0.012403899803757668, 0.0017983909929171205], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2603320, "componentType": 5126, "count": 620, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2866128, "componentType": 5125, "count": 840, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5139264, "componentType": 5126, "count": 1954, "max": [0.22168858349323273, 0.20644396543502808, 0.12496834993362427], "min": [-0.11631884425878525, 0.06746149808168411, -0.12100843340158463], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5162712, "componentType": 5126, "count": 1954, "max": [0.9999884963035583, 0.9994454383850098, 0.9994524121284485], "min": [-0.9999869465827942, -0.999377429485321, -0.9995720386505127], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2608280, "componentType": 5126, "count": 1954, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2623912, "componentType": 5126, "count": 1954, "max": [0.3277952969074249, 0.5195990800857544], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2639544, "componentType": 5126, "count": 1954, "max": [0.887326717376709, 0.5190761089324951], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2869488, "componentType": 5125, "count": 3630, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5186160, "componentType": 5126, "count": 520, "max": [0.1257174164056778, 0.14092020690441132, 0.12225595861673355], "min": [-0.12023117393255234, 0.1299082636833191, -0.12362174689769745], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5192400, "componentType": 5126, "count": 520, "max": [1.0, 0.9993769526481628, 0.9993767738342285], "min": [-1.0, -0.03530022129416466, -0.9993767738342285], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2655176, "componentType": 5126, "count": 520, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2659336, "componentType": 5126, "count": 520, "max": [0.04820716008543968, 0.5216919779777527], "min": [0.04126273840665817, 0.5147476196289062], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2663496, "componentType": 5126, "count": 520, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2884008, "componentType": 5125, "count": 840, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5198640, "componentType": 5126, "count": 3339, "max": [0.21900582313537598, 0.23380035161972046, 0.16508466005325317], "min": [0.13618908822536469, 0.06006946414709091, -0.17075017094612122], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5238708, "componentType": 5126, "count": 3339, "max": [0.9999997615814209, 0.999999463558197, 0.9998907446861267], "min": [-1.0, -0.9999821186065674, -0.9998880624771118], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2667656, "componentType": 5126, "count": 3339, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2694368, "componentType": 5126, "count": 3339, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2721080, "componentType": 5126, "count": 3339, "max": [0.7399219274520874, 0.5210670232772827], "min": [0.7366077899932861, 0.5076485872268677], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2887368, "componentType": 5125, "count": 10416, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5278776, "componentType": 5126, "count": 207, "max": [-0.03988013416528702, 0.023962123319506645, 0.029311643913388252], "min": [-0.0401710644364357, -0.034933216869831085, -0.02925342693924904], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5281260, "componentType": 5126, "count": 207, "max": [1.0, 0.11755228787660599, 0.11235949397087097], "min": [0.9930157661437988, -0.112766794860363, -0.11297082155942917], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2747792, "componentType": 5126, "count": 207, "max": [1.0002520084381104, 1.0007799863815308], "min": [-6.783010030630976e-05, 0.006074010860174894], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2929032, "componentType": 5125, "count": 1056, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5283744, "componentType": 5126, "count": 20097, "max": [0.06934200972318649, 0.2720867395401001, 0.277377188205719], "min": [-0.23856303095817566, -0.283051460981369, -0.27731773257255554], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5524908, "componentType": 5126, "count": 20097, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2749448, "componentType": 5126, "count": 20097, "max": [0.9853109121322632, 0.9633392095565796], "min": [0.02205180935561657, 0.3000394105911255], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2933256, "componentType": 5125, "count": 57624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5766072, "componentType": 5126, "count": 6684, "max": [0.3883117139339447, 0.2503212094306946, 0.38555437326431274], "min": [-0.38402143120765686, -0.0785524770617485, -0.3867405652999878], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5846280, "componentType": 5126, "count": 6684, "max": [0.9971345663070679, 0.9998342990875244, 0.9982646703720093], "min": [-0.9978967308998108, -0.9998297095298767, -0.9983161091804504], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2910224, "componentType": 5126, "count": 6684, "max": [0.9896999597549438, 0.9669117331504822], "min": [0.04797062277793884, 0.011577246710658073], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2963696, "componentType": 5126, "count": 6684, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3017168, "componentType": 5126, "count": 6684, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3163752, "componentType": 5125, "count": 38343, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5926488, "componentType": 5126, "count": 620, "max": [0.19936160743236542, 0.17527948319911957, 0.19697609543800354], "min": [-0.19387568533420563, 0.12971393764019012, -0.1970442682504654], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5933928, "componentType": 5126, "count": 620, "max": [0.996924102306366, 0.9993769526481628, 0.9993768334388733], "min": [-0.9969239234924316, -0.6549900770187378, -0.9993768334388733], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3070640, "componentType": 5126, "count": 620, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3075600, "componentType": 5126, "count": 620, "max": [0.9259564280509949, 0.9238094091415405], "min": [0.012403899803757668, 0.0017983909929171205], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3080560, "componentType": 5126, "count": 620, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3317124, "componentType": 5125, "count": 840, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5941368, "componentType": 5126, "count": 1954, "max": [0.22168858349323273, 0.20644396543502808, 0.12496834993362427], "min": [-0.11631884425878525, 0.06746149808168411, -0.12100843340158463], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5964816, "componentType": 5126, "count": 1954, "max": [0.9999884963035583, 0.9994454383850098, 0.9994524121284485], "min": [-0.9999869465827942, -0.999377429485321, -0.9995720386505127], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3085520, "componentType": 5126, "count": 1954, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3101152, "componentType": 5126, "count": 1954, "max": [0.3277952969074249, 0.5195990800857544], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3116784, "componentType": 5126, "count": 1954, "max": [0.887326717376709, 0.5190761089324951], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3320484, "componentType": 5125, "count": 3630, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5988264, "componentType": 5126, "count": 520, "max": [0.1257174164056778, 0.14092020690441132, 0.12225595861673355], "min": [-0.12023117393255234, 0.1299082636833191, -0.12362174689769745], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5994504, "componentType": 5126, "count": 520, "max": [1.0, 0.9993769526481628, 0.9993767738342285], "min": [-1.0, -0.03530022129416466, -0.9993767738342285], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3132416, "componentType": 5126, "count": 520, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3136576, "componentType": 5126, "count": 520, "max": [0.04820716008543968, 0.5216919779777527], "min": [0.04126273840665817, 0.5147476196289062], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3140736, "componentType": 5126, "count": 520, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3335004, "componentType": 5125, "count": 840, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6000744, "componentType": 5126, "count": 3339, "max": [0.21900582313537598, 0.23380035161972046, 0.16508466005325317], "min": [0.13618908822536469, 0.06006946414709091, -0.17075017094612122], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6040812, "componentType": 5126, "count": 3339, "max": [0.9999997615814209, 0.999999463558197, 0.9998907446861267], "min": [-1.0, -0.9999821186065674, -0.9998880624771118], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3144896, "componentType": 5126, "count": 3339, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3171608, "componentType": 5126, "count": 3339, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3198320, "componentType": 5126, "count": 3339, "max": [0.7399219274520874, 0.5210670232772827], "min": [0.7366077899932861, 0.5076485872268677], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3338364, "componentType": 5125, "count": 10416, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6080880, "componentType": 5126, "count": 207, "max": [-0.03988013416528702, 0.023962123319506645, 0.029311643913388252], "min": [-0.0401710644364357, -0.034933216869831085, -0.02925342693924904], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6083364, "componentType": 5126, "count": 207, "max": [1.0, 0.11755228787660599, 0.11235949397087097], "min": [0.9930157661437988, -0.112766794860363, -0.11297082155942917], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3225032, "componentType": 5126, "count": 207, "max": [1.0002520084381104, 1.0007799863815308], "min": [-6.783010030630976e-05, 0.006074010860174894], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3380028, "componentType": 5125, "count": 1056, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6085848, "componentType": 5126, "count": 20097, "max": [0.06934200972318649, 0.2720867395401001, 0.277377188205719], "min": [-0.23856303095817566, -0.283051460981369, -0.27731773257255554], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6327012, "componentType": 5126, "count": 20097, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3226688, "componentType": 5126, "count": 20097, "max": [0.9853109121322632, 0.9633392095565796], "min": [0.02205180935561657, 0.3000394105911255], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3384252, "componentType": 5125, "count": 57624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6568176, "componentType": 5126, "count": 6684, "max": [0.3883117139339447, 0.2503212094306946, 0.38555437326431274], "min": [-0.38402143120765686, -0.0785524770617485, -0.3867405652999878], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6648384, "componentType": 5126, "count": 6684, "max": [0.9971345663070679, 0.9998342990875244, 0.9982646703720093], "min": [-0.9978967308998108, -0.9998297095298767, -0.9983161091804504], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3387464, "componentType": 5126, "count": 6684, "max": [0.9896999597549438, 0.9669117331504822], "min": [0.04797062277793884, 0.011577246710658073], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3440936, "componentType": 5126, "count": 6684, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3494408, "componentType": 5126, "count": 6684, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3614748, "componentType": 5125, "count": 38343, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6728592, "componentType": 5126, "count": 620, "max": [0.19936160743236542, 0.17527948319911957, 0.19697609543800354], "min": [-0.19387568533420563, 0.12971393764019012, -0.1970442682504654], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6736032, "componentType": 5126, "count": 620, "max": [0.996924102306366, 0.9993769526481628, 0.9993768334388733], "min": [-0.9969239234924316, -0.6549900770187378, -0.9993768334388733], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3547880, "componentType": 5126, "count": 620, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3552840, "componentType": 5126, "count": 620, "max": [0.9259564280509949, 0.9238094091415405], "min": [0.012403899803757668, 0.0017983909929171205], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3557800, "componentType": 5126, "count": 620, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3768120, "componentType": 5125, "count": 840, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6743472, "componentType": 5126, "count": 1954, "max": [0.22168858349323273, 0.20644396543502808, 0.12496834993362427], "min": [-0.11631884425878525, 0.06746149808168411, -0.12100843340158463], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6766920, "componentType": 5126, "count": 1954, "max": [0.9999884963035583, 0.9994454383850098, 0.9994524121284485], "min": [-0.9999869465827942, -0.999377429485321, -0.9995720386505127], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3562760, "componentType": 5126, "count": 1954, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3578392, "componentType": 5126, "count": 1954, "max": [0.3277952969074249, 0.5195990800857544], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3594024, "componentType": 5126, "count": 1954, "max": [0.887326717376709, 0.5190761089324951], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3771480, "componentType": 5125, "count": 3630, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6790368, "componentType": 5126, "count": 520, "max": [0.1257174164056778, 0.14092020690441132, 0.12225595861673355], "min": [-0.12023117393255234, 0.1299082636833191, -0.12362174689769745], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6796608, "componentType": 5126, "count": 520, "max": [1.0, 0.9993769526481628, 0.9993767738342285], "min": [-1.0, -0.03530022129416466, -0.9993767738342285], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3609656, "componentType": 5126, "count": 520, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3613816, "componentType": 5126, "count": 520, "max": [0.04820716008543968, 0.5216919779777527], "min": [0.04126273840665817, 0.5147476196289062], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3617976, "componentType": 5126, "count": 520, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3786000, "componentType": 5125, "count": 840, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6802848, "componentType": 5126, "count": 3339, "max": [0.21900582313537598, 0.23380035161972046, 0.16508466005325317], "min": [0.13618908822536469, 0.06006946414709091, -0.17075017094612122], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6842916, "componentType": 5126, "count": 3339, "max": [0.9999997615814209, 0.999999463558197, 0.9998907446861267], "min": [-1.0, -0.9999821186065674, -0.9998880624771118], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3622136, "componentType": 5126, "count": 3339, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3648848, "componentType": 5126, "count": 3339, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3675560, "componentType": 5126, "count": 3339, "max": [0.7399219274520874, 0.5210670232772827], "min": [0.7366077899932861, 0.5076485872268677], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3789360, "componentType": 5125, "count": 10416, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6882984, "componentType": 5126, "count": 207, "max": [-0.03988013416528702, 0.023962123319506645, 0.029311643913388252], "min": [-0.0401710644364357, -0.034933216869831085, -0.02925342693924904], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6885468, "componentType": 5126, "count": 207, "max": [1.0, 0.11755228787660599, 0.11235949397087097], "min": [0.9930157661437988, -0.112766794860363, -0.11297082155942917], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3702272, "componentType": 5126, "count": 207, "max": [1.0002520084381104, 1.0007799863815308], "min": [-6.783010030630976e-05, 0.006074010860174894], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3831024, "componentType": 5125, "count": 1056, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6887952, "componentType": 5126, "count": 20097, "max": [0.06934200972318649, 0.2720867395401001, 0.277377188205719], "min": [-0.23856303095817566, -0.283051460981369, -0.27731773257255554], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7129116, "componentType": 5126, "count": 20097, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3703928, "componentType": 5126, "count": 20097, "max": [0.9853109121322632, 0.9633392095565796], "min": [0.02205180935561657, 0.3000394105911255], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3835248, "componentType": 5125, "count": 57624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7370280, "componentType": 5126, "count": 6684, "max": [0.3883117139339447, 0.2503212094306946, 0.38555437326431274], "min": [-0.38402143120765686, -0.0785524770617485, -0.3867405652999878], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7450488, "componentType": 5126, "count": 6684, "max": [0.9971345663070679, 0.9998342990875244, 0.9982646703720093], "min": [-0.9978967308998108, -0.9998297095298767, -0.9983161091804504], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3864704, "componentType": 5126, "count": 6684, "max": [0.9896999597549438, 0.9669117331504822], "min": [0.04797062277793884, 0.011577246710658073], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3918176, "componentType": 5126, "count": 6684, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 3971648, "componentType": 5126, "count": 6684, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4065744, "componentType": 5125, "count": 38343, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7530696, "componentType": 5126, "count": 620, "max": [0.19936160743236542, 0.17527948319911957, 0.19697609543800354], "min": [-0.19387568533420563, 0.12971393764019012, -0.1970442682504654], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7538136, "componentType": 5126, "count": 620, "max": [0.996924102306366, 0.9993769526481628, 0.9993768334388733], "min": [-0.9969239234924316, -0.6549900770187378, -0.9993768334388733], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4025120, "componentType": 5126, "count": 620, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 4030080, "componentType": 5126, "count": 620, "max": [0.9259564280509949, 0.9238094091415405], "min": [0.012403899803757668, 0.0017983909929171205], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 4035040, "componentType": 5126, "count": 620, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4219116, "componentType": 5125, "count": 840, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7545576, "componentType": 5126, "count": 1954, "max": [0.22168858349323273, 0.20644396543502808, 0.12496834993362427], "min": [-0.11631884425878525, 0.06746149808168411, -0.12100843340158463], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7569024, "componentType": 5126, "count": 1954, "max": [0.9999884963035583, 0.9994454383850098, 0.9994524121284485], "min": [-0.9999869465827942, -0.999377429485321, -0.9995720386505127], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4040000, "componentType": 5126, "count": 1954, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 4055632, "componentType": 5126, "count": 1954, "max": [0.3277952969074249, 0.5195990800857544], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 4071264, "componentType": 5126, "count": 1954, "max": [0.887326717376709, 0.5190761089324951], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4222476, "componentType": 5125, "count": 3630, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7592472, "componentType": 5126, "count": 520, "max": [0.1257174164056778, 0.14092020690441132, 0.12225595861673355], "min": [-0.12023117393255234, 0.1299082636833191, -0.12362174689769745], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7598712, "componentType": 5126, "count": 520, "max": [1.0, 0.9993769526481628, 0.9993767738342285], "min": [-1.0, -0.03530022129416466, -0.9993767738342285], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4086896, "componentType": 5126, "count": 520, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 4091056, "componentType": 5126, "count": 520, "max": [0.04820716008543968, 0.5216919779777527], "min": [0.04126273840665817, 0.5147476196289062], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 4095216, "componentType": 5126, "count": 520, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4236996, "componentType": 5125, "count": 840, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7604952, "componentType": 5126, "count": 3339, "max": [0.21900582313537598, 0.23380035161972046, 0.16508466005325317], "min": [0.13618908822536469, 0.06006946414709091, -0.17075017094612122], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7645020, "componentType": 5126, "count": 3339, "max": [0.9999997615814209, 0.999999463558197, 0.9998907446861267], "min": [-1.0, -0.9999821186065674, -0.9998880624771118], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4099376, "componentType": 5126, "count": 3339, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 4126088, "componentType": 5126, "count": 3339, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 4152800, "componentType": 5126, "count": 3339, "max": [0.7399219274520874, 0.5210670232772827], "min": [0.7366077899932861, 0.5076485872268677], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4240356, "componentType": 5125, "count": 10416, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7685088, "componentType": 5126, "count": 43, "max": [-0.6508062481880188, -0.7197089195251465, 0.11721134185791016], "min": [-0.8264563083648682, -1.4470698833465576, -0.25044965744018555], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7685604, "componentType": 5126, "count": 43, "max": [0.40693458914756775, -0.34357139468193054, 0.9389656186103821], "min": [-0.04588765278458595, -0.6913117170333862, 0.7214155793190002], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4179512, "componentType": 5126, "count": 43, "max": [3.4302310943603516, 10.724559783935547], "min": [-9.938139915466309, 8.575637817382812], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4282020, "componentType": 5125, "count": 156, "type": "SCALAR"}], "asset": {"extras": {"author": "Black Snow (https://sketchfab.com/BlackSnow02)", "license": "CC-BY-4.0 (http://creativecommons.org/licenses/by/4.0/)", "source": "https://sketchfab.com/3d-models/mercedes-benz-cls-w219-3b11611c6d06415b82b96495f5f249ce", "title": "Mercedes-Benz CLS [w219]"}, "generator": "Sketchfab-16.64.0", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 4282644, "name": "floatBufferViews", "target": 34963}, {"buffer": 0, "byteLength": 4179856, "byteOffset": 4282644, "byteStride": 8, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 7686120, "byteOffset": 8462500, "byteStride": 12, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 168192, "byteOffset": 16148620, "byteStride": 16, "name": "floatBufferViews", "target": 34962}], "buffers": [{"byteLength": 16316812, "uri": "scene.bin"}], "materials": [{"doubleSided": true, "name": "CLS500w219_black_glass3_int", "pbrMetallicRoughness": {"baseColorFactor": [0.007540174908580951, 0.007540174908580951, 0.007540174908580951, 1.0], "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "licenseplate-52-11", "pbrMetallicRoughness": {"baseColorFactor": [0.0856112, 0.0856112, 0.0856112, 1.0], "roughnessFactor": 0.4041953502244183}}, {"doubleSided": true, "name": "CLS500_placticc", "pbrMetallicRoughness": {"baseColorFactor": [0.0241143, 0.0241143, 0.0241143, 1.0], "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "etk8000", "pbrMetallicRoughness": {"baseColorFactor": [0.0407726, 0.0407726, 0.0407726, 1.0], "metallicFactor": 0.8520756624732002, "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "CLS500_grille", "pbrMetallicRoughness": {"baseColorFactor": [0.0, 0.0, 0.0, 1.0], "metallicFactor": 0.8459739716946855, "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "vehicle_mesh", "pbrMetallicRoughness": {"baseColorFactor": [0.0381478, 0.0381478, 0.0381478, 1.0], "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "CLS500_plastick", "pbrMetallicRoughness": {"baseColorFactor": [0.0104602, 0.0104602, 0.0104602, 1.0], "metallicFactor": 0.5408894327689492, "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "sunburst", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.8, 0.8, 1.0], "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "CLS500_body", "pbrMetallicRoughness": {"baseColorFactor": [0.010085079892761884, 0.010085079892761884, 0.010085079892761884, 1.0], "metallicFactor": 0.21139813072915395, "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "CLS500_kovrolin", "pbrMetallicRoughness": {"baseColorFactor": [0.0, 0.0, 0.0, 1.0], "metallicFactor": 0.0}}, {"doubleSided": true, "name": "CLS500_mirrorglass", "pbrMetallicRoughness": {"baseColorFactor": [0.10879, 0.10879, 0.10879, 1.0], "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "bastion", "pbrMetallicRoughness": {"baseColorFactor": [0.0, 0.0, 0.0, 1.0], "metallicFactor": 0.0}}, {"doubleSided": true, "name": "CLS500w219_signal_R", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.8, 0.8, 1.0], "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "CLS500w219_signal_L", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.8, 0.8, 1.0], "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "CLS500w219_highbeam", "pbrMetallicRoughness": {"baseColorFactor": [0.3953630024459208, 0.5665832716247717, 0.7669167817792903, 1.0], "roughnessFactor": 0.0}}, {"alphaMode": "BLEND", "doubleSided": true, "name": "etk800_glass", "pbrMetallicRoughness": {"baseColorFactor": [0.270697183511025, 0.270697183511025, 0.270697183511025, 0.699533393010332], "roughnessFactor": 0.0}}, {"alphaMode": "BLEND", "doubleSided": true, "name": "CLS500_tailglass", "pbrMetallicRoughness": {"baseColorFactor": [0.06586117413768745, 0.0, 0.0, 0.8032621362450824], "metallicFactor": 0.6507198667822143, "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "dark_misc", "pbrMetallicRoughness": {"baseColorFactor": [0.64, 0.64, 0.64, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.8568916494400134}}, {"doubleSided": true, "name": "CLS500w219_lowbeam", "pbrMetallicRoughness": {"roughnessFactor": 0.0}}, {"doubleSided": true, "name": "CLS500_chromee", "pbrMetallicRoughness": {"baseColorFactor": [0.941429385806688, 0.941429385806688, 0.941429385806688, 1.0], "metallicFactor": 0.9802111688220094, "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "mirror", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.8, 0.8, 1.0], "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "CLS500_wood", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.8, 0.8, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.7743695641788744}}, {"doubleSided": true, "name": "CLS500_logo", "pbrMetallicRoughness": {"baseColorFactor": [0.03384827750966822, 0.03384827750966822, 0.03384827750966822, 1.0], "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "bastion_engine", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.8, 0.8, 1.0], "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "CLS500w219_mechanical", "pbrMetallicRoughness": {"baseColorFactor": [0.0113013, 0.0113013, 0.0113013, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.9974729524965882}}, {"doubleSided": true, "name": "grille_hex", "pbrMetallicRoughness": {"baseColorFactor": [0.0327494, 0.0327494, 0.0327494, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.9926770115377669}}, {"doubleSided": true, "name": "CLS500w219_foglights", "pbrMetallicRoughness": {"roughnessFactor": 0.0}}, {"doubleSided": true, "name": "CLS500_wheelvolklogo", "pbrMetallicRoughness": {"baseColorFactor": [0.049567266760328785, 0.049567266760328785, 0.049567266760328785, 1.0], "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "CLS500_wheal_white", "pbrMetallicRoughness": {"baseColorFactor": [0.29482672106467883, 0.29482672106467883, 0.29482672106467883, 1.0], "metallicFactor": 0.6202114128896405, "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "CLS500_placticc_red", "pbrMetallicRoughness": {"baseColorFactor": [0.0184444, 0.0184444, 0.0184444, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.994167368592479}}, {"doubleSided": true, "name": "CLS500_blackfulle", "pbrMetallicRoughness": {"baseColorFactor": [0.0345364, 0.0345364, 0.0345364, 1.0], "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "Scene_-_Root.002", "pbrMetallicRoughness": {"baseColorFactor": [0.0, 0.0, 0.0, 1.0], "metallicFactor": 0.4188556171986546}}, {"doubleSided": true, "name": "amdb11_brake.002", "pbrMetallicRoughness": {"baseColorFactor": [0.116013, 0.116013, 0.116013, 1.0], "roughnessFactor": 0.9692024318163532}}, {"doubleSided": true, "name": "amdb11_misc_chrome.002", "pbrMetallicRoughness": {"baseColorFactor": [0.0486285, 0.0486285, 0.0486285, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.9846223180802502}}, {"doubleSided": true, "name": "amdb11_misc.002", "pbrMetallicRoughness": {"baseColorFactor": [0.0, 0.0, 0.0, 1.0], "metallicFactor": 0.0}}, {"doubleSided": true, "name": "amdb11_caliper.002", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 0.0, 0.0, 1.0], "metallicFactor": 0.8459739716946855, "roughnessFactor": 0.0}}, {"doubleSided": true, "name": "CLS500_leather_white", "pbrMetallicRoughness": {"baseColorFactor": [0.03638, 0.03638, 0.03638, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.9884956338723074}}], "meshes": [{"name": "CLS500w219_backlight_int_black_CLS500w219_black_glass3_int_0", "primitives": [{"attributes": {"NORMAL": 1, "POSITION": 0, "TEXCOORD_0": 2}, "indices": 3, "material": 0, "mode": 4}]}, {"name": "CLS500w219_doorglass_FL_int_black_CLS500w219_black_glass3_int_0", "primitives": [{"attributes": {"NORMAL": 5, "POSITION": 4, "TEXCOORD_0": 6}, "indices": 7, "material": 0, "mode": 4}]}, {"name": "CLS500w219_doorglass_FR_int_black_CLS500w219_black_glass3_int_0", "primitives": [{"attributes": {"NORMAL": 9, "POSITION": 8, "TEXCOORD_0": 10}, "indices": 11, "material": 0, "mode": 4}]}, {"name": "CLS500w219_doorglass_RL_int_black_CLS500w219_black_glass3_int_0", "primitives": [{"attributes": {"NORMAL": 13, "POSITION": 12, "TEXCOORD_0": 14}, "indices": 15, "material": 0, "mode": 4}]}, {"name": "CLS500w219_doorglass_RL_int2_black_CLS500w219_black_glass3_int_0", "primitives": [{"attributes": {"NORMAL": 17, "POSITION": 16, "TEXCOORD_0": 18}, "indices": 19, "material": 0, "mode": 4}]}, {"name": "CLS500w219_doorglass_RR_int_black_CLS500w219_black_glass3_int_0", "primitives": [{"attributes": {"NORMAL": 21, "POSITION": 20, "TEXCOORD_0": 22}, "indices": 23, "material": 0, "mode": 4}]}, {"name": "CLS500w219_doorglass_RR_int2_black_CLS500w219_black_glass3_int_0", "primitives": [{"attributes": {"NORMAL": 25, "POSITION": 24, "TEXCOORD_0": 26}, "indices": 27, "material": 0, "mode": 4}]}, {"name": "CLS500w219_windshield_int_black_CLS500w219_black_glass3_int_0", "primitives": [{"attributes": {"NORMAL": 29, "POSITION": 28, "TEXCOORD_0": 30}, "indices": 31, "material": 0, "mode": 4}]}, {"name": "licenseplate-52-11_F_licenseplate-52-11_0", "primitives": [{"attributes": {"COLOR_0": 34, "NORMAL": 33, "POSITION": 32, "TEXCOORD_0": 35}, "indices": 36, "material": 1, "mode": 4}]}, {"name": "number_ramka_f_CLS500_placticc_0", "primitives": [{"attributes": {"NORMAL": 38, "POSITION": 37, "TEXCOORD_0": 39, "TEXCOORD_1": 40}, "indices": 41, "material": 2, "mode": 4}]}, {"name": "number_ramka_f_etk8000_0", "primitives": [{"attributes": {"NORMAL": 43, "POSITION": 42, "TEXCOORD_0": 44, "TEXCOORD_1": 45}, "indices": 46, "material": 3, "mode": 4}]}, {"name": "licenseplate-52-11_R_licenseplate-52-11_0", "primitives": [{"attributes": {"COLOR_0": 49, "NORMAL": 48, "POSITION": 47, "TEXCOORD_0": 50}, "indices": 51, "material": 1, "mode": 4}]}, {"name": "CLS500w219_wiperL_CLS500_placticc_0", "primitives": [{"attributes": {"NORMAL": 53, "POSITION": 52, "TEXCOORD_0": 54}, "indices": 55, "material": 2, "mode": 4}]}, {"name": "CLS500w219_wiperR_CLS500_placticc_0", "primitives": [{"attributes": {"NORMAL": 57, "POSITION": 56, "TEXCOORD_0": 58}, "indices": 59, "material": 2, "mode": 4}]}, {"name": "CLS500w219_grille_two1_CLS500_grille_0", "primitives": [{"attributes": {"NORMAL": 61, "POSITION": 60, "TEXCOORD_0": 62, "TEXCOORD_1": 63}, "indices": 64, "material": 4, "mode": 4}]}, {"name": "skel_mesh.090_vehicle_mesh_0", "primitives": [{"attributes": {"COLOR_0": 67, "NORMAL": 66, "POSITION": 65, "TEXCOORD_0": 68, "TEXCOORD_1": 69}, "indices": 70, "material": 5, "mode": 4}]}, {"name": "CLS500w219_chassis_CLS500_placticc_0", "primitives": [{"attributes": {"NORMAL": 72, "POSITION": 71, "TEXCOORD_0": 73}, "indices": 74, "material": 2, "mode": 4}]}, {"name": "CLS500w219_chassis_CLS500_plastick_0", "primitives": [{"attributes": {"NORMAL": 76, "POSITION": 75, "TEXCOORD_0": 77}, "indices": 78, "material": 6, "mode": 4}]}, {"name": "sunburst_bumperbar_F_sunburst_0", "primitives": [{"attributes": {"NORMAL": 80, "POSITION": 79, "TEXCOORD_0": 81, "TEXCOORD_1": 82}, "indices": 83, "material": 7, "mode": 4}]}, {"name": "CLS500w219_z_chrome_etk8000_0", "primitives": [{"attributes": {"NORMAL": 85, "POSITION": 84, "TEXCOORD_0": 86}, "indices": 87, "material": 3, "mode": 4}]}, {"name": "CLS500w219_hood_CLS500_body_0", "primitives": [{"attributes": {"NORMAL": 89, "POSITION": 88, "TEXCOORD_0": 90}, "indices": 91, "material": 8, "mode": 4}]}, {"name": "CLS500w219_hood_CLS500_placticc_0", "primitives": [{"attributes": {"NORMAL": 93, "POSITION": 92, "TEXCOORD_0": 94}, "indices": 95, "material": 2, "mode": 4}]}, {"name": "CLS500w219_hood_CLS500_plastick_0", "primitives": [{"attributes": {"NORMAL": 97, "POSITION": 96, "TEXCOORD_0": 98}, "indices": 99, "material": 6, "mode": 4}]}, {"name": "CLS500w219_hood_CLS500_grille_0", "primitives": [{"attributes": {"NORMAL": 101, "POSITION": 100, "TEXCOORD_0": 102}, "indices": 103, "material": 4, "mode": 4}]}, {"name": "CLS500w219_trunk_CLS500_body_0", "primitives": [{"attributes": {"NORMAL": 105, "POSITION": 104, "TEXCOORD_0": 106}, "indices": 107, "material": 8, "mode": 4}]}, {"name": "CLS500w219_trunk_etk8000_0", "primitives": [{"attributes": {"NORMAL": 109, "POSITION": 108, "TEXCOORD_0": 110}, "indices": 111, "material": 3, "mode": 4}]}, {"name": "CLS500w219_trunk_CLS500_plastick_0", "primitives": [{"attributes": {"NORMAL": 113, "POSITION": 112, "TEXCOORD_0": 114}, "indices": 115, "material": 6, "mode": 4}]}, {"name": "CLS500w219_trunk_CLS500_kovrolin_0", "primitives": [{"attributes": {"NORMAL": 117, "POSITION": 116, "TEXCOORD_0": 118}, "indices": 119, "material": 9, "mode": 4}]}, {"name": "CLS500w219_door_FL_CLS500_body_0", "primitives": [{"attributes": {"NORMAL": 121, "POSITION": 120, "TEXCOORD_0": 122}, "indices": 123, "material": 8, "mode": 4}]}, {"name": "CLS500w219_door_FL_etk8000_0", "primitives": [{"attributes": {"NORMAL": 125, "POSITION": 124, "TEXCOORD_0": 126}, "indices": 127, "material": 3, "mode": 4}]}, {"name": "CLS500w219_door_FL_CLS500_plastick_0", "primitives": [{"attributes": {"NORMAL": 129, "POSITION": 128, "TEXCOORD_0": 130}, "indices": 131, "material": 6, "mode": 4}]}, {"name": "CLS500w219_mirror_lightglass_L_CLS500_mirrorglass_0", "primitives": [{"attributes": {"NORMAL": 133, "POSITION": 132, "TEXCOORD_0": 134}, "indices": 135, "material": 10, "mode": 4}]}, {"name": "CLS500w219_door_FR_CLS500_body_0", "primitives": [{"attributes": {"NORMAL": 137, "POSITION": 136, "TEXCOORD_0": 138}, "indices": 139, "material": 8, "mode": 4}]}, {"name": "CLS500w219_door_FR_etk8000_0", "primitives": [{"attributes": {"NORMAL": 141, "POSITION": 140, "TEXCOORD_0": 142}, "indices": 143, "material": 3, "mode": 4}]}, {"name": "CLS500w219_door_FR_CLS500_plastick_0", "primitives": [{"attributes": {"NORMAL": 145, "POSITION": 144, "TEXCOORD_0": 146}, "indices": 147, "material": 6, "mode": 4}]}, {"name": "CLS500w219_mirror_lightglass_R_CLS500_mirrorglass_0", "primitives": [{"attributes": {"NORMAL": 149, "POSITION": 148, "TEXCOORD_0": 150}, "indices": 151, "material": 10, "mode": 4}]}, {"name": "CLS500w219_door_RL_CLS500_body_0", "primitives": [{"attributes": {"NORMAL": 153, "POSITION": 152, "TEXCOORD_0": 154}, "indices": 155, "material": 8, "mode": 4}]}, {"name": "CLS500w219_door_RL_etk8000_0", "primitives": [{"attributes": {"NORMAL": 157, "POSITION": 156, "TEXCOORD_0": 158}, "indices": 159, "material": 3, "mode": 4}]}, {"name": "CLS500w219_door_RL_CLS500_plastick_0", "primitives": [{"attributes": {"NORMAL": 161, "POSITION": 160, "TEXCOORD_0": 162}, "indices": 163, "material": 6, "mode": 4}]}, {"name": "CLS500w219_door_RL_bastion_0", "primitives": [{"attributes": {"NORMAL": 165, "POSITION": 164, "TEXCOORD_0": 166}, "indices": 167, "material": 11, "mode": 4}]}, {"name": "CLS500w219_door_RR_CLS500_body_0", "primitives": [{"attributes": {"NORMAL": 169, "POSITION": 168, "TEXCOORD_0": 170}, "indices": 171, "material": 8, "mode": 4}]}, {"name": "CLS500w219_door_RR_etk8000_0", "primitives": [{"attributes": {"NORMAL": 173, "POSITION": 172, "TEXCOORD_0": 174}, "indices": 175, "material": 3, "mode": 4}]}, {"name": "CLS500w219_door_RR_CLS500_plastick_0", "primitives": [{"attributes": {"NORMAL": 177, "POSITION": 176, "TEXCOORD_0": 178}, "indices": 179, "material": 6, "mode": 4}]}, {"name": "CLS500w219_headlight_R3_CLS500w219_signal_R_0", "primitives": [{"attributes": {"NORMAL": 181, "POSITION": 180, "TEXCOORD_0": 182}, "indices": 183, "material": 12, "mode": 4}]}, {"name": "CLS500w219_headlight_L3_CLS500w219_signal_L_0", "primitives": [{"attributes": {"NORMAL": 185, "POSITION": 184, "TEXCOORD_0": 186}, "indices": 187, "material": 13, "mode": 4}]}, {"name": "CLS500w219_headlight_L_CLS500w219_highbeam_0", "primitives": [{"attributes": {"NORMAL": 189, "POSITION": 188, "TEXCOORD_0": 190}, "indices": 191, "material": 14, "mode": 4}]}, {"name": "CLS500w219_headlight_R_CLS500w219_highbeam_0", "primitives": [{"attributes": {"NORMAL": 193, "POSITION": 192, "TEXCOORD_0": 194}, "indices": 195, "material": 14, "mode": 4}]}, {"name": "CLS500w219_headlight_glass_L_etk800_glass_0", "primitives": [{"attributes": {"NORMAL": 197, "POSITION": 196, "TEXCOORD_0": 198}, "indices": 199, "material": 15, "mode": 4}]}, {"name": "CLS500w219_headlight_glass_R_etk800_glass_0", "primitives": [{"attributes": {"NORMAL": 201, "POSITION": 200, "TEXCOORD_0": 202}, "indices": 203, "material": 15, "mode": 4}]}, {"name": "CLS500w219_taillight_glass_R_CLS500_tailglass_0", "primitives": [{"attributes": {"NORMAL": 205, "POSITION": 204, "TEXCOORD_0": 206, "TEXCOORD_1": 207, "TEXCOORD_2": 208, "TEXCOORD_3": 209, "TEXCOORD_4": 210, "TEXCOORD_5": 211, "TEXCOORD_6": 212, "TEXCOORD_7": 213}, "indices": 214, "material": 16, "mode": 4}]}, {"name": "enginebloc_dark_misc_0", "primitives": [{"attributes": {"NORMAL": 216, "POSITION": 215, "TEXCOORD_0": 217}, "indices": 218, "material": 17, "mode": 4}]}, {"name": "CLS500w219_mirror_L_CLS500_body_0", "primitives": [{"attributes": {"NORMAL": 220, "POSITION": 219, "TEXCOORD_0": 221}, "indices": 222, "material": 8, "mode": 4}]}, {"name": "CLS500w219_mirror_R_CLS500_body_0", "primitives": [{"attributes": {"NORMAL": 224, "POSITION": 223, "TEXCOORD_0": 225}, "indices": 226, "material": 8, "mode": 4}]}, {"name": "CLS500w219_headlight_L2_CLS500w219_lowbeam_0", "primitives": [{"attributes": {"NORMAL": 228, "POSITION": 227, "TEXCOORD_0": 229}, "indices": 230, "material": 18, "mode": 4}]}, {"name": "CLS500w219_headlight_R2_CLS500w219_lowbeam_0", "primitives": [{"attributes": {"NORMAL": 232, "POSITION": 231, "TEXCOORD_0": 233}, "indices": 234, "material": 18, "mode": 4}]}, {"name": "CLS500w219_headlightframe_R_CLS500_chromee_0", "primitives": [{"attributes": {"NORMAL": 236, "POSITION": 235, "TEXCOORD_0": 237, "TEXCOORD_1": 238}, "indices": 239, "material": 19, "mode": 4}]}, {"name": "CLS500w219_headlightframe_L_CLS500_chromee_0", "primitives": [{"attributes": {"NORMAL": 241, "POSITION": 240, "TEXCOORD_0": 242, "TEXCOORD_1": 243}, "indices": 244, "material": 19, "mode": 4}]}, {"name": "CLS500w219_mirror_light_L_CLS500w219_signal_L_0", "primitives": [{"attributes": {"NORMAL": 246, "POSITION": 245, "TEXCOORD_0": 247}, "indices": 248, "material": 13, "mode": 4}]}, {"name": "CLS500w219_mirror_light_R_CLS500w219_signal_R_0", "primitives": [{"attributes": {"NORMAL": 250, "POSITION": 249, "TEXCOORD_0": 251}, "indices": 252, "material": 12, "mode": 4}]}, {"name": "CLS500w219_grille_CLS500_placticc_0", "primitives": [{"attributes": {"NORMAL": 254, "POSITION": 253, "TEXCOORD_0": 255}, "indices": 256, "material": 2, "mode": 4}]}, {"name": "CLS500w219_grille2_etk8000_0", "primitives": [{"attributes": {"NORMAL": 258, "POSITION": 257, "TEXCOORD_0": 259}, "indices": 260, "material": 3, "mode": 4}]}, {"name": "CLS500w219_fender_FR_CLS500_body_0", "primitives": [{"attributes": {"NORMAL": 262, "POSITION": 261, "TEXCOORD_0": 263}, "indices": 264, "material": 8, "mode": 4}]}, {"name": "CLS500w219_body_CLS500_body_0", "primitives": [{"attributes": {"NORMAL": 266, "POSITION": 265, "TEXCOORD_0": 267}, "indices": 268, "material": 8, "mode": 4}]}, {"name": "CLS500w219_chmsl_glass_CLS500_tailglass_0", "primitives": [{"attributes": {"NORMAL": 270, "POSITION": 269, "TEXCOORD_0": 271}, "indices": 272, "material": 16, "mode": 4}]}, {"name": "CLS500w219_mirrorglass_L_mirror_0", "primitives": [{"attributes": {"NORMAL": 274, "POSITION": 273, "TEXCOORD_0": 275}, "indices": 276, "material": 20, "mode": 4}]}, {"name": "CLS500w219_mirrorglass_R_mirror_0", "primitives": [{"attributes": {"NORMAL": 278, "POSITION": 277, "TEXCOORD_0": 279}, "indices": 280, "material": 20, "mode": 4}]}, {"name": "CLS500w219_console4_CLS500_wood_0", "primitives": [{"attributes": {"NORMAL": 282, "POSITION": 281, "TEXCOORD_0": 283}, "indices": 284, "material": 21, "mode": 4}]}, {"name": "CLS500w219_badge_mers_etk8000_0", "primitives": [{"attributes": {"NORMAL": 286, "POSITION": 285, "TEXCOORD_0": 287}, "indices": 288, "material": 3, "mode": 4}]}, {"name": "CLS500w219_hood_stock_CLS500_logo_0", "primitives": [{"attributes": {"NORMAL": 290, "POSITION": 289, "TEXCOORD_0": 291}, "indices": 292, "material": 22, "mode": 4}]}, {"name": "CLS500w219_fender_FL_CLS500_body_0", "primitives": [{"attributes": {"NORMAL": 294, "POSITION": 293, "TEXCOORD_0": 295}, "indices": 296, "material": 8, "mode": 4}]}, {"name": "CLS500w219_radiator_bastion_engine_0", "primitives": [{"attributes": {"COLOR_0": 299, "NORMAL": 298, "POSITION": 297, "TEXCOORD_0": 300}, "indices": 301, "material": 23, "mode": 4}]}, {"name": "CLS500w219_engine_undertray_CLS500w219_mechanical_0", "primitives": [{"attributes": {"NORMAL": 303, "POSITION": 302, "TEXCOORD_0": 304}, "indices": 305, "material": 24, "mode": 4}]}, {"name": "CLS500w219_r_bumper_AFTERMARKET_GRILLE_grille_hex_0", "primitives": [{"attributes": {"COLOR_0": 308, "NORMAL": 307, "POSITION": 306, "TEXCOORD_0": 309, "TEXCOORD_1": 310}, "indices": 311, "material": 25, "mode": 4}]}, {"name": "CLS500w219_swaybar_links_F_CLS500w219_mechanical_0", "primitives": [{"attributes": {"NORMAL": 313, "POSITION": 312, "TEXCOORD_0": 314}, "indices": 315, "material": 24, "mode": 4}]}, {"name": "CLS500w219_hub_FR_CLS500w219_mechanical_0", "primitives": [{"attributes": {"COLOR_0": 318, "NORMAL": 317, "POSITION": 316, "TEXCOORD_0": 319}, "indices": 320, "material": 24, "mode": 4}]}, {"name": "CLS500w219_tub_RL_CLS500w219_mechanical_0", "primitives": [{"attributes": {"COLOR_0": 323, "NORMAL": 322, "POSITION": 321, "TEXCOORD_0": 324}, "indices": 325, "material": 24, "mode": 4}]}, {"name": "CLS500w219_hoodlatch_CLS500w219_mechanical_0", "primitives": [{"attributes": {"COLOR_0": 328, "NORMAL": 327, "POSITION": 326, "TEXCOORD_0": 329, "TEXCOORD_1": 330}, "indices": 331, "material": 24, "mode": 4}]}, {"name": "CLS500w219_underbody_CLS500w219_mechanical_0", "primitives": [{"attributes": {"COLOR_0": 334, "NORMAL": 333, "POSITION": 332, "TEXCOORD_0": 335}, "indices": 336, "material": 24, "mode": 4}]}, {"name": "CLS500w219_tub_RR_CLS500w219_mechanical_0", "primitives": [{"attributes": {"COLOR_0": 339, "NORMAL": 338, "POSITION": 337, "TEXCOORD_0": 340}, "indices": 341, "material": 24, "mode": 4}]}, {"name": "CLS500w219_tub_FL_inner_CLS500w219_mechanical_0", "primitives": [{"attributes": {"COLOR_0": 344, "NORMAL": 343, "POSITION": 342, "TEXCOORD_0": 345}, "indices": 346, "material": 24, "mode": 4}]}, {"name": "CLS500w219_tub_FR_inner_CLS500w219_mechanical_0", "primitives": [{"attributes": {"COLOR_0": 349, "NORMAL": 348, "POSITION": 347, "TEXCOORD_0": 350}, "indices": 351, "material": 24, "mode": 4}]}, {"name": "CLS500w219_tub_FR_outer_CLS500w219_mechanical_0", "primitives": [{"attributes": {"COLOR_0": 354, "NORMAL": 353, "POSITION": 352, "TEXCOORD_0": 355}, "indices": 356, "material": 24, "mode": 4}]}, {"name": "CLS500w219_spoiler_R_sport_CLS500_plastick_0", "primitives": [{"attributes": {"NORMAL": 358, "POSITION": 357, "TEXCOORD_0": 359, "TEXCOORD_1": 360}, "indices": 361, "material": 6, "mode": 4}]}, {"name": "number_ramka_rear_CLS500_placticc_0", "primitives": [{"attributes": {"NORMAL": 363, "POSITION": 362, "TEXCOORD_0": 364, "TEXCOORD_1": 365}, "indices": 366, "material": 2, "mode": 4}]}, {"name": "number_ramka_rear_etk8000_0", "primitives": [{"attributes": {"NORMAL": 368, "POSITION": 367, "TEXCOORD_0": 369, "TEXCOORD_1": 370}, "indices": 371, "material": 3, "mode": 4}]}, {"name": "CLS500w219_lettering_amg_etk8000_0", "primitives": [{"attributes": {"NORMAL": 373, "POSITION": 372, "TEXCOORD_0": 374, "TEXCOORD_1": 375}, "indices": 376, "material": 3, "mode": 4}]}, {"name": "CLS500w219_bumper_F_amg_CLS500_body_0", "primitives": [{"attributes": {"NORMAL": 378, "POSITION": 377, "TEXCOORD_0": 379, "TEXCOORD_1": 380}, "indices": 381, "material": 8, "mode": 4}]}, {"name": "CLS500w219_bumper_F_amg_CLS500_placticc_0", "primitives": [{"attributes": {"NORMAL": 383, "POSITION": 382, "TEXCOORD_0": 384, "TEXCOORD_1": 385}, "indices": 386, "material": 2, "mode": 4}]}, {"name": "CLS500w219_bumper_F_amg_etk8000_0", "primitives": [{"attributes": {"NORMAL": 388, "POSITION": 387, "TEXCOORD_0": 389, "TEXCOORD_1": 390}, "indices": 391, "material": 3, "mode": 4}]}, {"name": "CLS500w219_bumper_R_amg_CLS500_body_0", "primitives": [{"attributes": {"NORMAL": 393, "POSITION": 392, "TEXCOORD_0": 394, "TEXCOORD_1": 395}, "indices": 396, "material": 8, "mode": 4}]}, {"name": "CLS500w219_bumper_R_reflector_amg_CLS500_placticc_0", "primitives": [{"attributes": {"NORMAL": 398, "POSITION": 397, "TEXCOORD_0": 399, "TEXCOORD_1": 400}, "indices": 401, "material": 2, "mode": 4}]}, {"name": "CLS500w219_grille3_CLS500_grille_0", "primitives": [{"attributes": {"NORMAL": 403, "POSITION": 402, "TEXCOORD_0": 404}, "indices": 405, "material": 4, "mode": 4}]}, {"name": "CLS500w219_bumper_F_luxe_amg_lightglass_L_etk800_glass_0", "primitives": [{"attributes": {"NORMAL": 407, "POSITION": 406, "TEXCOORD_0": 408, "TEXCOORD_1": 409}, "indices": 410, "material": 15, "mode": 4}]}, {"name": "CLS500w219_bumper_F_luxe_light_amg_R_CLS500w219_foglights_0", "primitives": [{"attributes": {"NORMAL": 412, "POSITION": 411, "TEXCOORD_0": 413, "TEXCOORD_1": 414}, "indices": 415, "material": 26, "mode": 4}]}, {"name": "CLS500w219_bumper_F_luxe_light_amg_L_CLS500w219_foglights_0", "primitives": [{"attributes": {"NORMAL": 417, "POSITION": 416, "TEXCOORD_0": 418, "TEXCOORD_1": 419}, "indices": 420, "material": 26, "mode": 4}]}, {"name": "CLS500w219_bumper_F_luxe_amg_lightglass_R_etk800_glass_0", "primitives": [{"attributes": {"NORMAL": 422, "POSITION": 421, "TEXCOORD_0": 423, "TEXCOORD_1": 424}, "indices": 425, "material": 15, "mode": 4}]}, {"name": "CLS500w219_sport_wheel2_white_CLS500_wheelvolklogo_0", "primitives": [{"attributes": {"NORMAL": 427, "POSITION": 426, "TEXCOORD_0": 428}, "indices": 429, "material": 27, "mode": 4}]}, {"name": "CLS500w219_sport_wheel2_white_CLS500_wheal_white_0", "primitives": [{"attributes": {"NORMAL": 431, "POSITION": 430, "TEXCOORD_0": 432}, "indices": 433, "material": 28, "mode": 4}]}, {"name": "CLS500w219_lettering_cls63_etk8000_0", "primitives": [{"attributes": {"NORMAL": 435, "POSITION": 434, "TEXCOORD_0": 436, "TEXCOORD_1": 437}, "indices": 438, "material": 3, "mode": 4}]}, {"name": "CLS500w219_lettering_cls63_CLS500_placticc_red_0", "primitives": [{"attributes": {"NORMAL": 440, "POSITION": 439, "TEXCOORD_0": 441, "TEXCOORD_1": 442}, "indices": 443, "material": 29, "mode": 4}]}, {"name": "CLS500w219_muffler_v6_L_amg_CLS500_placticc_0", "primitives": [{"attributes": {"NORMAL": 445, "POSITION": 444, "TEXCOORD_0": 446, "TEXCOORD_1": 447}, "indices": 448, "material": 2, "mode": 4}]}, {"name": "CLS500w219_muffler_v6_L_amg_CLS500_blackfulle_0", "primitives": [{"attributes": {"NORMAL": 450, "POSITION": 449, "TEXCOORD_0": 451, "TEXCOORD_1": 452}, "indices": 453, "material": 30, "mode": 4}]}, {"name": "CLS500w219_muffler_v6_R_amg_chrome_CLS500_placticc_0", "primitives": [{"attributes": {"NORMAL": 455, "POSITION": 454, "TEXCOORD_0": 456, "TEXCOORD_1": 457}, "indices": 458, "material": 2, "mode": 4}]}, {"name": "CLS500w219_muffler_v6_R_amg_chrome_CLS500_blackfulle_0", "primitives": [{"attributes": {"NORMAL": 460, "POSITION": 459, "TEXCOORD_0": 461, "TEXCOORD_1": 462}, "indices": 463, "material": 30, "mode": 4}]}, {"name": "3_Wheel_Scene_-_Root.002_0", "primitives": [{"attributes": {"NORMAL": 465, "POSITION": 464, "TEXCOORD_0": 466, "TEXCOORD_1": 467, "TEXCOORD_2": 468}, "indices": 469, "material": 31, "mode": 4}]}, {"name": "3_Wheel_amdb11_brake.002_0", "primitives": [{"attributes": {"NORMAL": 471, "POSITION": 470, "TEXCOORD_0": 472, "TEXCOORD_1": 473, "TEXCOORD_2": 474}, "indices": 475, "material": 32, "mode": 4}]}, {"name": "3_Wheel_amdb11_misc_chrome.002_0", "primitives": [{"attributes": {"NORMAL": 477, "POSITION": 476, "TEXCOORD_0": 478, "TEXCOORD_1": 479, "TEXCOORD_2": 480}, "indices": 481, "material": 33, "mode": 4}]}, {"name": "3_Wheel_amdb11_misc.002_0", "primitives": [{"attributes": {"NORMAL": 483, "POSITION": 482, "TEXCOORD_0": 484, "TEXCOORD_1": 485, "TEXCOORD_2": 486}, "indices": 487, "material": 34, "mode": 4}]}, {"name": "3_Wheel_amdb11_caliper.002_0", "primitives": [{"attributes": {"NORMAL": 489, "POSITION": 488, "TEXCOORD_0": 490, "TEXCOORD_1": 491, "TEXCOORD_2": 492}, "indices": 493, "material": 35, "mode": 4}]}, {"name": "CLS500w219_sport_wheel2_white.001_CLS500_wheelvolklogo_0", "primitives": [{"attributes": {"NORMAL": 495, "POSITION": 494, "TEXCOORD_0": 496}, "indices": 497, "material": 27, "mode": 4}]}, {"name": "CLS500w219_sport_wheel2_white.001_CLS500_wheal_white_0", "primitives": [{"attributes": {"NORMAL": 499, "POSITION": 498, "TEXCOORD_0": 500}, "indices": 501, "material": 28, "mode": 4}]}, {"name": "3_Wheel.001_Scene_-_Root.002_0", "primitives": [{"attributes": {"NORMAL": 503, "POSITION": 502, "TEXCOORD_0": 504, "TEXCOORD_1": 505, "TEXCOORD_2": 506}, "indices": 507, "material": 31, "mode": 4}]}, {"name": "3_Wheel.001_amdb11_brake.002_0", "primitives": [{"attributes": {"NORMAL": 509, "POSITION": 508, "TEXCOORD_0": 510, "TEXCOORD_1": 511, "TEXCOORD_2": 512}, "indices": 513, "material": 32, "mode": 4}]}, {"name": "3_Wheel.001_amdb11_misc_chrome.002_0", "primitives": [{"attributes": {"NORMAL": 515, "POSITION": 514, "TEXCOORD_0": 516, "TEXCOORD_1": 517, "TEXCOORD_2": 518}, "indices": 519, "material": 33, "mode": 4}]}, {"name": "3_Wheel.001_amdb11_misc.002_0", "primitives": [{"attributes": {"NORMAL": 521, "POSITION": 520, "TEXCOORD_0": 522, "TEXCOORD_1": 523, "TEXCOORD_2": 524}, "indices": 525, "material": 34, "mode": 4}]}, {"name": "3_Wheel.001_amdb11_caliper.002_0", "primitives": [{"attributes": {"NORMAL": 527, "POSITION": 526, "TEXCOORD_0": 528, "TEXCOORD_1": 529, "TEXCOORD_2": 530}, "indices": 531, "material": 35, "mode": 4}]}, {"name": "CLS500w219_sport_wheel2_white.002_CLS500_wheelvolklogo_0", "primitives": [{"attributes": {"NORMAL": 533, "POSITION": 532, "TEXCOORD_0": 534}, "indices": 535, "material": 27, "mode": 4}]}, {"name": "CLS500w219_sport_wheel2_white.002_CLS500_wheal_white_0", "primitives": [{"attributes": {"NORMAL": 537, "POSITION": 536, "TEXCOORD_0": 538}, "indices": 539, "material": 28, "mode": 4}]}, {"name": "3_Wheel.002_Scene_-_Root.002_0", "primitives": [{"attributes": {"NORMAL": 541, "POSITION": 540, "TEXCOORD_0": 542, "TEXCOORD_1": 543, "TEXCOORD_2": 544}, "indices": 545, "material": 31, "mode": 4}]}, {"name": "3_Wheel.002_amdb11_brake.002_0", "primitives": [{"attributes": {"NORMAL": 547, "POSITION": 546, "TEXCOORD_0": 548, "TEXCOORD_1": 549, "TEXCOORD_2": 550}, "indices": 551, "material": 32, "mode": 4}]}, {"name": "3_Wheel.002_amdb11_misc_chrome.002_0", "primitives": [{"attributes": {"NORMAL": 553, "POSITION": 552, "TEXCOORD_0": 554, "TEXCOORD_1": 555, "TEXCOORD_2": 556}, "indices": 557, "material": 33, "mode": 4}]}, {"name": "3_Wheel.002_amdb11_misc.002_0", "primitives": [{"attributes": {"NORMAL": 559, "POSITION": 558, "TEXCOORD_0": 560, "TEXCOORD_1": 561, "TEXCOORD_2": 562}, "indices": 563, "material": 34, "mode": 4}]}, {"name": "3_Wheel.002_amdb11_caliper.002_0", "primitives": [{"attributes": {"NORMAL": 565, "POSITION": 564, "TEXCOORD_0": 566, "TEXCOORD_1": 567, "TEXCOORD_2": 568}, "indices": 569, "material": 35, "mode": 4}]}, {"name": "CLS500w219_sport_wheel2_white.003_CLS500_wheelvolklogo_0", "primitives": [{"attributes": {"NORMAL": 571, "POSITION": 570, "TEXCOORD_0": 572}, "indices": 573, "material": 27, "mode": 4}]}, {"name": "CLS500w219_sport_wheel2_white.003_CLS500_wheal_white_0", "primitives": [{"attributes": {"NORMAL": 575, "POSITION": 574, "TEXCOORD_0": 576}, "indices": 577, "material": 28, "mode": 4}]}, {"name": "3_Wheel.003_Scene_-_Root.002_0", "primitives": [{"attributes": {"NORMAL": 579, "POSITION": 578, "TEXCOORD_0": 580, "TEXCOORD_1": 581, "TEXCOORD_2": 582}, "indices": 583, "material": 31, "mode": 4}]}, {"name": "3_Wheel.003_amdb11_brake.002_0", "primitives": [{"attributes": {"NORMAL": 585, "POSITION": 584, "TEXCOORD_0": 586, "TEXCOORD_1": 587, "TEXCOORD_2": 588}, "indices": 589, "material": 32, "mode": 4}]}, {"name": "3_Wheel.003_amdb11_misc_chrome.002_0", "primitives": [{"attributes": {"NORMAL": 591, "POSITION": 590, "TEXCOORD_0": 592, "TEXCOORD_1": 593, "TEXCOORD_2": 594}, "indices": 595, "material": 33, "mode": 4}]}, {"name": "3_Wheel.003_amdb11_misc.002_0", "primitives": [{"attributes": {"NORMAL": 597, "POSITION": 596, "TEXCOORD_0": 598, "TEXCOORD_1": 599, "TEXCOORD_2": 600}, "indices": 601, "material": 34, "mode": 4}]}, {"name": "3_Wheel.003_amdb11_caliper.002_0", "primitives": [{"attributes": {"NORMAL": 603, "POSITION": 602, "TEXCOORD_0": 604, "TEXCOORD_1": 605, "TEXCOORD_2": 606}, "indices": 607, "material": 35, "mode": 4}]}, {"name": "CLS500w219_interior.001_CLS500_leather_white_0", "primitives": [{"attributes": {"NORMAL": 609, "POSITION": 608, "TEXCOORD_0": 610}, "indices": 611, "material": 36, "mode": 4}]}], "nodes": [{"children": [1], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 2.220446049250313e-16, -1.0, 0.0, 0.0, 1.0, 2.220446049250313e-16, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Sketchfab_model"}, {"children": [2], "matrix": [0.009999999776482582, 0.0, 0.0, 0.0, 0.0, 0.0, 0.009999999776482582, 0.0, 0.0, -0.009999999776482582, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "219.fbx"}, {"children": [3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 24, 26, 28, 30, 32, 34, 37, 39, 41, 46, 51, 55, 57, 61, 63, 68, 72, 74, 76, 78, 80, 82, 84, 86, 88, 90, 92, 94, 96, 98, 100, 102, 104, 106, 108, 110, 112, 114, 116, 118, 120, 122, 124, 126, 128, 130, 132, 134, 136, 138, 140, 142, 144, 146, 148, 150, 152, 155, 157, 161, 163, 165, 167, 169, 171, 173, 175, 178, 181, 184, 187, 193, 196, 202, 205, 211, 214, 220], "name": "RootNode"}, {"children": [4], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.00011467526201158762, 129.53973388671875, -174.17251586914062, 1.0], "name": "CLS500w219_backlight_int_black"}, {"mesh": 0, "name": "CLS500w219_backlight_int_black_CLS500w219_black_glass3_int_0"}, {"children": [6], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 80.68017578125, 120.06776428222656, 5.6459808349609375, 1.0], "name": "CLS500w219_doorglass_FL_int_black"}, {"mesh": 1, "name": "CLS500w219_doorglass_FL_int_black_CLS500w219_black_glass3_int_0"}, {"children": [8], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -80.72611999511719, 120.0677719116211, 5.645936489105225, 1.0], "name": "CLS500w219_doorglass_FR_int_black"}, {"mesh": 2, "name": "CLS500w219_doorglass_FR_int_black_CLS500w219_black_glass3_int_0"}, {"children": [10], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 79.3502426147461, 124.41448211669922, -83.96942138671875, 1.0], "name": "CLS500w219_doorglass_RL_int_black"}, {"mesh": 3, "name": "CLS500w219_doorglass_RL_int_black_CLS500w219_black_glass3_int_0"}, {"children": [12], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 81.55989837646484, 118.35553741455078, -127.75008392333984, 1.0], "name": "CLS500w219_doorglass_RL_int2_black"}, {"mesh": 4, "name": "CLS500w219_doorglass_RL_int2_black_CLS500w219_black_glass3_int_0"}, {"children": [14], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -79.29137420654297, 124.4144287109375, -83.96942901611328, 1.0], "name": "CLS500w219_doorglass_RR_int_black"}, {"mesh": 5, "name": "CLS500w219_doorglass_RR_int_black_CLS500w219_black_glass3_int_0"}, {"children": [16], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -81.5754165649414, 118.35545349121094, -127.75013732910156, 1.0], "name": "CLS500w219_doorglass_RR_int2_black"}, {"mesh": 6, "name": "CLS500w219_doorglass_RR_int2_black_CLS500w219_black_glass3_int_0"}, {"children": [18], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -0.00015527773939538747, 124.25028991699219, 55.95515441894531, 1.0], "name": "CLS500w219_windshield_int_black"}, {"mesh": 7, "name": "CLS500w219_windshield_int_black_CLS500w219_black_glass3_int_0"}, {"children": [20], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.07494383305311203, 48.926734924316406, 246.56520080566406, 1.0], "name": "licenseplate-52-11_F"}, {"mesh": 8, "name": "licenseplate-52-11_F_licenseplate-52-11_0"}, {"children": [22, 23], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.42159318923950195, 48.39655303955078, 243.13473510742188, 1.0], "name": "number_ramka_f"}, {"mesh": 9, "name": "number_ramka_f_CLS500_placticc_0"}, {"mesh": 10, "name": "number_ramka_f_etk8000_0"}, {"children": [25], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.06881748884916306, 89.01629638671875, -264.4408874511719, 1.0], "name": "licenseplate-52-11_R"}, {"mesh": 11, "name": "licenseplate-52-11_R_licenseplate-52-11_0"}, {"children": [27], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 27.39139747619629, 106.78424835205078, 96.65727996826172, 1.0], "name": "CLS500w219_wiperL"}, {"mesh": 12, "name": "CLS500w219_wiperL_CLS500_placticc_0"}, {"children": [29], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -26.296323776245117, 102.7770767211914, 97.4634017944336, 1.0], "name": "CLS500w219_wiperR"}, {"mesh": 13, "name": "CLS500w219_wiperR_CLS500_placticc_0"}, {"children": [31], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.3033393919467926, 62.91139602661133, 231.16839599609375, 1.0], "name": "CLS500w219_grille_two1"}, {"mesh": 14, "name": "CLS500w219_grille_two1_CLS500_grille_0"}, {"children": [33], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.0981113389134407, 22.85736846923828, 220.83282470703125, 1.0], "name": "skel_mesh.090"}, {"mesh": 15, "name": "skel_mesh.090_vehicle_mesh_0"}, {"children": [35, 36], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -0.6371912360191345, 102.2696762084961, -83.84898376464844, 1.0], "name": "CLS500w219_chassis"}, {"mesh": 16, "name": "CLS500w219_chassis_CLS500_placticc_0"}, {"mesh": 17, "name": "CLS500w219_chassis_CLS500_plastick_0"}, {"children": [38], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 4.032078777527204e-06, 46.197967529296875, 216.67189025878906, 1.0], "name": "sunburst_bumperbar_F"}, {"mesh": 18, "name": "sunburst_bumperbar_F_sunburst_0"}, {"children": [40], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -3.360198736190796, 98.60519409179688, -95.15665435791016, 1.0], "name": "CLS500w219_z_chrome"}, {"mesh": 19, "name": "CLS500w219_z_chrome_etk8000_0"}, {"children": [42, 43, 44, 45], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -0.0963195264339447, 93.89279174804688, 145.3435821533203, 1.0], "name": "CLS500w219_hood"}, {"mesh": 20, "name": "CLS500w219_hood_CLS500_body_0"}, {"mesh": 21, "name": "CLS500w219_hood_CLS500_placticc_0"}, {"mesh": 22, "name": "CLS500w219_hood_CLS500_plastick_0"}, {"mesh": 23, "name": "CLS500w219_hood_CLS500_grille_0"}, {"children": [47, 48, 49, 50], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -2.408554792404175, 98.05244445800781, -238.6168975830078, 1.0], "name": "CLS500w219_trunk"}, {"mesh": 24, "name": "CLS500w219_trunk_CLS500_body_0"}, {"mesh": 25, "name": "CLS500w219_trunk_etk8000_0"}, {"mesh": 26, "name": "CLS500w219_trunk_CLS500_plastick_0"}, {"mesh": 27, "name": "CLS500w219_trunk_CLS500_kovrolin_0"}, {"children": [52, 53, 54], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 92.3870620727539, 82.98522186279297, 9.577990531921387, 1.0], "name": "CLS500w219_door_FL"}, {"mesh": 28, "name": "CLS500w219_door_FL_CLS500_body_0"}, {"mesh": 29, "name": "CLS500w219_door_FL_etk8000_0"}, {"mesh": 30, "name": "CLS500w219_door_FL_CLS500_plastick_0"}, {"children": [56], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 105.81800842285156, 112.14076232910156, 49.58395004272461, 1.0], "name": "CLS500w219_mirror_lightglass_L"}, {"mesh": 31, "name": "CLS500w219_mirror_lightglass_L_CLS500_mirrorglass_0"}, {"children": [58, 59, 60], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -92.21234130859375, 82.77010345458984, 10.483646392822266, 1.0], "name": "CLS500w219_door_FR"}, {"mesh": 32, "name": "CLS500w219_door_FR_CLS500_body_0"}, {"mesh": 33, "name": "CLS500w219_door_FR_etk8000_0"}, {"mesh": 34, "name": "CLS500w219_door_FR_CLS500_plastick_0"}, {"children": [62], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -105.81800842285156, 112.14088439941406, 49.58389663696289, 1.0], "name": "CLS500w219_mirror_lightglass_R"}, {"mesh": 35, "name": "CLS500w219_mirror_lightglass_R_CLS500_mirrorglass_0"}, {"children": [64, 65, 66, 67], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 90.72465515136719, 84.81048583984375, -93.62826538085938, 1.0], "name": "CLS500w219_door_RL"}, {"mesh": 36, "name": "CLS500w219_door_RL_CLS500_body_0"}, {"mesh": 37, "name": "CLS500w219_door_RL_etk8000_0"}, {"mesh": 38, "name": "CLS500w219_door_RL_CLS500_plastick_0"}, {"mesh": 39, "name": "CLS500w219_door_RL_bastion_0"}, {"children": [69, 70, 71], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -90.8028793334961, 84.86392211914062, -93.62478637695312, 1.0], "name": "CLS500w219_door_RR"}, {"mesh": 40, "name": "CLS500w219_door_RR_CLS500_body_0"}, {"mesh": 41, "name": "CLS500w219_door_RR_etk8000_0"}, {"mesh": 42, "name": "CLS500w219_door_RR_CLS500_plastick_0"}, {"children": [73], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -77.77201843261719, 79.48495483398438, 190.8325958251953, 1.0], "name": "CLS500w219_headlight_R3"}, {"mesh": 43, "name": "CLS500w219_headlight_R3_CLS500w219_signal_R_0"}, {"children": [75], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 78.00637817382812, 79.49250030517578, 190.16246032714844, 1.0], "name": "CLS500w219_headlight_L3"}, {"mesh": 44, "name": "CLS500w219_headlight_L3_CLS500w219_signal_L_0"}, {"children": [77], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 59.86356735229492, 66.72373962402344, 212.94644165039062, 1.0], "name": "CLS500w219_headlight_L"}, {"mesh": 45, "name": "CLS500w219_headlight_L_CLS500w219_highbeam_0"}, {"children": [79], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -59.863609313964844, 66.74274444580078, 212.94541931152344, 1.0], "name": "CLS500w219_headlight_R"}, {"mesh": 46, "name": "CLS500w219_headlight_R_CLS500w219_highbeam_0"}, {"children": [81], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 67.7725830078125, 69.92385864257812, 216.34902954101562, 1.0], "name": "CLS500w219_headlight_glass_L"}, {"mesh": 47, "name": "CLS500w219_headlight_glass_L_etk800_glass_0"}, {"children": [83], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -67.7726058959961, 69.92388153076172, 216.34902954101562, 1.0], "name": "CLS500w219_headlight_glass_R"}, {"mesh": 48, "name": "CLS500w219_headlight_glass_R_etk800_glass_0"}, {"children": [85], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -67.13078308105469, 83.36601257324219, -253.6842041015625, 1.0], "name": "CLS500w219_taillight_glass_R"}, {"mesh": 49, "name": "CLS500w219_taillight_glass_R_CLS500_tailglass_0"}, {"children": [87], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -0.12484190613031387, 60.820068359375, -221.33755493164062, 1.0], "name": "enginebloc"}, {"mesh": 50, "name": "enginebloc_dark_misc_0"}, {"children": [89], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 96.29727935791016, 111.4488754272461, 49.00542449951172, 1.0], "name": "CLS500w219_mirror_L"}, {"mesh": 51, "name": "CLS500w219_mirror_L_CLS500_body_0"}, {"children": [91], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -95.88599395751953, 111.34825134277344, 49.29243850708008, 1.0], "name": "CLS500w219_mirror_R"}, {"mesh": 52, "name": "CLS500w219_mirror_R_CLS500_body_0"}, {"children": [93], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 74.35941314697266, 68.78567504882812, 208.32164001464844, 1.0], "name": "CLS500w219_headlight_L2"}, {"mesh": 53, "name": "CLS500w219_headlight_L2_CLS500w219_lowbeam_0"}, {"children": [95], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -74.35944366455078, 68.78570556640625, 208.32164001464844, 1.0], "name": "CLS500w219_headlight_R2"}, {"mesh": 54, "name": "CLS500w219_headlight_R2_CLS500w219_lowbeam_0"}, {"children": [97], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -71.19215393066406, 70.510986328125, 204.53538513183594, 1.0], "name": "CLS500w219_headlightframe_R"}, {"mesh": 55, "name": "CLS500w219_headlightframe_R_CLS500_chromee_0"}, {"children": [99], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 71.19215393066406, 70.5110092163086, 204.53546142578125, 1.0], "name": "CLS500w219_headlightframe_L"}, {"mesh": 56, "name": "CLS500w219_headlightframe_L_CLS500_chromee_0"}, {"children": [101], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 104.4969711303711, 112.08623504638672, 50.68722915649414, 1.0], "name": "CLS500w219_mirror_light_L"}, {"mesh": 57, "name": "CLS500w219_mirror_light_L_CLS500w219_signal_L_0"}, {"children": [103], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -104.49700164794922, 112.08638000488281, 50.68718338012695, 1.0], "name": "CLS500w219_mirror_light_R"}, {"mesh": 58, "name": "CLS500w219_mirror_light_R_CLS500w219_signal_R_0"}, {"children": [105], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.00383185176178813, 65.99366760253906, 233.1229705810547, 1.0], "name": "CLS500w219_grille"}, {"mesh": 59, "name": "CLS500w219_grille_CLS500_placticc_0"}, {"children": [107], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.021391356363892555, 66.5126953125, 236.8617706298828, 1.0], "name": "CLS500w219_grille2"}, {"mesh": 60, "name": "CLS500w219_grille2_etk8000_0"}, {"children": [109], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -91.04376983642578, 72.08218383789062, 134.0037384033203, 1.0], "name": "CLS500w219_fender_FR"}, {"mesh": 61, "name": "CLS500w219_fender_FR_CLS500_body_0"}, {"children": [111], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -5.218687057495117, 86.90877532958984, -54.935401916503906, 1.0], "name": "CLS500w219_body"}, {"mesh": 62, "name": "CLS500w219_body_CLS500_body_0"}, {"children": [113], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.0681050717830658, 119.49441528320312, -204.68807983398438, 1.0], "name": "CLS500w219_chmsl_glass"}, {"mesh": 63, "name": "CLS500w219_chmsl_glass_CLS500_tailglass_0"}, {"children": [115], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 98.45879364013672, 112.6053237915039, 46.77265167236328, 1.0], "name": "CLS500w219_mirrorglass_L"}, {"mesh": 64, "name": "CLS500w219_mirrorglass_L_mirror_0"}, {"children": [117], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -98.45879364013672, 112.60541534423828, 46.7725944519043, 1.0], "name": "CLS500w219_mirrorglass_R"}, {"mesh": 65, "name": "CLS500w219_mirrorglass_R_mirror_0"}, {"children": [119], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.0020788086112588644, 57.16963577270508, -86.71847534179688, 1.0], "name": "CLS500w219_console4"}, {"mesh": 66, "name": "CLS500w219_console4_CLS500_wood_0"}, {"children": [121], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.02346092462539673, 103.66219329833984, -265.05352783203125, 1.0], "name": "CLS500w219_badge_mers"}, {"mesh": 67, "name": "CLS500w219_badge_mers_etk8000_0"}, {"children": [123], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -0.0002532601938582957, 81.99615478515625, 225.3834991455078, 1.0], "name": "CLS500w219_hood_stock"}, {"mesh": 68, "name": "CLS500w219_hood_stock_CLS500_logo_0"}, {"children": [125], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 91.04376983642578, 72.08218383789062, 134.0037384033203, 1.0], "name": "CLS500w219_fender_FL"}, {"mesh": 69, "name": "CLS500w219_fender_FL_CLS500_body_0"}, {"children": [127], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -1.6524860858917236, 48.55768966674805, 212.19358825683594, 1.0], "name": "CLS500w219_radiator"}, {"mesh": 70, "name": "CLS500w219_radiator_bastion_engine_0"}, {"children": [129], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 2.4143653263308806e-06, 18.46266746520996, 188.63699340820312, 1.0], "name": "CLS500w219_engine_undertray"}, {"mesh": 71, "name": "CLS500w219_engine_undertray_CLS500w219_mechanical_0"}, {"children": [131], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 2.483526941432501e-06, 44.40776824951172, -200.0902862548828, 1.0], "name": "CLS500w219_r_bumper_AFTERMARKET_GRILLE"}, {"mesh": 72, "name": "CLS500w219_r_bumper_AFTERMARKET_GRILLE_grille_hex_0"}, {"children": [133], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 1.7099691831390373e-06, 51.914058685302734, 155.804443359375, 1.0], "name": "CLS500w219_swaybar_links_F"}, {"mesh": 73, "name": "CLS500w219_swaybar_links_F_CLS500w219_mechanical_0"}, {"children": [135], "matrix": [1.0, 0.0, 0.0, 0.0, -0.0, -1.6292067939183141e-07, -0.9999999999999868, 0.0, 0.0, 0.9999999999999868, -1.6292067939183141e-07, 0.0, -73.96788024902344, 40.18989944458008, 151.79495239257812, 1.0], "name": "CLS500w219_hub_FR"}, {"mesh": 74, "name": "CLS500w219_hub_FR_CLS500w219_mechanical_0"}, {"children": [137], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 73.06163024902344, 49.28394317626953, -153.4341278076172, 1.0], "name": "CLS500w219_tub_RL"}, {"mesh": 75, "name": "CLS500w219_tub_RL_CLS500w219_mechanical_0"}, {"children": [139], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -1.0894279479980469, 77.47454833984375, 229.44932556152344, 1.0], "name": "CLS500w219_hoodlatch"}, {"mesh": 76, "name": "CLS500w219_hoodlatch_CLS500w219_mechanical_0"}, {"children": [141], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.0012106738286092877, 49.87583541870117, -9.01371955871582, 1.0], "name": "CLS500w219_underbody"}, {"mesh": 77, "name": "CLS500w219_underbody_CLS500w219_mechanical_0"}, {"children": [143], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -73.06166076660156, 49.28060531616211, -153.4315643310547, 1.0], "name": "CLS500w219_tub_RR"}, {"mesh": 78, "name": "CLS500w219_tub_RR_CLS500w219_mechanical_0"}, {"children": [145], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 62.018741607666016, 47.98893356323242, 143.50106811523438, 1.0], "name": "CLS500w219_tub_FL_inner"}, {"mesh": 79, "name": "CLS500w219_tub_FL_inner_CLS500w219_mechanical_0"}, {"children": [147], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -62.0444221496582, 49.5793342590332, 143.3827667236328, 1.0], "name": "CLS500w219_tub_FR_inner"}, {"mesh": 80, "name": "CLS500w219_tub_FR_inner_CLS500w219_mechanical_0"}, {"children": [149], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -84.94567108154297, 61.724639892578125, 135.01437377929688, 1.0], "name": "CLS500w219_tub_FR_outer"}, {"mesh": 81, "name": "CLS500w219_tub_FR_outer_CLS500w219_mechanical_0"}, {"children": [151], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 7.73202846175991e-05, 107.42086029052734, -258.0578308105469, 1.0], "name": "CLS500w219_spoiler_R_sport"}, {"mesh": 82, "name": "CLS500w219_spoiler_R_sport_CLS500_plastick_0"}, {"children": [153, 154], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -0.27296802401542664, 88.30076599121094, -263.8753662109375, 1.0], "name": "number_ramka_rear"}, {"mesh": 83, "name": "number_ramka_rear_CLS500_placticc_0"}, {"mesh": 84, "name": "number_ramka_rear_etk8000_0"}, {"children": [156], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -47.41456604003906, 102.08978271484375, -259.3155212402344, 1.0], "name": "CLS500w219_lettering_amg"}, {"mesh": 85, "name": "CLS500w219_lettering_amg_etk8000_0"}, {"children": [158, 159, 160], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.23870758712291718, 38.590694427490234, 229.00381469726562, 1.0], "name": "CLS500w219_bumper_F_amg"}, {"mesh": 86, "name": "CLS500w219_bumper_F_amg_CLS500_body_0"}, {"mesh": 87, "name": "CLS500w219_bumper_F_amg_CLS500_placticc_0"}, {"mesh": 88, "name": "CLS500w219_bumper_F_amg_etk8000_0"}, {"children": [162], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -5.1683600759133697e-05, 66.25384521484375, -232.5401611328125, 1.0], "name": "CLS500w219_bumper_R_amg"}, {"mesh": 89, "name": "CLS500w219_bumper_R_amg_CLS500_body_0"}, {"children": [164], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -0.01640726625919342, 50.699378967285156, -271.17950439453125, 1.0], "name": "CLS500w219_bumper_R_reflector_amg"}, {"mesh": 90, "name": "CLS500w219_bumper_R_reflector_amg_CLS500_placticc_0"}, {"children": [166], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.06995601952075958, 66.47718811035156, 233.34808349609375, 1.0], "name": "CLS500w219_grille3"}, {"mesh": 91, "name": "CLS500w219_grille3_CLS500_grille_0"}, {"children": [168], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 74.38018798828125, 36.375282287597656, 225.90696716308594, 1.0], "name": "CLS500w219_bumper_F_luxe_amg_lightglass_L"}, {"mesh": 92, "name": "CLS500w219_bumper_F_luxe_amg_lightglass_L_etk800_glass_0"}, {"children": [170], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -73.89997100830078, 36.19548797607422, 223.28872680664062, 1.0], "name": "CLS500w219_bumper_F_luxe_light_amg_R"}, {"mesh": 93, "name": "CLS500w219_bumper_F_luxe_light_amg_R_CLS500w219_foglights_0"}, {"children": [172], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 74.1693344116211, 36.195491790771484, 223.28868103027344, 1.0], "name": "CLS500w219_bumper_F_luxe_light_amg_L"}, {"mesh": 94, "name": "CLS500w219_bumper_F_luxe_light_amg_L_CLS500w219_foglights_0"}, {"children": [174], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -74.03819274902344, 36.375282287597656, 225.90695190429688, 1.0], "name": "CLS500w219_bumper_F_luxe_amg_lightglass_R"}, {"mesh": 95, "name": "CLS500w219_bumper_F_luxe_amg_lightglass_R_etk800_glass_0"}, {"children": [176, 177], "matrix": [110.0, 0.0, 0.0, 0.0, 0.0, 8.779219387010759e-06, -116.2842864990231, 0.0, 0.0, 116.2842864990231, 8.779219387010759e-06, 0.0, 87.56173706054688, 42.45737075805664, 151.50698852539062, 1.0], "name": "CLS500w219_sport_wheel2_white"}, {"mesh": 96, "name": "CLS500w219_sport_wheel2_white_CLS500_wheelvolklogo_0"}, {"mesh": 97, "name": "CLS500w219_sport_wheel2_white_CLS500_wheal_white_0"}, {"children": [179, 180], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 40.36387634277344, 100.81303405761719, -261.0893859863281, 1.0], "name": "CLS500w219_lettering_cls63"}, {"mesh": 98, "name": "CLS500w219_lettering_cls63_etk8000_0"}, {"mesh": 99, "name": "CLS500w219_lettering_cls63_CLS500_placticc_red_0"}, {"children": [182, 183], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 50.27759552001953, 44.440208435058594, -268.8144836425781, 1.0], "name": "CLS500w219_muffler_v6_L_amg"}, {"mesh": 100, "name": "CLS500w219_muffler_v6_L_amg_CLS500_placticc_0"}, {"mesh": 101, "name": "CLS500w219_muffler_v6_L_amg_CLS500_blackfulle_0"}, {"children": [185, 186], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -50.22404098510742, 44.43268966674805, -268.810546875, 1.0], "name": "CLS500w219_muffler_v6_R_amg_chrome"}, {"mesh": 102, "name": "CLS500w219_muffler_v6_R_amg_chrome_CLS500_placticc_0"}, {"mesh": 103, "name": "CLS500w219_muffler_v6_R_amg_chrome_CLS500_blackfulle_0"}, {"children": [188, 189, 190, 191, 192], "matrix": [1.9790182600626126e-05, -5.6421944583616695e-15, -101.64073944091606, 0.0, -101.64073944091597, -4.442858008003346e-06, -1.9790182628837097e-05, 0.0, -4.442858330210397e-06, 101.64074707031243, -9.027511811005029e-13, 0.0, 86.7811050415039, 42.45737075805664, 151.50698852539062, 1.0], "name": "3_Wheel"}, {"mesh": 104, "name": "3_Wheel_Scene_-_Root.002_0"}, {"mesh": 105, "name": "3_Wheel_amdb11_brake.002_0"}, {"mesh": 106, "name": "3_Wheel_amdb11_misc_chrome.002_0"}, {"mesh": 107, "name": "3_Wheel_amdb11_misc.002_0"}, {"mesh": 108, "name": "3_Wheel_amdb11_caliper.002_0"}, {"children": [194, 195], "matrix": [110.0, 0.0, 0.0, 0.0, 0.0, 8.779219387010759e-06, -116.2842864990231, 0.0, 0.0, 116.2842864990231, 8.779219387010759e-06, 0.0, 90.56173706054688, 42.45737075805664, -153.13925170898438, 1.0], "name": "CLS500w219_sport_wheel2_white.001"}, {"mesh": 109, "name": "CLS500w219_sport_wheel2_white.001_CLS500_wheelvolklogo_0"}, {"mesh": 110, "name": "CLS500w219_sport_wheel2_white.001_CLS500_wheal_white_0"}, {"children": [197, 198, 199, 200, 201], "matrix": [1.9790182600626126e-05, -5.6421944583616695e-15, -101.64073944091606, 0.0, -101.64073944091597, -4.442858008003346e-06, -1.9790182628837097e-05, 0.0, -4.442858330210397e-06, 101.64074707031243, -9.027511811005029e-13, 0.0, 89.7811050415039, 42.45737075805664, -153.13925170898438, 1.0], "name": "3_Wheel.001"}, {"mesh": 111, "name": "3_Wheel.001_Scene_-_Root.002_0"}, {"mesh": 112, "name": "3_Wheel.001_amdb11_brake.002_0"}, {"mesh": 113, "name": "3_Wheel.001_amdb11_misc_chrome.002_0"}, {"mesh": 114, "name": "3_Wheel.001_amdb11_misc.002_0"}, {"mesh": 115, "name": "3_Wheel.001_amdb11_caliper.002_0"}, {"children": [203, 204], "matrix": [-110.0, -0.0, -0.0, -0.0, -0.0, -8.779219387010759e-06, -116.2842864990231, -0.0, -0.0, 116.2842864990231, -8.779219387010759e-06, -0.0, -87.0803451538086, 42.45737075805664, 151.50698852539062, 1.0], "name": "CLS500w219_sport_wheel2_white.002"}, {"mesh": 116, "name": "CLS500w219_sport_wheel2_white.002_CLS500_wheelvolklogo_0"}, {"mesh": 117, "name": "CLS500w219_sport_wheel2_white.002_CLS500_wheal_white_0"}, {"children": [206, 207, 208, 209, 210], "matrix": [-1.9790182600626126e-05, 5.6421944583616695e-15, -101.64073944091606, -0.0, 101.64073944091597, 4.442858008003346e-06, -1.9790182628837097e-05, -0.0, -4.442858330210397e-06, 101.64074707031243, 9.027511811005029e-13, -0.0, -86.29972076416016, 42.45737075805664, 151.50698852539062, 1.0], "name": "3_Wheel.002"}, {"mesh": 118, "name": "3_Wheel.002_Scene_-_Root.002_0"}, {"mesh": 119, "name": "3_Wheel.002_amdb11_brake.002_0"}, {"mesh": 120, "name": "3_Wheel.002_amdb11_misc_chrome.002_0"}, {"mesh": 121, "name": "3_Wheel.002_amdb11_misc.002_0"}, {"mesh": 122, "name": "3_Wheel.002_amdb11_caliper.002_0"}, {"children": [212, 213], "matrix": [-110.0, -0.0, -0.0, -0.0, -0.0, -8.779219387010759e-06, -116.2842864990231, -0.0, -0.0, 116.2842864990231, -8.779219387010759e-06, -0.0, -90.0803451538086, 42.45737075805664, -153.13925170898438, 1.0], "name": "CLS500w219_sport_wheel2_white.003"}, {"mesh": 123, "name": "CLS500w219_sport_wheel2_white.003_CLS500_wheelvolklogo_0"}, {"mesh": 124, "name": "CLS500w219_sport_wheel2_white.003_CLS500_wheal_white_0"}, {"children": [215, 216, 217, 218, 219], "matrix": [-1.9790182600626126e-05, 5.6421944583616695e-15, -101.64073944091606, -0.0, 101.64073944091597, 4.442858008003346e-06, -1.9790182628837097e-05, -0.0, -4.442858330210397e-06, 101.64074707031243, 9.027511811005029e-13, -0.0, -89.29971313476562, 42.45737075805664, -153.13925170898438, 1.0], "name": "3_Wheel.003"}, {"mesh": 125, "name": "3_Wheel.003_Scene_-_Root.002_0"}, {"mesh": 126, "name": "3_Wheel.003_amdb11_brake.002_0"}, {"mesh": 127, "name": "3_Wheel.003_amdb11_misc_chrome.002_0"}, {"mesh": 128, "name": "3_Wheel.003_amdb11_misc.002_0"}, {"mesh": 129, "name": "3_Wheel.003_amdb11_caliper.002_0"}, {"children": [221], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.03393588215112686, 128.69126892089844, -59.967960357666016, 1.0], "name": "CLS500w219_interior.001"}, {"mesh": 130, "name": "CLS500w219_interior.001_CLS500_leather_white_0"}], "scene": 0, "scenes": [{"name": "Sketchfab_Scene", "nodes": [0]}]}