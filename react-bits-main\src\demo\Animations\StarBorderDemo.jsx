import { Box, Text } from "@chakra-ui/react";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";

import CodeExample from "../../components/code/CodeExample";
import PropTable from "../../components/common/PropTable";
import CliInstallation from "../../components/code/CliInstallation";

import StarBorder from "../../content/Animations/StarBorder/StarBorder";
import { starBorder } from "../../constants/code/Animations/starBorderCode";

const StarBorderDemo = () => {
  const propData = [
    {
      name: "as",
      type: "string",
      default: "button",
      description:
        "Allows specifying the type of the parent component to be rendered.",
    },
    {
      name: "className",
      type: "string",
      default: "-",
      description: "Allows adding custom classes to the component.",
    },
    {
      name: "color",
      type: "string",
      default: "white",
      description:
        "Changes the main color of the border (fades to transparent)",
    },
    {
      name: "speed",
      type: "string",
      default: "6s",
      description: "Changes the speed of the animation.",
    },
  ];

  return (
    <TabbedLayout data-oid="9hssfjy">
      <PreviewTab data-oid="ta_vnig">
        <h2 className="demo-title-extra" data-oid="xx_ygm.">
          Button
        </h2>
        <Box position="relative" className="demo-container" data-oid="9-xzevc">
          <StarBorder data-oid="gf53x-w">
            <Text mx={0} fontSize={"1em"} data-oid="i6pk8dm">
              Hello World!
            </Text>
          </StarBorder>
        </Box>

        <h2 className="demo-title-extra" data-oid="lgkx7wq">
          Container, custom color
        </h2>
        <Box position="relative" className="demo-container" data-oid="6a:vrw8">
          <StarBorder as="div" color="cyan" data-oid="8pl-dbl">
            <div
              style={{
                width: "200px",
                height: "100px",
                display: "grid",
                placeItems: "center",
              }}
              data-oid="t2slpmi"
            >
              Hello
            </div>
          </StarBorder>
        </Box>

        <PropTable data={propData} data-oid="gwob.ig" />
      </PreviewTab>

      <CodeTab data-oid="a3k-gp4">
        <CodeExample codeObject={starBorder} data-oid=".uqgs74" />
      </CodeTab>

      <CliTab data-oid="hyz65h2">
        <CliInstallation {...starBorder} data-oid="uamgsqh" />
      </CliTab>
    </TabbedLayout>
  );
};

export default StarBorderDemo;
