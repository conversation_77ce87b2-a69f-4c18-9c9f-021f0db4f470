import React from "react";
import CityTemplate from "./CityTemplate";
import { frankfurt, frankfurt1 } from "../../../assets";

const Frankfurt = () => {
  // City images
  const cityImages = [frankfurt, frankfurt1];

  // Top attractions
  const attractions = [
    {
      name: "Römerberg",
      description:
        "Historic square in the heart of Frankfurt's Old Town with beautiful medieval buildings and the iconic Römer city hall.",
    },
    {
      name: "Main Tower",
      description:
        "Skyscraper with observation deck offering panoramic views of Frankfurt's skyline and the Main River.",
    },
    {
      name: "Städel Museum",
      description:
        "One of Germany's most important art museums with an impressive collection spanning 700 years of European art.",
    },
    {
      name: "Palmengarten",
      description:
        "Beautiful botanical garden with diverse plant collections, greenhouses, and landscaped grounds.",
    },
    {
      name: "Goethe House",
      description:
        "Birthplace and childhood home of <PERSON>, Germany's most famous writer.",
    },
  ];

  // Recommended restaurants
  const restaurants = [
    {
      name: "Kleinmarkthalle",
      description:
        "Indoor market hall with food stalls offering local specialties, international cuisine, and fresh produce.",
    },
    {
      name: "Restaurant Lafleur",
      description:
        "Two Michelin-starred restaurant in the Palmengarten serving refined French-inspired cuisine.",
    },
    {
      name: "Apfelwein Wagner",
      description:
        "Traditional apple wine tavern serving hearty Frankfurt specialties like Handkäse and Grüne Soße.",
    },
    {
      name: "Emma Metzler",
      description:
        "Modern restaurant in the Museum of Applied Arts with seasonal German and European dishes.",
    },
  ];

  // Shopping areas
  const shopping = [
    {
      name: "Zeil",
      description:
        "Frankfurt's premier shopping street with department stores, fashion retailers, and the MyZeil shopping center.",
    },
    {
      name: "Goethestrasse",
      description:
        "Luxury shopping street with high-end designer boutiques and jewelry stores.",
    },
    {
      name: "Skyline Plaza",
      description:
        "Modern mall near the exhibition center with over 170 shops, restaurants, and a rooftop garden.",
    },
    {
      name: "Berger Strasse",
      description:
        "Vibrant street in the Nordend district with boutiques, cafés, and specialty shops.",
    },
  ];

  // Accommodation options
  const hotels = [
    {
      name: "Steigenberger Frankfurter Hof",
      description:
        "Historic luxury hotel in the city center with elegant rooms and Michelin-starred dining.",
    },
    {
      name: "Jumeirah Frankfurt",
      description:
        "Modern 5-star hotel with skyline views, spa facilities, and contemporary design.",
    },
    {
      name: "Villa Kennedy",
      description:
        "Luxurious hotel in a restored villa with beautiful courtyard garden and excellent amenities.",
    },
    {
      name: "25hours Hotel The Trip",
      description:
        "Quirky design hotel with travel-themed rooms and a popular rooftop bar.",
    },
  ];

  // Upcoming events
  const events = [
    {
      name: "Frankfurt Book Fair",
      date: "October",
      description:
        "The world's largest trade fair for books, attracting publishers, authors, and literary agents from around the globe.",
    },
    {
      name: "Museumsufer Festival",
      date: "August",
      description:
        "Cultural festival along the Main riverbank with art exhibitions, music, and food stalls.",
    },
    {
      name: "Christmas Market",
      date: "November-December",
      description:
        "One of Germany's oldest and most beautiful Christmas markets at Römerberg and St. Paul's Square.",
    },
    {
      name: "Frankfurt Marathon",
      date: "October",
      description:
        "Major international marathon through the streets of Frankfurt with thousands of participants.",
    },
  ];

  // Google Maps embed URL
  const mapUrl =
    "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d81880.***********!2d8.6018529!3d50.1109221!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47bd096f477096c5%3A0x422435029b0c600!2sFrankfurt%2C%20Germany!5e0!3m2!1sen!2sus!4v1653062799729!5m2!1sen!2sus";

  return (
    <CityTemplate
      cityKey="frankfurt"
      cityImages={cityImages}
      attractions={attractions}
      restaurants={restaurants}
      shopping={shopping}
      hotels={hotels}
      events={events}
      mapUrl={mapUrl}
      data-oid="t71qag-"
    />
  );
};

export default Frankfurt;
