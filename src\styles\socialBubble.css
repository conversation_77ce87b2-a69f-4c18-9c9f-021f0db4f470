.social-bubble-container {
  position: fixed;
  bottom: 30px;
  left: 30px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Main bubble styling */
.main-bubble {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #000000;
  border: 2px solid #D4AF37;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(212, 175, 55, 0.5), inset 0 0 10px rgba(212, 175, 55, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1001;
  position: relative;
  animation: pulse 2s infinite;
}

.main-bubble:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5), 0 0 0 2px rgba(212, 175, 55, 0.7), inset 0 0 15px rgba(212, 175, 55, 0.3);
  border-color: #F0C14B;
}

.main-bubble.open {
  transform: rotate(45deg) scale(1.1);
  background: #000000;
  border-color: #F0C14B;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5), 0 0 0 2px rgba(240, 193, 75, 0.7), inset 0 0 15px rgba(240, 193, 75, 0.3);
}

.bubble-icon {
  color: #D4AF37;
  font-size: 24px;
  font-weight: bold;
  transition: all 0.3s ease;
  text-shadow: 0 0 5px rgba(212, 175, 55, 0.5);
}

.main-bubble.open .bubble-icon {
  transform: rotate(-45deg);
}

/* Tooltip styling */
.bubble-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: #D4AF37;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  left: 70px;
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
  pointer-events: none;
  border: 1px solid rgba(212, 175, 55, 0.3);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.bubble-tooltip::before {
  content: '';
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 6px 6px 6px 0;
  border-style: solid;
  border-color: transparent rgba(0, 0, 0, 0.9) transparent transparent;
}

.main-bubble:hover .bubble-tooltip {
  opacity: 1;
  transform: translateX(0);
}

/* Social icons styling */
.social-icons {
  position: absolute;
  bottom: 70px;
  left: 10px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.social-icons.open {
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

.social-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #D4AF37;
  font-size: 20px;
  background: #000000;
  border: 2px solid #D4AF37;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3), inset 0 0 5px rgba(212, 175, 55, 0.2);
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-icon:hover {
  transform: scale(1.1) translateX(5px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4), inset 0 0 10px rgba(240, 193, 75, 0.3);
  border-color: #F0C14B;
  color: #F0C14B;
}

.social-icon::after {
  content: attr(title);
  position: absolute;
  left: 60px;
  background: rgba(0, 0, 0, 0.9);
  color: #D4AF37;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
  pointer-events: none;
  border: 1px solid rgba(212, 175, 55, 0.3);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.social-icon::before {
  content: '';
  position: absolute;
  left: 55px;
  border-width: 6px 6px 6px 0;
  border-style: solid;
  border-color: transparent rgba(0, 0, 0, 0.9) transparent transparent;
  opacity: 0;
  transition: all 0.3s ease;
}

.social-icon:hover::after,
.social-icon:hover::before {
  opacity: 1;
  transform: translateX(0);
}

/* Individual social icon colors - all using the same luxury theme */
.whatsapp {
  animation: slideIn 0.3s ease forwards;
}

.email {
  animation: slideIn 0.3s ease 0.1s forwards;
}

.instagram {
  animation: slideIn 0.3s ease 0.2s forwards;
}

.phone {
  animation: slideIn 0.3s ease 0.3s forwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .social-bubble-container {
    bottom: 20px;
    left: 20px;
  }

  .main-bubble {
    width: 50px;
    height: 50px;
  }

  .social-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
}

/* Ensure the bubble is visible in fullscreen mode */
:fullscreen .social-bubble-container,
::backdrop .social-bubble-container {
  z-index: 10000;
}

/* Pulse animation for the main bubble */
@keyframes pulse {
  0% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(212, 175, 55, 0.5), inset 0 0 10px rgba(212, 175, 55, 0.2);
  }
  50% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5), 0 0 0 4px rgba(212, 175, 55, 0.3), inset 0 0 10px rgba(212, 175, 55, 0.2);
  }
  100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(212, 175, 55, 0.5), inset 0 0 10px rgba(212, 175, 55, 0.2);
  }
}
