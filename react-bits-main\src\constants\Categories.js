// Highlighted sidebar items
export const NEW = ['Silk', 'Scrambled Text', 'Dot Grid', 'Beams', 'Chroma Grid', 'G<PERSON>e Hover', 'Profile Card', 'Card Swap'];
export const UPDATED = ['Blob Cursor', 'Split Text'];

// Used for main sidebar navigation
export const CATEGORIES = [
  {
    name: 'Text Animations',
    subcategories: [
      'Split Text',
      'Blur Text',
      'Circular Text',
      'Shiny Text',
      'Text Pressure',
      'Fuzzy Text',
      'Gradient Text',
      'Falling Text',
      'Text Cursor',
      'Decrypted Text',
      'True Focus',
      'Scroll Float',
      'Scroll Reveal',
      'ASCII Text',
      'Scrambled Text',
      'Rotating Text',
      'Glitch Text',
      'Scroll Velocity',
      'Variable Proximity',
      'Count Up'
    ]
  },
  {
    name: 'Animations',
    subcategories: [
      'Animated Content',
      'Fade Content',
      'Pixel Transition',
      'Glare Hover',
      'Magnet Lines',
      'Click Spark',
      'Magnet',
      'Pixel Trail',
      'Metallic Paint',
      'Noise',
      'Crosshair',
      'Image Trail',
      'Ribbons',
      'Splash Cursor',
      'Meta Balls',
      'Blob Cursor',
      'Star Border'
    ]
  },
  {
    name: 'Components',
    subcategories: [
      'Animated List',
      'Stack',
      'Tilted Card',
      'Chroma Grid',
      'Folder',
      'Lanyard',
      'Profile Card',
      'Dock',
      'Gooey Nav',
      'Pixel Card',
      'Circular Gallery',
      'Carousel',
      'Spotlight Card',
      'Flying Posters',
      'Card Swap',
      'Infinite Scroll',
      'Glass Icons',
      'Decay Card',
      'Flowing Menu',
      'Elastic Slider',
      'Counter',
      'Infinite Menu',
      'Rolling Gallery',
      'Stepper',
      'Bounce Cards'
    ],
  },
  {
    name: 'Backgrounds',
    subcategories: [
      'Silk',
      'Aurora',
      'Beams',
      'Lightning',
      'Balatro',
      'Dither',
      'Shape Blur',
      'Dot Grid',
      'Threads',
      'Hyperspeed',
      'Iridescence',
      'Grid Distortion',
      'Ballpit',
      'Orb',
      'Grid Motion',
      'Liquid Chrome',
      'Squares',
      'Letter Glitch',
      'Particles',
      'Waves'
    ],
  }
];