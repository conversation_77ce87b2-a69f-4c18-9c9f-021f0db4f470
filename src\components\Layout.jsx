import React, { useEffect } from "react";
import { Navbar, Footer } from "./";
import { Outlet } from "react-router-dom";
import { useLocation } from "react-router-dom";
import PocketFlowChatBot from "./ChatBot/PocketFlowChatBot";
import Hyperspeed from "./ReactBits/Hyperspeed";
import SocialBubble from "./SocialBubble";

const Layout = () => {
  const location = useLocation();
  const isHomePage = location.pathname === "/";

  // Add event listener for fullscreen changes to ensure background color is maintained
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (document.fullscreenElement) {
        document.documentElement.style.backgroundColor = "#000000";
        document.body.style.backgroundColor = "#000000";
      }
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);

    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
    };
  }, []);

  return (
    <div
      className="relative z-0 bg-black black-gold-bg min-h-screen"
      data-oid="mm2shw7"
    >
      {/* Hyperspeed Background Effect */}
      <Hyperspeed data-oid="xgop:nh" />

      {/* Navbar floats on top with transparent background */}
      <Navbar data-oid="8eqciy1" />

      {/* Main content with proper padding to account for navbar */}
      <div
        className={`relative z-10 ${isHomePage ? "pt-0" : "pt-[100px]"}`}
        data-oid="azqyx2_"
      >
        <Outlet data-oid="9z18gn6" />
      </div>

      <Footer data-oid="0wypkd." />

      {/* PocketFlow Chat Bot */}
      <PocketFlowChatBot data-oid="p15jeib" />

      {/* Social Media Bubble */}
      <SocialBubble data-oid="-f9_xux" />
    </div>
  );
};

export default Layout;
