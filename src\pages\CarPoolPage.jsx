import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../contexts/LanguageContext";
import { styles } from "../styles";
import { fadeIn, textVariant } from "../utils/motion";
import "../styles/carpool.css";

// Carpool items data
const carpoolItems = [
  {
    key: "s_class",
    title: "Mercedes S-Class",
    icon: "/src/assets/S_Class/s1.jpg", // S-Class image from assets
    iconBg: "linear-gradient(135deg, #000000, #111111)",
    link: "/carpool/mercedes-sclass",
  },
  {
    key: "v_class",
    title: "Mercedes V-Class",
    icon: "/src/assets/V_Class/v1.jpg", // V-Class image from assets
    iconBg: "linear-gradient(135deg, #000000, #111111)",
    link: "/carpool/mercedes-vclass",
  },
  {
    key: "bmw7",
    title: "BMW 7",
    icon: "/src/assets/BMW7/bmw1.jpg", // BMW 7 image from assets
    iconBg: "linear-gradient(135deg, #000000, #111111)",
    link: "/carpool/bmw-7",
  },
];

const CarPoolPage = () => {
  const { t } = useTranslation();
  const { language } = useLanguage();

  return (
    <div className="relative z-0" data-oid="u7s:ams">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid="xrae89o"
      >
        <motion.div
          variants={textVariant()}
          className="section-header"
          data-oid="mvmc2pc"
        >
          <p
            className={`${styles.sectionSubText} text-[#D4AF37]`}
            data-oid="1.ldky:"
          >
            {language === "ar" ? t("carpool.subtitle") : "PREMIUM VEHICLES"}
          </p>
          <h2 className={`${styles.sectionHeadText}`} data-oid="owqqsja">
            {language === "ar" ? t("carpool.title") : "Carpool"}
          </h2>
          <div className="title-underline" data-oid="wbqk:x7"></div>
        </motion.div>

        <motion.p
          variants={fadeIn("", "", 0.1, 1)}
          className="section-description"
          data-oid="w44t1ai"
        >
          {language === "ar"
            ? t("carpool.description")
            : "Our premium carpool service offers comfortable and reliable transportation options for groups of all sizes. Whether you're traveling for business or leisure, our fleet of luxury vehicles ensures a smooth and enjoyable journey. We prioritize safety, punctuality, and customer satisfaction with every ride."}
        </motion.p>

        <div className="vehicle-grid" data-oid="4t8ema4">
          {carpoolItems.map((vehicle, index) => (
            <motion.div
              key={vehicle.key}
              variants={fadeIn("up", "spring", index * 0.3, 0.75)}
              className="vehicle-card-container"
              data-oid="7:mpul."
            >
              <div className="vehicle-card" data-oid="8ndqfz2">
                <div className="vehicle-image-container" data-oid="..r3045">
                  <img
                    src={vehicle.icon}
                    alt={
                      language === "ar"
                        ? t(`carpool.${vehicle.key}`)
                        : vehicle.title
                    }
                    className="vehicle-image"
                    data-oid="ivv14uf"
                  />
                </div>
                <div className="vehicle-content" data-oid="ke2j5yv">
                  <h3 className="vehicle-title" data-oid="330g84o">
                    {language === "ar"
                      ? t(`carpool.${vehicle.key}`)
                      : vehicle.title}
                  </h3>
                  <Link
                    to={vehicle.link}
                    className="vehicle-link"
                    data-oid="anslcyy"
                  >
                    {language === "ar"
                      ? t("common.viewDetails")
                      : "View Details"}
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          variants={fadeIn("", "", 0.2, 1)}
          className="features-section"
          data-oid="09.5.ai"
        >
          <h2 className="section-title" data-oid="eauxqph">
            {language === "ar"
              ? t("carpool.why-choose-title")
              : "Why Choose Our Carpool Service?"}
          </h2>

          <div className="features-grid" data-oid="fd_sb_c">
            <motion.div
              variants={fadeIn("right", "spring", 0.3, 0.75)}
              className="feature-card"
              data-oid="bk844nh"
            >
              <div className="feature-icon" data-oid="fdwueb2">
                <span className="icon-circle" data-oid="hpo:ch6">
                  ✓
                </span>
              </div>
              <h3 className="feature-title" data-oid="7h6vlq8">
                {language === "ar"
                  ? t("carpool.professional-chauffeurs")
                  : "Professional Chauffeurs"}
              </h3>
              <p className="feature-description" data-oid="mosncly">
                {language === "ar"
                  ? t("carpool.professional-chauffeurs-desc")
                  : "Our experienced and professionally trained chauffeurs ensure your safety and comfort throughout your journey."}
              </p>
            </motion.div>

            <motion.div
              variants={fadeIn("up", "spring", 0.4, 0.75)}
              className="feature-card"
              data-oid="d5o_7bd"
            >
              <div className="feature-icon" data-oid="z7fznqb">
                <span className="icon-circle" data-oid="n:2bm.i">
                  ✓
                </span>
              </div>
              <h3 className="feature-title" data-oid="scksx.4">
                {language === "ar" ? t("carpool.luxury-fleet") : "Luxury Fleet"}
              </h3>
              <p className="feature-description" data-oid="w6n5c-e">
                {language === "ar"
                  ? t("carpool.luxury-fleet-desc")
                  : "Travel in style with our meticulously maintained luxury vehicles with premium amenities."}
              </p>
            </motion.div>

            <motion.div
              variants={fadeIn("left", "spring", 0.5, 0.75)}
              className="feature-card"
              data-oid="sno72cb"
            >
              <div className="feature-icon" data-oid="zn3i2.j">
                <span className="icon-circle" data-oid="dw_hppc">
                  ✓
                </span>
              </div>
              <h3 className="feature-title" data-oid="x37lluu">
                {language === "ar"
                  ? t("carpool.customized-service")
                  : "Customized Service"}
              </h3>
              <p className="feature-description" data-oid="x04_:c-">
                {language === "ar"
                  ? t("carpool.customized-service-desc")
                  : "We tailor our service to meet your specific needs for any occasion."}
              </p>
            </motion.div>
          </div>
        </motion.div>

        <motion.div
          variants={fadeIn("up", "spring", 0.6, 0.75)}
          className="cta-section"
          data-oid="o.o3ioa"
        >
          <h2 className="cta-title" data-oid="fwxyiho">
            {language === "ar"
              ? t("carpool.ready-to-book")
              : "Ready to Experience Luxury?"}
          </h2>
          <p className="cta-description" data-oid="h.2.s3i">
            {language === "ar"
              ? t("carpool.booking-description")
              : "Contact us today to reserve your luxury transportation and experience the difference of our premium chauffeur service."}
          </p>
          <Link to="/contact" className="cta-button" data-oid="8oha_t3">
            {language === "ar" ? t("common.bookNow") : "Book Now"}
          </Link>
        </motion.div>
      </div>
    </div>
  );
};

export default CarPoolPage;
