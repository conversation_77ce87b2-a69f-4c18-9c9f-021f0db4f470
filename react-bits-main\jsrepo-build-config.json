{"$schema": "https://unpkg.com/jsrepo@1.30.1/schemas/registry-config.json", "meta": {"authors": ["<PERSON>"], "description": "An open source collection of animated, interactive & fully customizable React components for building stunning, memorable user interfaces.", "bugs": "https://github.com/David<PERSON>ev/react-bits/issues", "homepage": "https://reactbits.dev", "repository": "https://github.com/David<PERSON>ev/react-bits", "tags": ["react", "javascript", "components", "web", "reactjs", "css-animations", "component-library", "ui-components", "3d", "ui-library", "tailwind", "tailwindcss", "components", "components-library"]}, "dirs": [], "doNotListBlocks": [], "doNotListCategories": [], "listBlocks": [], "listCategories": [], "excludeDeps": ["react"], "includeBlocks": [], "includeCategories": [], "excludeBlocks": [], "excludeCategories": [], "preview": true}