/* RTL Timeline Specific Styles - Complete Rewrite */

/* Basic RTL direction for all timeline elements */
[dir="rtl"] .vertical-timeline,
[dir="rtl"] .vertical-timeline-element,
[dir="rtl"] .vertical-timeline-element-content {
  direction: rtl !important;
  text-align: right !important;
}

/* Center line positioning */
[dir="rtl"] .vertical-timeline::before {
  left: 50% !important;
  margin-left: -2px !important;
}

/* Timeline element spacing */
[dir="rtl"] .vertical-timeline-element {
  margin: 4em 0 !important;
}

/* Icon positioning */
[dir="rtl"] .vertical-timeline-element-icon {
  left: auto !important;
  right: 50% !important;
  margin-right: -30px !important;
  margin-left: 0 !important;
}

/* Content box positioning - ONE COLUMN LAYOUT */
[dir="rtl"] .vertical-timeline--one-column-left .vertical-timeline-element-content,
[dir="rtl"] .vertical-timeline--one-column-right .vertical-timeline-element-content {
  margin-left: 0 !important;
  margin-right: 60px !important;
}

/* Content arrow styling */
[dir="rtl"] .vertical-timeline-element-content-arrow {
  border-right: none !important;
  border-left: 7px solid #000000 !important;
  right: auto !important;
  left: -7px !important;
}

/* List styling */
[dir="rtl"] .vertical-timeline-element-content ul {
  margin-right: 1.25rem !important;
  margin-left: 0 !important;
  padding-right: 0 !important;
}

[dir="rtl"] .vertical-timeline-element-content li {
  text-align: right !important;
  padding-right: 0.25rem !important;
  padding-left: 0 !important;
}

/* TWO COLUMN LAYOUT SPECIFIC STYLES */
@media only screen and (min-width: 1170px) {
  /* Content positioning for odd elements (right side) */
  [dir="rtl"] .vertical-timeline--two-columns .vertical-timeline-element:nth-child(odd) .vertical-timeline-element-content {
    float: right !important;
    left: auto !important;
    right: 0 !important;
    margin-right: 0 !important;
    margin-left: auto !important;
    text-align: right !important;
  }

  /* Content positioning for even elements (left side) */
  [dir="rtl"] .vertical-timeline--two-columns .vertical-timeline-element:nth-child(even) .vertical-timeline-element-content {
    float: left !important;
    right: auto !important;
    left: 0 !important;
    margin-left: 0 !important;
    margin-right: auto !important;
    text-align: right !important;
  }

  /* Content arrow for odd elements (right side) */
  [dir="rtl"] .vertical-timeline--two-columns .vertical-timeline-element:nth-child(odd) .vertical-timeline-element-content-arrow {
    border-left: 7px solid #000000 !important;
    border-right: none !important;
    left: -7px !important;
    right: auto !important;
  }

  /* Content arrow for even elements (left side) */
  [dir="rtl"] .vertical-timeline--two-columns .vertical-timeline-element:nth-child(even) .vertical-timeline-element-content-arrow {
    border-right: 7px solid #000000 !important;
    border-left: none !important;
    right: -7px !important;
    left: auto !important;
  }

  /* Date positioning for odd elements (right side) */
  [dir="rtl"] .vertical-timeline--two-columns .vertical-timeline-element:nth-child(odd) .vertical-timeline-element-date {
    left: 124% !important;
    right: auto !important;
    text-align: left !important;
  }

  /* Date positioning for even elements (left side) */
  [dir="rtl"] .vertical-timeline--two-columns .vertical-timeline-element:nth-child(even) .vertical-timeline-element-date {
    right: 124% !important;
    left: auto !important;
    text-align: right !important;
  }
}

/* Force RTL for specific elements */
.rtl-timeline .vertical-timeline {
  direction: rtl !important;
}

.rtl-timeline-element {
  direction: rtl !important;
}

.rtl-date {
  text-align: left !important;
}

/* Fix for content width in two-column layout */
@media only screen and (min-width: 1170px) {
  [dir="rtl"] .vertical-timeline--two-columns .vertical-timeline-element-content {
    width: 44% !important;
  }
}

/* Force RTL class for JavaScript manipulation */
.rtl-force.vertical-timeline--two-columns::before {
  left: 50% !important;
  margin-left: -2px !important;
}

.rtl-force.vertical-timeline--two-columns .vertical-timeline-element:nth-child(odd) .vertical-timeline-element-content {
  float: right !important;
  left: auto !important;
  right: 0 !important;
  margin-right: 0 !important;
  margin-left: auto !important;
  text-align: right !important;
}

.rtl-force.vertical-timeline--two-columns .vertical-timeline-element:nth-child(even) .vertical-timeline-element-content {
  float: left !important;
  right: auto !important;
  left: 0 !important;
  margin-left: 0 !important;
  margin-right: auto !important;
  text-align: right !important;
}

/* Fix for RTL timeline element icon positioning */
[dir="rtl"] .vertical-timeline-element-icon {
  transform: none !important;
  left: auto !important;
  right: 50% !important;
  margin-right: -30px !important;
  margin-left: 0 !important;
}

/* Fix for RTL date positioning in two-column layout */
@media only screen and (min-width: 1170px) {
  /* Ensure dates are positioned correctly for odd elements */
  .rtl-force.vertical-timeline--two-columns .vertical-timeline-element:nth-child(odd) .vertical-timeline-element-date {
    left: 124% !important;
    right: auto !important;
    text-align: left !important;
  }

  /* Ensure dates are positioned correctly for even elements */
  .rtl-force.vertical-timeline--two-columns .vertical-timeline-element:nth-child(even) .vertical-timeline-element-date {
    right: 124% !important;
    left: auto !important;
    text-align: right !important;
  }

  /* Fix for content arrows */
  .rtl-force.vertical-timeline--two-columns .vertical-timeline-element:nth-child(odd) .vertical-timeline-element-content-arrow {
    border-left: 7px solid #000000 !important;
    border-right: none !important;
    left: -7px !important;
    right: auto !important;
  }

  .rtl-force.vertical-timeline--two-columns .vertical-timeline-element:nth-child(even) .vertical-timeline-element-content-arrow {
    border-right: 7px solid #000000 !important;
    border-left: none !important;
    right: -7px !important;
    left: auto !important;
  }
}
