import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Box } from "@chakra-ui/react";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";

import FlowingMenu from "../../content/Components/FlowingMenu/FlowingMenu";
import { flowingMenu } from "../../constants/code/Components/flowingMenuCode";

const FlowingMenuDemo = () => {
  const propData = [
    {
      name: "items",
      type: "object[]",
      default: "[]",
      description: "An array of object scontaining: link, text, image.",
    },
  ];

  const demoItems = [
    {
      link: "#",
      text: "Mojave",
      image: "https://picsum.photos/600/400?random=1",
    },
    {
      link: "#",
      text: "Sonoma",
      image: "https://picsum.photos/600/400?random=2",
    },
    {
      link: "#",
      text: "Monterey",
      image: "https://picsum.photos/600/400?random=3",
    },
    {
      link: "#",
      text: "Sequoia",
      image: "https://picsum.photos/600/400?random=4",
    },
  ];

  return (
    <TabbedLayout data-oid="q.lyop4">
      <PreviewTab data-oid="k_0pity">
        <Box
          position="relative"
          className="demo-container"
          h={600}
          overflow="hidden"
          px={0}
          pt="100px"
          pb="100px"
          data-oid="thu6-9u"
        >
          <FlowingMenu items={demoItems} data-oid=":_omho6" />
        </Box>

        <PropTable data={propData} data-oid="khxgh0k" />
        <Dependencies dependencyList={["gsap"]} data-oid="0yehhh_" />
      </PreviewTab>

      <CodeTab data-oid="_c8km99">
        <CodeExample codeObject={flowingMenu} data-oid="4wtuwvz" />
      </CodeTab>

      <CliTab data-oid="i3yye1d">
        <CliInstallation {...flowingMenu} data-oid="8jjihua" />
      </CliTab>
    </TabbedLayout>
  );
};

export default FlowingMenuDemo;
