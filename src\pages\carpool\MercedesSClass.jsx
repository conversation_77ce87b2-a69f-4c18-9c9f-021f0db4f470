import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { styles } from "../../styles";
import { fadeIn, textVariant } from "../../utils/motion";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../../contexts/LanguageContext";
import RollingGallery from "../../components/ReactBits/RollingGallery";
import ModelViewer from "../../components/ReactBits/ModelViewer";

import "../../styles/carpool.css";



const MercedesSClass = () => {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const [isMobile, setIsMobile] = useState(false);

  const images = [
    "/src/assets/S_Class/s1.jpg",
    "/src/assets/S_Class/s2.jpg",
    "/src/assets/S_Class/s3.jpg",
    "/src/assets/S_Class/s4.jpg",
  ];

  // Fallback image in case the original images don't load
  const fallbackImage =
    "https://images.unsplash.com/photo-1563720223185-11003d516935?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80";

  useEffect(() => {
    // Add a listener for changes to the screen size
    const mediaQuery = window.matchMedia("(max-width: 500px)");

    // Set the initial value of the `isMobile` state variable
    setIsMobile(mediaQuery.matches);

    // Define a callback function to handle changes to the media query
    const handleMediaQueryChange = (event) => {
      setIsMobile(event.matches);
    };

    // Add the callback function as a listener for changes to the media query
    mediaQuery.addEventListener("change", handleMediaQueryChange);

    // Remove the listener when the component is unmounted
    return () => {
      mediaQuery.removeEventListener("change", handleMediaQueryChange);
    };
  }, []);

  return (
    <div className="carpool-container black-gold-bg" data-oid="dimlzm3">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid="has.rv9"
      >
        <motion.div
          variants={fadeIn("", "", 0.1, 1)}
          className="carpool-frame"
          data-oid="tpmxx7z"
        >
          <motion.div
            variants={textVariant()}
            className="carpool-header"
            data-oid="3d80t1-"
          >
            <p className="carpool-subtitle" data-oid="wd.9g3j">
              {language === "ar"
                ? t("common.premiumVehicle")
                : "Premium Vehicle"}
            </p>
            <h2 className="carpool-title" data-oid="p:_c2w0">
              {language === "ar"
                ? t("vehicles.mercedes-sclass.title")
                : "Mercedes S-Class"}
            </h2>
            <p className="carpool-description" data-oid="9f92cs9">
              {language === "ar"
                ? t("vehicles.mercedes-sclass.description")
                : "The pinnacle of luxury sedans, offering unparalleled comfort and sophisticated design."}
            </p>
          </motion.div>

          {/* 3D Model Viewer Section */}
          <div className="model-viewer-section" data-oid="tr1:hzl">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="model-viewer-wrapper"
            >
              <ModelViewer
                url="/Mercedes_S_class/scene.gltf"
                width={400}
                height={450}
                defaultRotationX={-30}
                defaultRotationY={30}
                defaultZoom={2}
                minZoomDistance={1}
                maxZoomDistance={5}
                enableManualRotation={true}
                enableManualZoom={true}
                enableMouseParallax={true}
                enableHoverRotation={true}
                autoRotate={true}
                autoRotateSpeed={0.5}
                environmentPreset="studio"
                ambientIntensity={0.4}
                keyLightIntensity={1.2}
                fillLightIntensity={0.6}
                rimLightIntensity={0.8}
                fadeIn={true}
                showScreenshotButton={false}
              />
            </motion.div>
          </div>
        </motion.div>

        {/* Rolling Gallery Section */}
        <motion.div
          variants={fadeIn("", "", 0.2, 1)}
          className="rolling-gallery-section"
          data-oid="gallery-section"
        >
          <h3 className="section-title" data-oid="gallery-title">
            {language === "ar"
              ? t("vehicles.mercedes-sclass.gallery")
              : "Vehicle Gallery"}
          </h3>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="rolling-gallery-wrapper"
          >
            <RollingGallery
              images={images}
              autoplay={true}
              pauseOnHover={true}
            />
          </motion.div>
        </motion.div>



        <div className="cta-container" data-oid="0:q:f87">
          <div className="car-info-section" data-oid="d0k2zeo">
            <h3 className="section-title" data-oid="nkrso0s">
              {language === "ar"
                ? t("vehicles.mercedes-sclass.premium-services")
                : "Premium Services & Luxury Experience"}
            </h3>
            <div className="specs-grid mb-6" data-oid="2z:7kml">
              <div className="specs-category" data-oid="gi47bx2">
                <ul className="specs-list" data-oid="w3quyh8">
                  {language === "ar" ? (
                    // Arabic services list (first half)
                    t("vehicles.mercedes-sclass.services-list", {
                      returnObjects: true,
                    })
                      .slice(0, 4)
                      .map((service, index) => (
                        <li
                          key={index}
                          className="specs-item"
                          data-oid="a:yxuc0"
                        >
                          <span className="specs-item-icon" data-oid="jftj_ym">
                            •
                          </span>{" "}
                          {service}
                        </li>
                      ))
                  ) : (
                    // English services list (first half)
                    <>
                      <li className="specs-item" data-oid="b4kegc3">
                        <span className="specs-item-icon" data-oid="s8mezh9">
                          •
                        </span>{" "}
                        Professional chauffeur service
                      </li>
                      <li className="specs-item" data-oid="sxy425u">
                        <span className="specs-item-icon" data-oid="dek8q8a">
                          •
                        </span>{" "}
                        Complimentary Wi-Fi onboard
                      </li>
                      <li className="specs-item" data-oid="-gtijbs">
                        <span className="specs-item-icon" data-oid="j8m68do">
                          •
                        </span>{" "}
                        Premium refreshments
                      </li>
                      <li className="specs-item" data-oid="7csbnb-">
                        <span className="specs-item-icon" data-oid="6h0:fci">
                          •
                        </span>{" "}
                        Daily newspapers and magazines
                      </li>
                    </>
                  )}
                </ul>
              </div>
              <div className="specs-category" data-oid="k5hn0k6">
                <ul className="specs-list" data-oid="t9:237o">
                  {language === "ar" ? (
                    // Arabic services list (second half)
                    t("vehicles.mercedes-sclass.services-list", {
                      returnObjects: true,
                    })
                      .slice(4)
                      .map((service, index) => (
                        <li
                          key={index + 4}
                          className="specs-item"
                          data-oid="mz4km53"
                        >
                          <span className="specs-item-icon" data-oid="m9hoji6">
                            •
                          </span>{" "}
                          {service}
                        </li>
                      ))
                  ) : (
                    // English services list (second half)
                    <>
                      <li className="specs-item" data-oid="4pa-hgb">
                        <span className="specs-item-icon" data-oid="rig6ilu">
                          •
                        </span>{" "}
                        Advanced driver assistance systems for maximum safety
                      </li>
                      <li className="specs-item" data-oid="dte3j7s">
                        <span className="specs-item-icon" data-oid="tcyt6r4">
                          •
                        </span>{" "}
                        Premium Burmester® surround sound system
                      </li>
                      <li className="specs-item" data-oid="ancl970">
                        <span className="specs-item-icon" data-oid="8sz1p-q">
                          •
                        </span>{" "}
                        Ambient lighting with 64 colors
                      </li>
                      <li className="specs-item" data-oid="e7gntrn">
                        <span className="specs-item-icon" data-oid="n45drg.">
                          •
                        </span>{" "}
                        Spacious cabin with executive rear seating
                      </li>
                    </>
                  )}
                </ul>
              </div>
            </div>
            <p className="section-content" data-oid="7dq6y2q">
              {language === "ar"
                ? t("vehicles.mercedes-sclass.book-cta")
                : "Book your premium Mercedes S-Class chauffeur service today and enjoy the ultimate in comfort and style."}
            </p>
            <div className="flex justify-center mt-6" data-oid="xv1ji7y">
              <Link to="/contact" className="cta-button" data-oid="ufvn8a6">
                {language === "ar" ? t("common.bookNow") : "Book Now"}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MercedesSClass;
