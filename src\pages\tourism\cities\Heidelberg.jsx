import React from "react";
import CityTemplate from "./CityTemplate";
import { muenchen2 } from "../../../assets";

const Heidelberg = () => {
  // City images - using placeholder images since we don't have specific Heidelberg images
  const cityImages = [
    muenchen2, // Using as placeholder
    "https://images.unsplash.com/photo-1501621667575-af81f1f0bacc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    "https://images.unsplash.com/photo-1467269204594-9661b134dd2b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
  ];

  // Top attractions
  const attractions = [
    {
      name: "Heidelberg Castle",
      description:
        "Iconic partially ruined castle overlooking the city, offering stunning views and rich history.",
    },
    {
      name: "Old Bridge (Alte Brücke)",
      description:
        "Historic stone bridge across the Neckar River with a medieval bridge gate and beautiful views.",
    },
    {
      name: "Philosophers' Walk (Philosophenweg)",
      description:
        "Scenic pathway on the northern bank of the Neckar with panoramic views of the Old Town and castle.",
    },
    {
      name: "Old Town (Altstadt)",
      description:
        "Charming historic center with baroque architecture, cobblestone streets, and lively squares.",
    },
    {
      name: "Heidelberg University",
      description:
        "Germany's oldest university founded in 1386, with historic buildings throughout the city.",
    },
  ];

  // Recommended restaurants
  const restaurants = [
    {
      name: "Schnitzelbank",
      description:
        "Cozy traditional restaurant in the Old Town serving authentic German cuisine in a historic setting.",
    },
    {
      name: "Zum Güldenen Schaf",
      description:
        "One of Heidelberg's oldest restaurants dating back to 1749, offering regional specialties.",
    },
    {
      name: "Kulturbrauerei",
      description:
        "Brewery restaurant with a beer garden serving Heidelberg's local craft beers and hearty German food.",
    },
    {
      name: "Heidelberg Suites Restaurant",
      description:
        "Elegant dining with terrace views of the castle and river, serving refined European cuisine.",
    },
  ];

  // Shopping areas
  const shopping = [
    {
      name: "Hauptstrasse",
      description:
        "One of Europe's longest pedestrian shopping streets with a mix of international brands and local shops.",
    },
    {
      name: "Heumarkt",
      description:
        "Market square with specialty shops, boutiques, and cafés in historic buildings.",
    },
    {
      name: "Heidelberger Weihnachtsmarkt",
      description:
        "Christmas market at multiple squares in the Old Town, perfect for unique gifts and crafts (seasonal).",
    },
    {
      name: "Handschuhsheim District",
      description:
        "Charming area with small boutiques, antique shops, and specialty stores.",
    },
  ];

  // Accommodation options
  const hotels = [
    {
      name: "Heidelberg Suites Boutique Hotel",
      description:
        "Luxury boutique hotel with stunning views of the castle and elegant suites in a historic villa.",
    },
    {
      name: "Hotel Europäischer Hof",
      description:
        "Heidelberg's grand 5-star hotel with traditional elegance, spa facilities, and excellent service.",
    },
    {
      name: "Hotel Zum Ritter St. Georg",
      description:
        "Historic hotel in a Renaissance building on the market square, offering character and central location.",
    },
    {
      name: "Boutique Hotel Heidelberg Suites",
      description:
        "Stylish hotel on the banks of the Neckar with panoramic views and contemporary design.",
    },
  ];

  // Upcoming events
  const events = [
    {
      name: "Heidelberg Castle Illumination",
      date: "June, July, and September",
      description:
        "Spectacular fireworks display over the castle and Neckar River, a tradition dating back to 1613.",
    },
    {
      name: "Heidelberger Herbst",
      date: "September",
      description:
        "Annual autumn festival in the Old Town with music, food, crafts, and entertainment.",
    },
    {
      name: "Heidelberg Literature Festival",
      date: "March",
      description:
        "International literary festival featuring readings, discussions, and workshops with renowned authors.",
    },
    {
      name: "Heidelberger Frühling",
      date: "March-April",
      description:
        "Classical music festival with performances by world-class orchestras and musicians.",
    },
  ];

  // Google Maps embed URL
  const mapUrl =
    "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d41731.93657253493!2d8.6724197!3d49.4093582!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4797c1050eccdccd%3A0xefe6ea0044243ad7!2sHeidelberg%2C%20Germany!5e0!3m2!1sen!2sus!4v1653063799729!5m2!1sen!2sus";

  return (
    <CityTemplate
      cityKey="heidelberg"
      cityImages={cityImages}
      attractions={attractions}
      restaurants={restaurants}
      shopping={shopping}
      hotels={hotels}
      events={events}
      mapUrl={mapUrl}
      data-oid="hs5p7ch"
    />
  );
};

export default Heidelberg;
