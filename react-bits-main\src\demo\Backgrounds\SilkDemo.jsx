import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Box, Flex, Text, Input } from "@chakra-ui/react";

import Customize from "../../components/common/Customize";
import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import useForceRerender from "../../hooks/useForceRerender";
import RefreshButton from "../../components/common/RefreshButton";
import PreviewSlider from "../../components/common/PreviewSlider";

import { silkCode } from "../../constants/code/Backgrounds/silkCode";
import Silk from "../../content/Backgrounds/Silk/Silk";

const SilkDemo = () => {
  const [speed, setSpeed] = useState(5);
  const [scale, setScale] = useState(1);
  const [color, setColor] = useState("#7B7481");
  const [noiseIntensity, setNoiseIntensity] = useState(1.5);
  const [rotation, setRotation] = useState(0);

  const [key, forceRerender] = useForceRerender();

  const propData = [
    {
      name: "speed",
      type: "number",
      default: "5",
      description: "Controls the animation speed of the silk effect.",
    },
    {
      name: "scale",
      type: "number",
      default: "1",
      description: "Controls the scale of the silk pattern.",
    },
    {
      name: "color",
      type: "string",
      default: "'#7B7481'",
      description: "Hex color code for the silk pattern.",
    },
    {
      name: "noiseIntensity",
      type: "number",
      default: "1.5",
      description: "Controls the intensity of the noise effect.",
    },
    {
      name: "rotation",
      type: "number",
      default: "0",
      description: "Controls the rotation of the silk pattern (in radians).",
    },
  ];

  return (
    <TabbedLayout data-oid="xr7eg4v">
      <PreviewTab data-oid=":iwite:">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          overflow="hidden"
          p={0}
          data-oid="2cyk7y7"
        >
          <RefreshButton onClick={forceRerender} data-oid="p.hsd8l" />
          <Silk
            key={key}
            speed={speed}
            scale={scale}
            color={color}
            noiseIntensity={noiseIntensity}
            rotation={rotation}
            data-oid="vq4scys"
          />
        </Box>

        <Customize data-oid="ktbb-g6">
          <PreviewSlider
            title="Speed"
            min={0.1}
            max={20}
            step={0.1}
            value={speed}
            onChange={(val) => {
              setSpeed(val);
              forceRerender();
            }}
            data-oid="_tc7aq-"
          />

          <PreviewSlider
            title="Scale"
            min={0.1}
            max={5}
            step={0.1}
            value={scale}
            onChange={(val) => {
              setScale(val);
              forceRerender();
            }}
            data-oid="wf06cmx"
          />

          <PreviewSlider
            title="Noise Intensity"
            min={0}
            max={10}
            step={0.1}
            value={noiseIntensity}
            onChange={(val) => {
              setNoiseIntensity(val);
              forceRerender();
            }}
            data-oid="3ps5e82"
          />

          <PreviewSlider
            title="Rotation"
            min={0}
            max={Math.PI * 2}
            step={0.01}
            value={rotation}
            onChange={(val) => {
              setRotation(val);
              forceRerender();
            }}
            data-oid="s.4k1b."
          />

          <Flex align="center" gap={2} mt={4} data-oid="ecdkqo-">
            <Text fontSize="sm" data-oid="z0-kvmw">
              Color
            </Text>
            <Input
              type="color"
              value={color}
              onChange={(e) => {
                setColor(e.target.value);
                forceRerender();
              }}
              width="100px"
              data-oid="xvzk4n."
            />
          </Flex>
        </Customize>

        <PropTable data={propData} data-oid="i0virc:" />
        <Dependencies
          dependencyList={["three", "@react-three/fiber"]}
          data-oid="b7cl_d7"
        />
      </PreviewTab>

      <CodeTab data-oid="5res6oe">
        <CodeExample codeObject={silkCode} data-oid="a9t-6w." />
      </CodeTab>

      <CliTab data-oid="yq75z9u">
        <CliInstallation {...silkCode} data-oid="8m96yzk" />
      </CliTab>
    </TabbedLayout>
  );
};

export default SilkDemo;
