import React from "react";
import Hyperspeed from "./components/ReactBits/Hyperspeed";
import "./components/ReactBits/Hyperspeed.css";

const HyperspeedDemo = () => {
  // Custom options (optional)
  const customOptions = {
    // You can override any of the default luxury preset options here
    speedUp: 2.0, // Increase speed boost effect
    fovSpeedUp: 160, // Increase field of view during speed boost
    colors: {
      // Custom colors
      leftCars: [0xff0000, 0xff3333, 0xff6666], // Red cars
      rightCars: [0x00ffff, 0x33ffff, 0x66ffff], // Cyan cars
      shoulderLines: 0xd4af37, // Gold lines
    },
  };

  return (
    <div className="app">
      {/* The Hyperspeed component will fill the entire viewport as a background */}
      <Hyperspeed effectOptions={customOptions} />
      
      {/* Content overlay */}
      <div className="content">
        <h1>Hyperspeed Demo</h1>
        <p>Click or tap anywhere to activate speed boost!</p>
      </div>
      
      {/* Inline styles for the demo */}
      <style jsx="true">{`
        .app {
          width: 100vw;
          height: 100vh;
          overflow: hidden;
          position: relative;
        }
        
        .content {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;
          color: white;
          z-index: 10;
          padding: 20px;
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 10px;
          max-width: 80%;
        }
        
        h1 {
          color: #d4af37; /* Gold color */
          margin-bottom: 20px;
        }
      `}</style>
    </div>
  );
};

export default HyperspeedDemo;