import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Box, Button, ButtonGroup, Flex, Text } from "@chakra-ui/react";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import useForceRerender from "../../hooks/useForceRerender";

import { imageTrail } from "../../constants/code/Animations/imageTrailCode";
import ImageTrail from "../../ts-default/Animations/ImageTrail/ImageTrail";

const ImageTrailDemo = () => {
  const [variant, setVariant] = useState("1");
  const [key, forceRerender] = useForceRerender();

  const propData = [
    {
      name: "items",
      type: "string[]",
      default: "[]",
      description:
        "An array of image URLs which will be animated in the trail.",
    },
    {
      name: "variant",
      type: "number",
      default: "1",
      description: "A number from 1 to 8 - all different animation styles.",
    },
  ];

  return (
    <TabbedLayout data-oid="y4w:fox">
      <PreviewTab data-oid="nljc1am">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          overflow="hidden"
          data-oid="e3mm2g:"
        >
          <ImageTrail
            key={key}
            items={[
              "https://picsum.photos/id/287/300/300",
              "https://picsum.photos/id/1001/300/300",
              "https://picsum.photos/id/1025/300/300",
              "https://picsum.photos/id/1026/300/300",
              "https://picsum.photos/id/1027/300/300",
              "https://picsum.photos/id/1028/300/300",
              "https://picsum.photos/id/1029/300/300",
              "https://picsum.photos/id/1030/300/300",
              // ...
            ]}
            variant={variant}
            data-oid="eq1e7ws"
          />

          <Flex
            position="absolute"
            justifyContent="center"
            flexDirection="column"
            alignItems="center"
            data-oid="lvlwewi"
          >
            <Text
              fontSize="clamp(2rem, 6vw, 6rem)"
              fontWeight={900}
              color="#222"
              mb={0}
              data-oid="nmyatn6"
            >
              Hover Me.
            </Text>
            <Text
              fontSize="18px"
              fontWeight={900}
              color="#a6a6a6"
              mt={0}
              data-oid="s6md4sg"
            >
              Variant {variant}
            </Text>
          </Flex>
        </Box>

        <div className="preview-options" data-oid="::fl3ni">
          <h2 className="demo-title-extra" data-oid="lwndazp">
            Customize
          </h2>
          <Flex gap={6} direction="column" data-oid="q23:ano">
            <ButtonGroup isAttached size="sm" data-oid="a:quk2k">
              <Button
                fontSize="xs"
                h={8}
                bg="#a1a1aa"
                isDisabled
                _disabled={{
                  bg: "#222",
                  cursor: "not-allowed",
                  _hover: { bg: "#222" },
                }}
                data-oid="ftv.ggj"
              >
                Variant
              </Button>
              {[1, 2, 3, 4, 5, 6, 7, 8].map((num) => {
                const isActive = variant === String(num);

                return (
                  <Button
                    key={num}
                    bg={isActive ? "#00f0ff" : "#111"}
                    _hover={{ backgroundColor: isActive ? "#00f0ff" : "#111" }}
                    color={isActive ? "black" : "white"}
                    fontSize="xs"
                    h={8}
                    onClick={() => {
                      setVariant(String(num));
                      forceRerender();
                    }}
                    data-oid="s.quatf"
                  >
                    {num}
                  </Button>
                );
              })}
            </ButtonGroup>
          </Flex>
        </div>

        <PropTable data={propData} data-oid="j485dsd" />
        <Dependencies dependencyList={["gsap"]} data-oid="86kd19j" />
      </PreviewTab>

      <CodeTab data-oid="k-l6q1g">
        <CodeExample codeObject={imageTrail} data-oid="a_4yqt1" />
      </CodeTab>

      <CliTab data-oid="sjh4b56">
        <CliInstallation {...imageTrail} data-oid="d.-4tn6" />
      </CliTab>
    </TabbedLayout>
  );
};

export default ImageTrailDemo;
