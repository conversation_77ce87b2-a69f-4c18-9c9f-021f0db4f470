import React from "react";
import { Link } from "react-router-dom";
import { navLinks } from "../constants";
import Rotating<PERSON>ogo from "./RotatingLogo";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../contexts/LanguageContext";
import {
  FaFacebookF,
  FaTwitter,
  FaInstagram,
  FaWhatsapp,
  FaCcVisa,
  FaCcMastercard,
  FaCcAmex,
  FaCcPaypal,
  FaMapMarkerAlt,
  FaPhone,
  FaEnvelope,
} from "react-icons/fa";

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const { t } = useTranslation();
  const { dir } = useLanguage();

  return (
    <footer className="bg-black-100 py-5" data-oid="q2rfxn_">
      <div className="max-w-7xl mx-auto px-6 lg:px-8" data-oid="r:70-q8">
        <div
          className="grid grid-cols-1 md:grid-cols-4 gap-4"
          data-oid="hmqgkoq"
        >
          {/* Logo and Company Info */}
          <div className="col-span-1" data-oid="0u3c_t6">
            <Link
              to="/"
              className="flex items-center gap-2 mb-2"
              data-oid="uy5stjd"
            >
              <RotatingLogo data-oid="8wgvkcl" />
              <p
                className="text-white text-[16px] font-bold tracking-wider uppercase"
                data-oid="-f0x3j-"
              >
                Premium Chauffeur
              </p>
            </Link>
            <p className="text-secondary text-[12px]" data-oid="q-zu2po">
              {t("footer.companyInfo")}
            </p>
          </div>

          {/* Quick Links */}
          <div className="col-span-1" data-oid=":3ggb88">
            <h3
              className="text-white text-[14px] font-bold mb-2"
              data-oid="flhjmja"
            >
              {t("footer.quickLinks")}
            </h3>
            <div
              className="grid grid-cols-2 gap-x-2 gap-y-1"
              data-oid="9jp8az5"
            >
              {navLinks.map((link) => (
                <Link
                  key={link.id}
                  to={`/${link.id}`}
                  className="text-secondary text-[12px] hover:text-gold-100 transition-colors duration-150"
                  data-oid="ry4sgao"
                >
                  {t(`navbar.${link.id}`)}
                </Link>
              ))}
            </div>
          </div>

          {/* Legal Information */}
          <div className="col-span-1" data-oid="4u_lmpj">
            <h3
              className="text-white text-[14px] font-bold mb-2"
              data-oid="jmeufjn"
            >
              {t("footer.legal")}
            </h3>
            <div
              className="grid grid-cols-2 gap-x-2 gap-y-1"
              data-oid="bivn6ti"
            >
              <Link
                to="/privacy-policy"
                className="text-secondary text-[12px] hover:text-gold-100 transition-colors duration-150"
                data-oid="..zsenk"
              >
                {t("footer.privacyPolicy")}
              </Link>
              <Link
                to="/terms-of-service"
                className="text-secondary text-[12px] hover:text-gold-100 transition-colors duration-150"
                data-oid="vudhi0w"
              >
                {t("footer.termsOfService")}
              </Link>
              <Link
                to="/cookie-policy"
                className="text-secondary text-[12px] hover:text-gold-100 transition-colors duration-150"
                data-oid="uqj.5cw"
              >
                {t("footer.cookiePolicy")}
              </Link>
              <Link
                to="/imprint"
                className="text-secondary text-[12px] hover:text-gold-100 transition-colors duration-150"
                data-oid="hsxr43_"
              >
                {t("footer.imprint")}
              </Link>
            </div>
          </div>

          {/* Contact Information */}
          <div className="col-span-1" data-oid="miijd.c">
            <h3
              className="text-white text-[14px] font-bold mb-2"
              data-oid="p_-4:gy"
            >
              {t("navbar.contact")}
            </h3>
            <ul className="space-y-1" data-oid="m2dtgf1">
              <li
                className="flex items-center text-secondary text-[12px]"
                data-oid="y0xc63u"
              >
                <FaMapMarkerAlt
                  className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-secondary`}
                  data-oid="vnr.1d-"
                />
                <span
                  className={`${dir === "rtl" ? "ml-1" : "mr-1"} text-white text-[12px]`}
                  data-oid="lohca1p"
                >
                  {t("footer.address")}:
                </span>
                Luxusstraße 123, 10115 Berlin
              </li>
              <li
                className="flex items-center text-secondary text-[12px]"
                data-oid="hh842w2"
              >
                <FaPhone
                  className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-secondary`}
                  data-oid="4dbzgn-"
                />
                <span
                  className={`${dir === "rtl" ? "ml-1" : "mr-1"} text-white text-[12px]`}
                  data-oid="dv_292."
                >
                  {t("footer.phone")}:
                </span>
                +49 ************
              </li>
              <li
                className="flex items-center text-secondary text-[12px]"
                data-oid="j6jklbt"
              >
                <FaEnvelope
                  className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-secondary`}
                  data-oid="pk3i6p-"
                />
                <span
                  className={`${dir === "rtl" ? "ml-1" : "mr-1"} text-white text-[12px]`}
                  data-oid="vxe1820"
                >
                  {t("footer.email")}:
                </span>
                <EMAIL>
              </li>
            </ul>
          </div>
        </div>

        {/* Payment Methods and Social Media */}
        <div
          className="flex flex-col sm:flex-row justify-between items-center border-t border-gray-800 pt-3 mt-3"
          data-oid="x2n6xjq"
        >
          {/* Payment Methods */}
          <div className="flex gap-2 mb-3 sm:mb-0" data-oid=":b38ucs">
            <FaCcVisa className="text-secondary text-2xl" data-oid="p4ieuc3" />
            <FaCcMastercard
              className="text-secondary text-2xl"
              data-oid=".83g20c"
            />

            <FaCcAmex className="text-secondary text-2xl" data-oid="_yt9-dr" />
            <FaCcPaypal
              className="text-secondary text-2xl"
              data-oid="vz4pako"
            />
          </div>

          {/* Social Media Links */}
          <div className="flex gap-3" data-oid="qt2-82:">
            <a
              href="https://facebook.com"
              target="_blank"
              rel="noopener noreferrer"
              className="w-8 h-8 rounded-full bg-tertiary flex items-center justify-center hover:bg-secondary transition-colors duration-150"
              data-oid="wtp9k4e"
            >
              <FaFacebookF
                className="text-white hover:text-black-200"
                data-oid="niu2tbv"
              />
            </a>
            <a
              href="https://twitter.com"
              target="_blank"
              rel="noopener noreferrer"
              className="w-8 h-8 rounded-full bg-tertiary flex items-center justify-center hover:bg-secondary transition-colors duration-150"
              data-oid="q63h4as"
            >
              <FaTwitter
                className="text-white hover:text-black-200"
                data-oid="zlfkd-3"
              />
            </a>
            <a
              href="https://instagram.com"
              target="_blank"
              rel="noopener noreferrer"
              className="w-8 h-8 rounded-full bg-tertiary flex items-center justify-center hover:bg-secondary transition-colors duration-150"
              data-oid="gw-q45y"
            >
              <FaInstagram
                className="text-white hover:text-black-200"
                data-oid="y-nsth_"
              />
            </a>
            <a
              href="https://wa.me/491234567890"
              target="_blank"
              rel="noopener noreferrer"
              className="w-8 h-8 rounded-full bg-tertiary flex items-center justify-center hover:bg-secondary transition-colors duration-150"
              data-oid="ujcpf:l"
            >
              <FaWhatsapp
                className="text-white hover:text-black-200"
                data-oid="544sj:0"
              />
            </a>
          </div>
        </div>

        {/* Copyright */}
        <div className="pt-3 mt-3 text-center" data-oid="ivkm3v-">
          <p className="text-secondary text-[12px]" data-oid="w47ujdz">
            {t("footer.copyright", { year: currentYear })}
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
