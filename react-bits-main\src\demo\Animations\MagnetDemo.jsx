import { useState } from "react";
import { Box, Flex, Text } from "@chakra-ui/react";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import PreviewSlider from "../../components/common/PreviewSlider";
import PreviewSwitch from "../../components/common/PreviewSwitch";
import Customize from "../../components/common/Customize";

import Magnet from "../../content/Animations/Magnet/Magnet";
import { magnet } from "../../constants/code/Animations/magnetCode";

const MagnetDemo = () => {
  const [disabled, setDisabled] = useState(false);
  const [padding, setPadding] = useState(100);
  const [magnetStrength, setMagnetStrength] = useState(2);

  const propData = [
    {
      name: "padding",
      type: "number",
      default: 100,
      description:
        "Specifies the distance (in pixels) around the element that activates the magnet pull.",
    },
    {
      name: "disabled",
      type: "boolean",
      default: false,
      description: "Disables the magnet effect when set to true.",
    },
    {
      name: "magnetStrength",
      type: "number",
      default: 2,
      description:
        "Controls the strength of the pull; higher values reduce movement, lower values increase it.",
    },
    {
      name: "activeTransition",
      type: "string",
      default: '"transform 0.3s ease-out"',
      description:
        "CSS transition applied to the element when the magnet is active.",
    },
    {
      name: "inactiveTransition",
      type: "string",
      default: '"transform 0.5s ease-in-out"',
      description:
        "CSS transition applied when the magnet is inactive (mouse out of range).",
    },
    {
      name: "wrapperClassName",
      type: "string",
      default: '""',
      description: "Optional CSS class name for the outermost wrapper element.",
    },
    {
      name: "innerClassName",
      type: "string",
      default: '""',
      description: "Optional CSS class name for the moving (inner) element.",
    },
    {
      name: "children",
      type: "ReactNode",
      default: "",
      description:
        "The content (JSX) to be displayed inside the magnetized element.",
    },
  ];

  return (
    <TabbedLayout data-oid="70s-too">
      <PreviewTab data-oid="_u2ts.t">
        <h2 className="demo-title-extra" data-oid="h9rz3x5">
          Container
        </h2>
        <Box
          position="relative"
          className="demo-container"
          minH={300}
          data-oid="547ba88"
        >
          <Magnet
            padding={padding}
            disabled={disabled}
            magnetStrength={magnetStrength}
            data-oid="wxvlu9g"
          >
            <Flex
              w={200}
              h={100}
              fontSize="xl"
              fontWeight="bolder"
              color="#fff"
              bg="#060606"
              border="1px solid #222"
              borderRadius="20px"
              justifyContent="center"
              alignItems="center"
              data-oid="k7axdnh"
            >
              Hover Me!
            </Flex>
          </Magnet>
        </Box>

        <h2 className="demo-title-extra" data-oid="15k7uw_">
          Link
        </h2>
        <Box
          position="relative"
          className="demo-container"
          minH={300}
          data-oid=":15dpnt"
        >
          <Magnet
            padding={Math.floor(padding / 2)}
            disabled={disabled}
            magnetStrength={magnetStrength}
            data-oid="n3ee4-b"
          >
            <a
              href="https://github.com/DavidHDev/react-bits"
              target="_blank"
              rel="noreferrer"
              data-oid="e7f0bkc"
            >
              <Flex fontSize="lg" color="#fff" data-oid="avmb2ke">
                Star&nbsp;
                <Text color="#00f0ff" data-oid="gb.r-ck">
                  React Bits
                </Text>
                &nbsp;on GitHub!
              </Flex>
            </a>
          </Magnet>
        </Box>

        <Customize data-oid="oi.ii5:">
          <PreviewSwitch
            title="Disabled"
            isChecked={disabled}
            onChange={(e) => setDisabled(e.target.checked)}
            data-oid="vme47.i"
          />

          <PreviewSlider
            title="Padding"
            min={0}
            max={300}
            step={10}
            value={padding}
            valueUnit="px"
            onChange={setPadding}
            data-oid="66-i1ly"
          />

          <PreviewSlider
            title="Strength"
            min={1}
            max={10}
            step={1}
            value={magnetStrength}
            onChange={setMagnetStrength}
            data-oid="pjsxook"
          />
        </Customize>

        <PropTable data={propData} data-oid="dvea3z." />
      </PreviewTab>

      <CodeTab data-oid="zsrbjzi">
        <CodeExample codeObject={magnet} data-oid="xaya0ti" />
      </CodeTab>

      <CliTab data-oid="fsvi5-m">
        <CliInstallation {...magnet} data-oid="y8hd.u5" />
      </CliTab>
    </TabbedLayout>
  );
};

export default MagnetDemo;
