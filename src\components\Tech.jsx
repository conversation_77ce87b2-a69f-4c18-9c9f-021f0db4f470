import React from "react";
import { SectionWrapper } from "../hoc";
import { technologies } from "../constants";

const Tech = () => (
  <div
    className="flex flex-row flex-wrap justify-center gap-10"
    data-oid="1ebqgvk"
  >
    {technologies.map(({ name, icon }) => (
      <div
        className="w-28 h-28 flex flex-col items-center justify-center"
        key={name}
        data-oid="ewf5m:8"
      >
        <img
          src={icon}
          alt={name}
          className="w-16 h-16 object-contain"
          data-oid="rbjl5n9"
        />

        <p className="text-white text-sm mt-2" data-oid="t8koogq">
          {name}
        </p>
      </div>
    ))}
  </div>
);

export default SectionWrapper(Tech, "");
