import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Box, Flex, Input, Text } from "@chakra-ui/react";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import PreviewSlider from "../../components/common/PreviewSlider";
import PreviewSwitch from "../../components/common/PreviewSwitch";
import Customize from "../../components/common/Customize";

import Particles from "../../ts-default/Backgrounds/Particles/Particles";
import { particles } from "../../constants/code/Backgrounds/particlesCode";

const ParticlesDemo = () => {
  const [colors, setColors] = useState("#ffffff");

  const [particleCount, setParticleCount] = useState(200);
  const [particleSpread, setParticleSpread] = useState(10);
  const [speed, setSpeed] = useState(0.1);
  const [baseSize, setBaseSize] = useState(100);

  const [moveParticlesOnHover, setMoveParticlesOnHover] = useState(true);
  const [alphaParticles, setAlphaParticles] = useState(false);
  const [disableRotation, setDisableRotation] = useState(false);

  const propData = [
    {
      name: "particleCount",
      type: "number",
      default: "200",
      description: "The number of particles to generate.",
    },
    {
      name: "particleSpread",
      type: "number",
      default: "10",
      description: "Controls how far particles are spread from the center.",
    },
    {
      name: "speed",
      type: "number",
      default: "0.1",
      description: "Speed factor controlling the animation pace.",
    },
    {
      name: "particleColors",
      type: "string[]",
      default: "['#ffffff']",
      description: "An array of hex color strings used to color the particles.",
    },
    {
      name: "moveParticlesOnHover",
      type: "boolean",
      default: "false",
      description:
        "Determines if particles should move in response to mouse hover.",
    },
    {
      name: "particleHoverFactor",
      type: "number",
      default: "1",
      description: "Multiplier for the particle movement when hovering.",
    },
    {
      name: "alphaParticles",
      type: "boolean",
      default: "false",
      description:
        "If true, particles are rendered with varying transparency; otherwise, as solid circles.",
    },
    {
      name: "particleBaseSize",
      type: "number",
      default: "100",
      description: "The base size of the particles.",
    },
    {
      name: "sizeRandomness",
      type: "number",
      default: "1",
      description:
        "Controls the variation in particle sizes (0 means all particles have the same size).",
    },
    {
      name: "cameraDistance",
      type: "number",
      default: "20",
      description: "Distance from the camera to the particle system.",
    },
    {
      name: "disableRotation",
      type: "boolean",
      default: "false",
      description: "If true, stops the particle system from rotating.",
    },
  ];

  return (
    <TabbedLayout data-oid="-u9gv1y">
      <PreviewTab data-oid="x--oamb">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          p={0}
          overflow="hidden"
          data-oid="kj2ec56"
        >
          <Particles
            particleColors={[colors]}
            particleCount={particleCount}
            particleSpread={particleSpread}
            speed={speed}
            particleBaseSize={baseSize}
            moveParticlesOnHover={moveParticlesOnHover}
            alphaParticles={alphaParticles}
            disableRotation={disableRotation}
            data-oid="py6v5ry"
          />
        </Box>

        <Customize data-oid="sub8.q9">
          <Flex gap={4} align="center" mt={4} data-oid="mquuty_">
            <Text fontSize="sm" data-oid="loca.lx">
              Color
            </Text>
            <Input
              type="color"
              value={colors}
              onChange={(e) => {
                setColors(e.target.value);
              }}
              width="50px"
              data-oid="u9i:2zn"
            />

            <Text fontSize="sm" data-oid="ij57icd">
              {colors}
            </Text>
          </Flex>

          <PreviewSlider
            title="Count"
            min={100}
            max={1000}
            step={100}
            value={particleCount}
            onChange={setParticleCount}
            data-oid="nqyw00s"
          />

          <PreviewSlider
            title="Spread"
            min={10}
            max={100}
            step={10}
            value={particleSpread}
            onChange={setParticleSpread}
            data-oid="it:3t_3"
          />

          <PreviewSlider
            title="Speed"
            min={0}
            max={2}
            step={0.1}
            value={speed}
            onChange={setSpeed}
            data-oid="4qtqnqd"
          />

          <PreviewSlider
            title="Base Size"
            min={100}
            max={1000}
            step={100}
            value={baseSize}
            onChange={setBaseSize}
            data-oid="b7r..qo"
          />

          <PreviewSwitch
            title="Mouse Interaction"
            isChecked={moveParticlesOnHover}
            onChange={(e) => setMoveParticlesOnHover(e.target.checked)}
            data-oid="l2tehq-"
          />

          <PreviewSwitch
            title="Particle Transparency"
            isChecked={alphaParticles}
            onChange={(e) => setAlphaParticles(e.target.checked)}
            data-oid="3p2494n"
          />

          <PreviewSwitch
            title="Disable Rotation"
            isChecked={disableRotation}
            onChange={(e) => setDisableRotation(e.target.checked)}
            data-oid="5yn74_v"
          />
        </Customize>

        <PropTable data={propData} data-oid="a5iai83" />
        <Dependencies dependencyList={["ogl"]} data-oid="_khungd" />
      </PreviewTab>

      <CodeTab data-oid="59pc0l-">
        <CodeExample codeObject={particles} data-oid="_ehkncj" />
      </CodeTab>

      <CliTab data-oid=".2jflq_">
        <CliInstallation {...particles} data-oid=".we0w1e" />
      </CliTab>
    </TabbedLayout>
  );
};

export default ParticlesDemo;
