import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { close, menu } from "../assets";
import { navLinks } from "../constants";
import { styles } from "../styles";
import "./Navbar.css";
import Rotating<PERSON>ogo from "./RotatingLogo";
import LanguageSwitcher from "./LanguageSwitcher";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../contexts/LanguageContext";

const Navbar = () => {
  const [active, setActive] = useState("");
  const [toggle, setToggle] = useState(false);
  const [mobileDropdowns, setMobileDropdowns] = useState({});
  const [scrolled, setScrolled] = useState(false);
  const { t } = useTranslation();
  const { dir, language } = useLanguage();

  useEffect(() => {
    if (toggle) {
      setActive("");
      setMobileDropdowns({});
    }
  }, [toggle]);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      if (scrollTop > 100) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const toggleMobileDropdown = (id) => {
    setMobileDropdowns((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const renderNavLinks = (isSecondary) => (
    <ul
      className={`list-none ${isSecondary ? "flex flex-col sm:hidden" : "hidden sm:flex"} ${isSecondary ? "" : "flex-row"} gap-6`}
      data-oid="tab3968"
    >
      {navLinks.map((link) => (
        <li
          key={link.id}
          className={`${
            active === link.title
              ? "text-white"
              : isSecondary
                ? "text-secondary"
                : "text-white"
          } hover:text-white text-[20px] font-medium cursor-pointer nav-item drop-shadow-[0_2px_2px_rgba(0,0,0,0.5)]`}
          data-oid="fyysqdi"
        >
          {link.dropdown ? (
            <>
              {isSecondary ? (
                // Mobile dropdown
                <div data-oid="zuga_de">
                  <div
                    className="mobile-dropdown-toggle"
                    onClick={() => toggleMobileDropdown(link.id)}
                    data-oid="o.zlt7s"
                  >
                    <span data-oid="b9jsqs8">{t(`navbar.${link.id}`)}</span>
                    <span
                      className={`dropdown-arrow ${mobileDropdowns[link.id] ? "dropdown-arrow-active" : ""}`}
                      data-oid="q9t:xp7"
                    >
                      ▼
                    </span>
                  </div>
                  <div
                    className={`mobile-dropdown-menu ${mobileDropdowns[link.id] ? "mobile-dropdown-active" : ""}`}
                    data-oid="fz5071v"
                  >
                    {link.dropdown.map((item) => (
                      <Link
                        key={item.id}
                        to={`/${link.id}/${item.id}`}
                        className="mobile-dropdown-item block"
                        onClick={() => {
                          setActive(item.title);
                          setToggle(false);
                        }}
                        data-oid=":0cd6jk"
                      >
                        {t(`${link.id}.${item.id}`)}
                      </Link>
                    ))}
                  </div>
                </div>
              ) : (
                // Desktop dropdown
                <>
                  <Link
                    to={`/${link.id}`}
                    onClick={() => setActive(link.title)}
                    className="flex items-center"
                    data-oid="0hb4h9p"
                  >
                    {t(`navbar.${link.id}`)}
                    <span
                      className={`dropdown-arrow ${dir === "rtl" ? "mr-1" : "ml-1"}`}
                      data-oid="avf-5m3"
                    >
                      ▼
                    </span>
                  </Link>
                  <div className="dropdown-menu" data-oid="m.bsk:g">
                    {link.dropdown.map((item) => (
                      <Link
                        key={item.id}
                        to={`/${link.id}/${item.id}`}
                        className="dropdown-item"
                        onClick={() => setActive(item.title)}
                        data-oid="4zyv-m3"
                      >
                        {t(`${link.id}.${item.id}`)}
                      </Link>
                    ))}
                  </div>
                </>
              )}
            </>
          ) : (
            // Regular link without dropdown
            <Link
              to={`/${link.id}`}
              onClick={() => {
                setActive(link.title);
                if (isSecondary) {
                  setToggle(false);
                }
              }}
              data-oid="vu6vey0"
            >
              {t(`navbar.${link.id}`)}
            </Link>
          )}
        </li>
      ))}
    </ul>
  );

  return (
    <>
      <nav
        className={`${styles.paddingX} w-full flex items-center py-3 fixed top-0 z-30 ${
          scrolled ? "bg-black/30" : "bg-transparent"
        } backdrop-blur-sm transition-all duration-300`}
        data-oid="p2rbno4"
      >
        <div
          className="w-full flex justify-between items-center max-w-7xl mx-auto"
          data-oid=".i46lhw"
        >
          <Link
            to="/"
            className="flex items-center gap-2"
            onClick={() => {
              setActive("");
              window.scrollTo(0, 0);
            }}
            data-oid="qz8q1xy"
          >
            <RotatingLogo data-oid="q::fm2o" />
            <p
              className="text-white text-[16px] sm:text-[20px] font-bold cursor-pointer flex flex-wrap sm:flex-nowrap tracking-wider uppercase drop-shadow-[0_2px_2px_rgba(0,0,0,0.5)]"
              data-oid="phuo.jp"
            >
              {language === "ar" ? (
                <>
                  <span className="text-[#D4AF37]" data-oid="5m4e6dm">
                    Chauffeur
                  </span>
                  &nbsp;
                  <span data-oid="fxtkbzd">Premium</span>
                </>
              ) : (
                <>
                  <span data-oid="1xk93q3">Premium</span>&nbsp;
                  <span className="text-[#D4AF37]" data-oid="c3_qp7r">
                    Chauffeur
                  </span>
                </>
              )}
            </p>
          </Link>
          <div className="hidden sm:flex items-center gap-6" data-oid="j_mg..s">
            {renderNavLinks(false)}
            <LanguageSwitcher data-oid="fq6es2d" />
          </div>
          <div
            className="sm:hidden flex flex-1 justify-end items-center"
            data-oid="sdz9b-e"
          >
            <img
              src={toggle ? close : menu}
              alt="menu"
              className="w-[32px] h-[24px] object-contain cursor-pointer mr-3 menu-icon"
              onClick={() => setToggle(!toggle)}
              aria-label={toggle ? "Close menu" : "Open menu"}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  setToggle(!toggle);
                }
              }}
              data-oid="uokdc55"
            />

            <div data-oid="g49859k">
              <LanguageSwitcher data-oid="3xln._k" />
            </div>
            <div
              className={`p-6 absolute top-14 ${dir === "rtl" ? "left-0" : "right-0"} mx-2 my-2 min-w-[280px] z-10 rounded-xl backdrop-blur-md bg-black/80 border-2 border-secondary/30 shadow-lg ${
                toggle ? "flex animate-fadeIn" : "hidden"
              }`}
              style={{ flexDirection: "column" }}
              data-oid=":4skjoj"
            >
              {renderNavLinks(true)}
            </div>
          </div>
        </div>
      </nav>
    </>
  );
};

export default Navbar;
