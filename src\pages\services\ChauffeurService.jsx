import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { styles } from "../../styles";
import { fadeIn, textVariant } from "../../utils/motion";
import { StarsCanvas } from "../../components/canvas";
import { chauffeur1, chauffeur2, chauffeur3 } from "../../assets";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../../contexts/LanguageContext";

const ChauffeurService = () => {
  const { t } = useTranslation();
  const { dir } = useLanguage();

  return (
    <div className="relative w-full h-auto mx-auto" data-oid="l1pwzb0">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid="wu9_xnz"
      >
        <motion.div variants={textVariant()} data-oid="_mzh9l3">
          <p
            className={`${styles.sectionSubText} text-center text-[#D4AF37]`}
            data-oid="nhsicu7"
          >
            {t("service-pages.common.our-services")}
          </p>
          <h2
            className={`${styles.sectionHeadText} text-center`}
            data-oid="wp:nan-"
          >
            {t("service-pages.chauffeur-service.title")}
          </h2>
        </motion.div>

        <motion.p
          variants={fadeIn("", "", 0.1, 1)}
          className="mt-4 text-white text-[17px] max-w-3xl leading-[30px] text-center mx-auto"
          style={{ direction: dir }}
          data-oid="g5u772e"
        >
          {t("service-pages.chauffeur-service.description")}
        </motion.p>

        <div
          className="mt-10 bg-black rounded-2xl p-8 border border-[#D4AF37]"
          data-oid="2gp:qv0"
        >
          <h3
            className="text-[#D4AF37] font-bold text-[24px] text-center"
            style={{ direction: dir }}
            data-oid="arbmgbd"
          >
            {t("service-pages.common.service-features")}
          </h3>
          <ul
            className="mt-5 list-disc ml-5 space-y-2"
            style={{
              direction: dir,
              textAlign: dir === "rtl" ? "right" : "left",
            }}
            data-oid="0sy1cmr"
          >
            <li
              className="text-white text-[17px] pl-1 tracking-wider flex items-center"
              data-oid="jfrv161"
            >
              <span
                className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-[#D4AF37]`}
                data-oid="f:-mowk"
              >
                •
              </span>{" "}
              {dir === "rtl"
                ? "سائقون محترفون بزي رسمي مع تدريب مكثف"
                : "Professional, uniformed chauffeurs with extensive training"}
            </li>
            <li
              className="text-white text-[17px] pl-1 tracking-wider flex items-center"
              data-oid="4kgy28:"
            >
              <span
                className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-[#D4AF37]`}
                data-oid="i56lfe0"
              >
                •
              </span>{" "}
              {dir === "rtl"
                ? "خدمة دقيقة المواعيد مع مراقبة الرحلات في الوقت الفعلي للاستقبال من المطار"
                : "Punctual service with real-time flight monitoring for airport pickups"}
            </li>
            <li
              className="text-white text-[17px] pl-1 tracking-wider flex items-center"
              data-oid="ugz4olh"
            >
              <span
                className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-[#D4AF37]`}
                data-oid="klgp8l8"
              >
                •
              </span>{" "}
              {dir === "rtl"
                ? "وسائل راحة مجانية تشمل المياه المعبأة وخدمة الواي فاي والصحف"
                : "Complimentary amenities including bottled water, Wi-Fi, and newspapers"}
            </li>
            <li
              className="text-white text-[17px] pl-1 tracking-wider flex items-center"
              data-oid="d_w2ej7"
            >
              <span
                className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-[#D4AF37]`}
                data-oid="a2jgema"
              >
                •
              </span>{" "}
              {dir === "rtl"
                ? "خيارات حجز مرنة للخدمة بالساعة أو اليومية أو الأسبوعية"
                : "Flexible booking options for hourly, daily, or weekly service"}
            </li>
            <li
              className="text-white text-[17px] pl-1 tracking-wider flex items-center"
              data-oid="m7tfh.3"
            >
              <span
                className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-[#D4AF37]`}
                data-oid="rcnjq_i"
              >
                •
              </span>{" "}
              {dir === "rtl"
                ? "دعم العملاء وتوفر الخدمة على مدار الساعة طوال أيام الأسبوع"
                : "24/7 customer support and service availability"}
            </li>
          </ul>
        </div>

        <div className="mt-20" data-oid="6mec0pw">
          <h3
            className="text-[#D4AF37] font-bold text-[24px] mb-6 text-center"
            style={{ direction: dir }}
            data-oid="44986es"
          >
            {t("service-pages.chauffeur-service.experience-title")}
          </h3>
          <div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            data-oid="pmf.x9k"
          >
            <motion.div
              variants={fadeIn("right", "spring", 0.3, 0.75)}
              className="rounded-lg overflow-hidden shadow-lg border border-[#D4AF37]"
              data-oid="qz1.y8:"
            >
              <img
                src={chauffeur1}
                alt={t(
                  "service-pages.chauffeur-service.professional-chauffeurs",
                )}
                className="w-full h-64 object-cover"
                data-oid="29p4ynb"
              />

              <div className="p-4 bg-black" data-oid="n1fmjod">
                <h4
                  className="text-[#D4AF37] font-semibold text-[18px]"
                  style={{
                    direction: dir,
                    textAlign: dir === "rtl" ? "right" : "left",
                  }}
                  data-oid="n6he0zu"
                >
                  {t("service-pages.chauffeur-service.professional-chauffeurs")}
                </h4>
                <p
                  className="text-white text-[14px] mt-2"
                  style={{
                    direction: dir,
                    textAlign: dir === "rtl" ? "right" : "left",
                  }}
                  data-oid="9c_m35g"
                >
                  {t(
                    "service-pages.chauffeur-service.professional-chauffeurs-desc",
                  )}
                </p>
              </div>
            </motion.div>

            <motion.div
              variants={fadeIn("up", "spring", 0.4, 0.75)}
              className="rounded-lg overflow-hidden shadow-lg border border-[#D4AF37]"
              data-oid="su.i7ep"
            >
              <img
                src={chauffeur2}
                alt={t("service-pages.chauffeur-service.luxury-vehicles")}
                className="w-full h-64 object-cover"
                data-oid="h7dq4a9"
              />

              <div className="p-4 bg-black" data-oid="ra.z.n0">
                <h4
                  className="text-[#D4AF37] font-semibold text-[18px]"
                  style={{
                    direction: dir,
                    textAlign: dir === "rtl" ? "right" : "left",
                  }}
                  data-oid="axpfeb4"
                >
                  {t("service-pages.chauffeur-service.luxury-vehicles")}
                </h4>
                <p
                  className="text-white text-[14px] mt-2"
                  style={{
                    direction: dir,
                    textAlign: dir === "rtl" ? "right" : "left",
                  }}
                  data-oid="4-qd2ci"
                >
                  {t("service-pages.chauffeur-service.luxury-vehicles-desc")}
                </p>
              </div>
            </motion.div>

            <motion.div
              variants={fadeIn("left", "spring", 0.5, 0.75)}
              className="rounded-lg overflow-hidden shadow-lg border border-[#D4AF37]"
              data-oid="f:0tm3n"
            >
              <img
                src={chauffeur3}
                alt={t("service-pages.chauffeur-service.personalized-service")}
                className="w-full h-64 object-cover"
                data-oid="cerl3b0"
              />

              <div className="p-4 bg-black" data-oid="rb_m14x">
                <h4
                  className="text-[#D4AF37] font-semibold text-[18px]"
                  style={{
                    direction: dir,
                    textAlign: dir === "rtl" ? "right" : "left",
                  }}
                  data-oid="m0pkmrq"
                >
                  {t("service-pages.chauffeur-service.personalized-service")}
                </h4>
                <p
                  className="text-white text-[14px] mt-2"
                  style={{
                    direction: dir,
                    textAlign: dir === "rtl" ? "right" : "left",
                  }}
                  data-oid="nm1496f"
                >
                  {t(
                    "service-pages.chauffeur-service.personalized-service-desc",
                  )}
                </p>
              </div>
            </motion.div>
          </div>
        </div>

        <div
          className="mt-10 bg-black rounded-2xl p-8 border border-[#D4AF37]"
          data-oid="qp4hvht"
        >
          <h3
            className="text-[#D4AF37] font-bold text-[24px] text-center"
            style={{ direction: dir }}
            data-oid="0_j-7ei"
          >
            {t("service-pages.common.service-options")}
          </h3>
          <div
            className="mt-5 grid grid-cols-1 md:grid-cols-2 gap-10"
            data-oid="vfz473s"
          >
            <div
              className="bg-black p-5 rounded-2xl border border-[#D4AF37]"
              data-oid=":6idfqk"
            >
              <h4
                className="text-[#D4AF37] font-semibold text-[20px] text-center"
                style={{ direction: dir }}
                data-oid="f5xyaf2"
              >
                {t("service-pages.chauffeur-service.business-travel")}
              </h4>
              <p
                className="mt-2 text-white text-[16px] text-center"
                style={{ direction: dir }}
                data-oid="jwhh:f2"
              >
                {t("service-pages.chauffeur-service.business-travel-desc")}
              </p>
            </div>
            <div
              className="bg-black p-5 rounded-2xl border border-[#D4AF37]"
              data-oid="qj4z5hd"
            >
              <h4
                className="text-[#D4AF37] font-semibold text-[20px] text-center"
                style={{ direction: dir }}
                data-oid="j3:_l_g"
              >
                {t("service-pages.chauffeur-service.special-occasions")}
              </h4>
              <p
                className="mt-2 text-white text-[16px] text-center"
                style={{ direction: dir }}
                data-oid="douqa9t"
              >
                {t("service-pages.chauffeur-service.special-occasions-desc")}
              </p>
            </div>
            <div
              className="bg-black p-5 rounded-2xl border border-[#D4AF37]"
              data-oid="nt-tm4o"
            >
              <h4
                className="text-[#D4AF37] font-semibold text-[20px] text-center"
                style={{ direction: dir }}
                data-oid="ufgn.v9"
              >
                {t("service-pages.chauffeur-service.city-tours")}
              </h4>
              <p
                className="mt-2 text-white text-[16px] text-center"
                style={{ direction: dir }}
                data-oid="sk8ke4n"
              >
                {t("service-pages.chauffeur-service.city-tours-desc")}
              </p>
            </div>
            <div
              className="bg-black p-5 rounded-2xl border border-[#D4AF37]"
              data-oid="vg8pyw3"
            >
              <h4
                className="text-[#D4AF37] font-semibold text-[20px] text-center"
                style={{ direction: dir }}
                data-oid="r:-tjpq"
              >
                {t("service-pages.chauffeur-service.vip-service")}
              </h4>
              <p
                className="mt-2 text-white text-[16px] text-center"
                style={{ direction: dir }}
                data-oid=":nl0fm3"
              >
                {t("service-pages.chauffeur-service.vip-service-desc")}
              </p>
            </div>
          </div>
        </div>

        <div className="mt-10 flex justify-center" data-oid="5k.ipm3">
          <Link
            to="/contact"
            className="bg-black py-3 px-8 rounded-xl outline-none w-fit text-[#D4AF37] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
            data-oid="moc_nlq"
          >
            {t("service-pages.common.book-now")}
          </Link>
        </div>
      </div>
      <StarsCanvas data-oid="h4-l:x9" />
    </div>
  );
};

export default ChauffeurService;
