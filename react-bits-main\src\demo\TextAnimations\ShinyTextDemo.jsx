import { useState } from "react";
import { Box } from "@chakra-ui/react";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";

import CodeExample from "../../components/code/CodeExample";
import PropTable from "../../components/common/PropTable";
import CliInstallation from "../../components/code/CliInstallation";
import PreviewSlider from "../../components/common/PreviewSlider";
import Customize from "../../components/common/Customize";

import ShinyText from "../../content/TextAnimations/ShinyText/ShinyText";
import { shinyText } from "../../constants/code/TextAnimations/shinyTextCode";

const ShinyTextDemo = () => {
  const [speed, setSpeed] = useState(3);

  const propData = [
    {
      name: "text",
      type: "string",
      default: "-",
      description: "The text to be displayed with the shiny effect.",
    },
    {
      name: "disabled",
      type: "boolean",
      default: "false",
      description: "Disables the shiny effect when set to true.",
    },
    {
      name: "speed",
      type: "number",
      default: "5",
      description: "Specifies the duration of the animation in seconds.",
    },
    {
      name: "className",
      type: "string",
      default: "''",
      description: "Adds custom classes to the root element.",
    },
  ];

  return (
    <TabbedLayout data-oid="x9z8c::">
      <PreviewTab data-oid="mz93695">
        <h2 className="demo-title-extra" data-oid="2ge-5rp">
          Button Example
        </h2>
        <Box
          position="relative"
          className="demo-container"
          minH={150}
          data-oid="6v0nvl."
        >
          <div className="shiny-button" data-oid="l3xx0s6">
            <ShinyText
              text="Shiny Button"
              disabled={false}
              speed={3}
              className="shiny-text-demo"
              data-oid=":.z3b-7"
            />
          </div>
        </Box>

        <h2 className="demo-title-extra" data-oid="_j4cebr">
          Text Example
        </h2>
        <Box
          position="relative"
          className="demo-container"
          minH={150}
          data-oid="2q.q7nw"
        >
          <ShinyText
            text="Just some shiny text!"
            disabled={false}
            speed={3}
            className="shiny-text-demo"
            data-oid="ctqblyd"
          />
        </Box>

        <h2 className="demo-title-extra" data-oid="9.6bxwn">
          Configurable Speed
        </h2>
        <Box
          position="relative"
          className="demo-container"
          minH={150}
          data-oid="50g86c3"
        >
          <ShinyText
            text={speed < 2.5 ? "🐎 This is fast!" : "🐌 This is slow!"}
            disabled={false}
            speed={speed}
            className="shiny-text-demo"
            data-oid="6tq_80_"
          />
        </Box>

        <Customize data-oid="j60dq:k">
          <PreviewSlider
            title="Animation Speed"
            min={1}
            max={5}
            step={0.1}
            value={speed}
            valueUnit="s"
            onChange={setSpeed}
            data-oid="rd6ntiw"
          />
        </Customize>

        <PropTable data={propData} data-oid="-bb5ehf" />
      </PreviewTab>

      <CodeTab data-oid=".mh2zg-">
        <CodeExample codeObject={shinyText} data-oid="d.sqgn:" />
      </CodeTab>

      <CliTab data-oid="5m5h2g:">
        <CliInstallation {...shinyText} data-oid=".q9hkft" />
      </CliTab>
    </TabbedLayout>
  );
};

export default ShinyTextDemo;
