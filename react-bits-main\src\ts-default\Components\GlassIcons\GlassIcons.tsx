import React from "react";
import "./GlassIcons.css";

export interface GlassIconsItem {
  icon: React.ReactElement;
  color: string;
  label: string;
  customClass?: string;
}

export interface GlassIconsProps {
  items: GlassIconsItem[];
  className?: string;
}

const gradientMapping: Record<string, string> = {
  blue: "linear-gradient(hsl(223, 90%, 50%), hsl(208, 90%, 50%))",
  purple: "linear-gradient(hsl(283, 90%, 50%), hsl(268, 90%, 50%))",
  red: "linear-gradient(hsl(3, 90%, 50%), hsl(348, 90%, 50%))",
  indigo: "linear-gradient(hsl(253, 90%, 50%), hsl(238, 90%, 50%))",
  orange: "linear-gradient(hsl(43, 90%, 50%), hsl(28, 90%, 50%))",
  green: "linear-gradient(hsl(123, 90%, 40%), hsl(108, 90%, 40%))",
};

const GlassIcons: React.FC<GlassIconsProps> = ({ items, className }) => {
  const getBackgroundStyle = (color: string): React.CSSProperties => {
    if (gradientMapping[color]) {
      return { background: gradientMapping[color] };
    }
    return { background: color };
  };

  return (
    <div className={`icon-btns ${className || ""}`} data-oid="cxtn4cb">
      {items.map((item, index) => (
        <button
          key={index}
          type="button"
          className={`icon-btn ${item.customClass || ""}`}
          aria-label={item.label}
          data-oid="5am_cke"
        >
          <span
            className="icon-btn__back"
            style={getBackgroundStyle(item.color)}
            data-oid="zo71zir"
          ></span>
          <span className="icon-btn__front" data-oid="3e3-ghn">
            <span
              className="icon-btn__icon"
              aria-hidden="true"
              data-oid="ut.0uqi"
            >
              {item.icon}
            </span>
          </span>
          <span className="icon-btn__label" data-oid="b-jii:k">
            {item.label}
          </span>
        </button>
      ))}
    </div>
  );
};

export default GlassIcons;
