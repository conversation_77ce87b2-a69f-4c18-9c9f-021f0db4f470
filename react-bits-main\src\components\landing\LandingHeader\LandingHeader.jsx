import { Flex, Text } from "@chakra-ui/react";
import { useMediaQuery } from "react-haiku";
import { Link, useLocation } from "react-router-dom";

import reactbitslogo from "../../../assets/logos/reactbits-logo.svg";
import github from "../../../assets/common/icon-github.svg";
import showcase from "../../../assets/common/icon-showcase.svg";
import docs from "../../../assets/common/icon-docs.svg";

import FadeContent from "../../../content/Animations/FadeContent/FadeContent";

import "./LandingHeader.css";

const LandingHeader = () => {
  const isMobile = useMediaQuery("(max-width: 1024px)");
  const { pathname } = useLocation();

  return (
    <header className="app-header" data-oid="-x0u.6i">
      <nav className="header-content" data-oid="zizzgr2">
        <FadeContent blur data-oid="__r2twu">
          <Link className="logo" to="/" data-oid="utyhnfw">
            <img
              src={reactbitslogo}
              alt="The shape of a 3 point atom, representing a fraction of ReactJS"
              data-oid="zt0.5q3"
            />
          </Link>
        </FadeContent>

        <Flex gap="8px" className="menu-items" data-oid="6dyz229">
          {!isMobile && (
            <FadeContent blur data-oid="t9jtfxq">
              <Text
                as="a"
                fontWeight={500}
                fontSize="16px"
                href="https://github.com/DavidHDev/react-bits"
                target="_blank"
                rel="noopener noreferrer"
                data-oid=".2ayda6"
              >
                <img
                  src={github}
                  className="link-github"
                  alt="minimal github octocat logo"
                  data-oid="_87y84u"
                />{" "}
                GitHub
              </Text>
            </FadeContent>
          )}

          <FadeContent blur data-oid="_kal9ej">
            <Text
              as={Link}
              fontWeight={500}
              fontSize="16px"
              to={
                pathname !== "/showcase"
                  ? "/showcase"
                  : "/text-animations/split-text"
              }
              data-oid="p:zvxqn"
            >
              {pathname !== "/showcase" && (
                <>
                  <img src={showcase} alt="gallery" data-oid="d-r._s0" />{" "}
                  Showcase
                </>
              )}
              {pathname === "/showcase" && (
                <>
                  <img src={docs} alt="gallery" data-oid="hbdvpd3" /> Docs
                </>
              )}
            </Text>
          </FadeContent>

          {!isMobile && pathname !== "/showcase" && (
            <FadeContent blur data-oid="v58f:s3">
              <Text
                as={Link}
                fontWeight={500}
                fontSize="16px"
                to="/text-animations/split-text"
                data-oid="ng-26_h"
              >
                <img
                  src={docs}
                  alt="a page with some writing on it"
                  data-oid="2u4vtf."
                />{" "}
                Docs
              </Text>
            </FadeContent>
          )}
        </Flex>
      </nav>
    </header>
  );
};

export default LandingHeader;
