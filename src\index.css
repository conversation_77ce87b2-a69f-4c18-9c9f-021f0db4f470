@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import './styles/rtl-timeline.css';
@import './styles/section-styles.css';
@import './styles/black-gold-background.css';
@import './styles/carpool-page.css';
@import './styles/tourism-page.css';
@import './styles/mobile-optimizations.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  color-scheme: dark;
}

html[lang="en"] * {
  font-family: "Montserrat", sans-serif;
}

html[lang="ar"] * {
  font-family: "Cairo", sans-serif;
}

/* RTL specific styles */
html[dir="rtl"] .dropdown-menu {
  left: auto;
  right: 0;
}

html[dir="rtl"] .mobile-dropdown-toggle {
  flex-direction: row-reverse;
}

html[dir="rtl"] .dropdown-arrow {
  margin-right: 0.25rem;
  margin-left: 0;
}

/* Adjust padding for RTL */
html[dir="rtl"] .section-padding {
  padding-left: 0;
  padding-right: 3rem;
}

/* RTL Timeline Styles moved to src/styles/rtl-timeline.css */

html {
  scroll-behavior: smooth;
  --scroll-duration: 0.3s; /* Even faster scroll duration */
}

.hash-span {
  margin-top: -100px;
  padding-bottom: 100px;
  display: block;
}

.gradient {
  background: #000000; /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #434343, #000000);
  background: linear-gradient(to right, #434343, #000000);
}

.gold-gradient {
  background: #d4af37;
  background: linear-gradient(-90deg, #d4af37 0%, rgba(184, 134, 11, 0) 100%);
  background: -webkit-linear-gradient(-90deg, #d4af37 0%, rgba(184, 134, 11, 0) 100%);
}

.black-gold-gradient {
  background: #0a0a0a;
  background: linear-gradient(90.13deg, #0a0a0a 1.9%, #d4af37 97.5%);
  background: -webkit-linear-gradient(90.13deg, #0a0a0a 1.9%, #d4af37 97.5%);
}

.gold-text-gradient {
  background: linear-gradient(to top, #b8860b, #f5d76e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gold {
  color: #D4AF37 !important;
}

.orange-text-gradient,
.green-text-gradient,
.blue-text-gradient,
.pink-text-gradient {
  background: linear-gradient(to top, #f12711, #f5af19);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.translucent-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* Adjust the opacity by modifying the last value (0.5 in this case) */
  z-index: 9999; /* Adjust the z-index to ensure it's displayed above other elements if necessary */
}

@keyframes spin {
  0%, 100% {
    /* Box shadow styles */
  }
  12.5% {
    /* Box shadow styles */
  }
  25% {
    /* Box shadow styles */
  }
  37.5% {
    /* Box shadow styles */
  }
  50% {
    /* Box shadow styles */
  }
  62.5% {
    /* Box shadow styles */
  }
  75% {
    /* Box shadow styles */
  }
  87.5% {
    /* Box shadow styles */
  }
}

.canvas-loader {
  font-size: 10px;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  position: relative;
  text-indent: -9999em;
  animation: spin 0.7s infinite ease; /* Faster animation (was 1.1s) */
  transform: translateZ(0);
}

/* Star animations removed - replaced with Hyperspeed effect */

/* CSS Fallback for Car Canvas */
.car-fallback-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(to bottom, #000000, #111111);
}

/* Ensure the background is black in fullscreen mode */
:fullscreen, ::backdrop {
  background-color: #000000;
}

/* Additional fix for fullscreen background */
html, body {
  background-color: #000000;
  min-height: 100vh;
  color: #ffffff;
  position: relative;
  overflow-x: hidden;
}

/* Clean body background - particle effects replaced with Hyperspeed */
body {
  background: #000000;
}

.car-fallback {
  position: relative;
  width: 200px;
  height: 80px;
  animation: car-float 3s ease-in-out infinite;
}

@keyframes car-float {
  0%, 100% { transform: translateY(0px) rotate(1deg); }
  50% { transform: translateY(-10px) rotate(-1deg); }
}

.car-body {
  position: absolute;
  width: 180px;
  height: 30px;
  background: #333;
  bottom: 15px;
  left: 10px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.car-roof {
  position: absolute;
  width: 100px;
  height: 25px;
  background: #222;
  bottom: 45px;
  left: 50px;
  border-radius: 10px 10px 0 0;
}

.car-window {
  position: absolute;
  width: 40px;
  height: 15px;
  background: #aaddff;
  bottom: 45px;
  left: 80px;
  border-radius: 5px;
  opacity: 0.7;
}

.car-wheel {
  position: absolute;
  width: 25px;
  height: 25px;
  background: #111;
  border-radius: 50%;
  bottom: 0;
  border: 3px solid #444;
  animation: wheel-spin 2s linear infinite;
}

@keyframes wheel-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.car-wheel-left {
  left: 30px;
}

.car-wheel-right {
  right: 30px;
}

.car-light {
  position: absolute;
  width: 10px;
  height: 5px;
  bottom: 25px;
  border-radius: 3px;
}

.car-light-front {
  right: 0;
  background: #fff;
  box-shadow: 0 0 10px #fff;
}

.car-light-back {
  left: 0;
  background: #f00;
  box-shadow: 0 0 10px #f00;
}

/* Section transition styles */
.section-transition {
  position: relative;
  background-color: transparent;
  z-index: 1;
}

.section-transition::before {
  content: '';
  position: absolute;
  top: -50px;
  left: 0;
  width: 100%;
  height: 50px;
  background: linear-gradient(to bottom, transparent, #000000);
  z-index: 5;
  pointer-events: none;
}



/* global.css */
.input-field {
  display: flex;
  flex-direction: column;
}

.input-label {
  color: #fff;
  font-weight: 600;
  margin-bottom: 16px;
}

.input {
  background: #333; /* Background color */
  padding: 16px 24px;
  color: #fff;
  border: none;
  border-radius: 8px;
  outline: none;
  font-weight: 600;
  placeholder-color: #999; /* Placeholder text color */
}

.input-error {
  color: #ff0000;
}

.submit-button {
  background: #c0c0c0; /* Background color */
  padding: 12px 24px;
  border-radius: 16px;
  outline: none;
  color: #fff;
  font-weight: 600;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2); /* Box shadow for the button */
}

.confirmation {
  color: #00ff00;
}

/* Modern Text Animation Styles */
.text-animation-container {
  position: relative;
  display: inline-block;
  min-width: 200px;
  min-height: 40px;
}

.modern-text-animation {
  display: inline-block;
  position: relative;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.4);
}

/* Add a subtle underline effect */
.modern-text-animation::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -3px;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #D4AF37, transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Add a subtle glow effect to the text */
.modern-text-animation {
  animation: textGlow 3s infinite alternate;
}

@keyframes textGlow {
  0% { text-shadow: 0 0 5px rgba(212, 175, 55, 0.3); }
  100% { text-shadow: 0 0 15px rgba(212, 175, 55, 0.7); }
}

/* Chat bot animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Message animation */
@keyframes slideIn {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Gold border pulse animation */
@keyframes borderPulse {
  0% { border-color: rgba(212, 175, 55, 0.5); }
  50% { border-color: rgba(212, 175, 55, 1); }
  100% { border-color: rgba(212, 175, 55, 0.5); }
}

/* Typing indicator animation - more subtle than bounce */
@keyframes typing {
  0% { transform: translateY(0px); opacity: 0.3; }
  50% { transform: translateY(-2px); opacity: 1; }
  100% { transform: translateY(0px); opacity: 0.3; }
}

.animate-typing {
  animation: typing 1s infinite ease-in-out;
}

/* AI connection indicator animation */
@keyframes aiGlow {
  0% { filter: drop-shadow(0 0 2px rgba(212, 175, 55, 0.5)); }
  50% { filter: drop-shadow(0 0 8px rgba(212, 175, 55, 1)); }
  100% { filter: drop-shadow(0 0 2px rgba(212, 175, 55, 0.5)); }
}

.ai-connected {
  animation: aiGlow 2s infinite ease-in-out;
  filter: drop-shadow(0 0 5px rgba(212, 175, 55, 0.8));
}
