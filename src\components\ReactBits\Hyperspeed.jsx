import React, { useEffect, useRef, useState } from "react";
import * as THREE from "three";
import "./Hyperspeed.css";

// Hyperspeed presets for chauffeur service theme
const chauffeurPresets = {
  luxury: {
    distortion: "turbulentDistortion",
    length: 400,
    roadWidth: 10,
    islandWidth: 2,
    lanesPerRoad: 3,
    fov: 90,
    fovSpeedUp: 150,
    speedUp: 1.5,
    carLightsFade: 0.6,
    totalSideLightSticks: 25,
    lightPairsPerRoadWay: 50,
    shoulderLinesWidthPercentage: 0.05,
    brokenLinesWidthPercentage: 0.1,
    brokenLinesLengthPercentage: 0.5,
    lightStickWidth: [0.12, 0.5],
    lightStickHeight: [1.3, 1.7],
    movingAwaySpeed: [40, 60],
    movingCloserSpeed: [-80, -120],
    carLightsLength: [400 * 0.03, 400 * 0.2],
    carLightsRadius: [0.05, 0.14],
    carWidthPercentage: [0.3, 0.5],
    carShiftX: [-0.8, 0.8],
    carFloorSeparation: [0, 5],
    colors: {
      roadColor: 0x080808,
      islandColor: 0x0a0a0a,
      background: 0x000000,
      shoulderLines: 0xd4af37, // Gold for luxury
      brokenLines: 0xffffff,
      leftCars: [0xff0000, 0xff3333, 0xff6666],
      rightCars: [0xffffff, 0xf0f0f0, 0xe0e0e0],
      sticks: 0xd4af37, // Gold accent
    },
  },
};

// Distortion effects
const distortions = {
  turbulentDistortion: {
    uniforms: {
      uDistortionX: { value: 2 },
      uDistortionY: { value: 2 },
      uSpeedDistortion: { value: 0.1 },
    },
    getDistortion: `
      uniform float uDistortionX;
      uniform float uDistortionY; 
      uniform float uSpeedDistortion;
      
      float nsin(float val) {
        return sin(val) * 0.5 + 0.5;
      }
      
      vec3 getDistortion(float progress) {
        progress = clamp(progress, 0., 1.);
        float xAmp = uDistortionX;
        float yAmp = uDistortionY;
        float speed = uSpeedDistortion;
        return vec3( 
          xAmp * nsin(progress * PI * 4. + uTime * speed + position.x + position.z),
          yAmp * nsin(progress * PI * 3. + uTime * speed + position.y + position.z),
          0.
        );
      }
    `,
  },
};

// Utility functions
function lerp(current, target, speed = 0.1, limit = 0.001) {
  let change = (target - current) * speed;
  if (Math.abs(change) < limit) {
    change = target - current;
  }
  return change;
}

// Car Lights Class
class CarLights {
  constructor(webgl, options, colors, speed, fade) {
    this.webgl = webgl;
    this.options = options;
    this.colors = colors;
    this.speed = speed;
    this.fade = fade;

    this.init();
  }

  init() {
    const options = this.options;

    // Create instanced geometry
    const geometry = new THREE.PlaneGeometry(1, 1);
    const instanced = new THREE.InstancedBufferGeometry().copy(geometry);
    instanced.instanceCount = options.lightPairsPerRoadWay;

    // Create attributes
    const aOffset = [];
    const aMetrics = [];
    const aColor = [];

    for (let i = 0; i < options.lightPairsPerRoadWay; i++) {
      const radius = Math.random() * 0.1 + 0.1;
      const length =
        Math.random() * options.length * 0.08 + options.length * 0.02;
      const speed = Math.random() * this.speed[1] + this.speed[0];

      aOffset.push(
        (Math.random() - 0.5) * options.roadWidth,
        Math.random() * options.length,
        Math.random() * options.length,
      );

      aMetrics.push(radius, length, speed);

      const color = this.colors[Math.floor(Math.random() * this.colors.length)];
      aColor.push(
        ((color & 0xff0000) >> 16) / 255,
        ((color & 0x00ff00) >> 8) / 255,
        (color & 0x0000ff) / 255,
      );
    }

    instanced.setAttribute(
      "aOffset",
      new THREE.InstancedBufferAttribute(new Float32Array(aOffset), 3),
    );
    instanced.setAttribute(
      "aMetrics",
      new THREE.InstancedBufferAttribute(new Float32Array(aMetrics), 3),
    );
    instanced.setAttribute(
      "aColor",
      new THREE.InstancedBufferAttribute(new Float32Array(aColor), 3),
    );

    // Create material
    const material = new THREE.ShaderMaterial({
      fragmentShader: this.getFragmentShader(),
      vertexShader: this.getVertexShader(),
      transparent: true,
      uniforms: {
        uTime: { value: 0 },
        uTravelLength: { value: options.length },
        uFade: { value: this.fade },
      },
    });

    this.mesh = new THREE.Mesh(instanced, material);
    this.mesh.frustumCulled = false;
    this.webgl.scene.add(this.mesh);
  }

  update(time) {
    this.mesh.material.uniforms.uTime.value = time;
  }

  getVertexShader() {
    return `
      attribute vec3 aOffset;
      attribute vec3 aMetrics;
      attribute vec3 aColor;
      uniform float uTravelLength;
      uniform float uTime;
      varying vec2 vUv; 
      varying vec3 vColor; 
      
      void main() {
        vec3 transformed = position.xyz;
        float radius = aMetrics.r;
        float myLength = aMetrics.g;
        float speed = aMetrics.b;

        transformed.xy *= radius;
        transformed.z *= myLength;

        transformed.z += myLength - mod(uTime * speed + aOffset.z, uTravelLength);
        transformed.xy += aOffset.xy;

        vec4 mvPosition = modelViewMatrix * vec4(transformed, 1.0);
        gl_Position = projectionMatrix * mvPosition;
        
        vUv = uv;
        vColor = aColor;
      }
    `;
  }

  getFragmentShader() {
    return `
      uniform float uFade;
      varying vec2 vUv;
      varying vec3 vColor;
      
      void main() {
        vec3 color = vColor;
        float alpha = (1. - length(vUv - 0.5) * 2.) * uFade;
        gl_FragColor = vec4(color, alpha);
      }
    `;
  }
}

// Road Class
class Road {
  constructor(webgl, options) {
    this.webgl = webgl;
    this.options = options;
    this.init();
  }

  init() {
    const options = this.options;
    const geometry = new THREE.PlaneGeometry(options.roadWidth, options.length);
    const material = new THREE.MeshBasicMaterial({
      color: options.colors.roadColor,
      transparent: true,
      opacity: 0.9,
    });

    this.mesh = new THREE.Mesh(geometry, material);
    this.mesh.rotation.x = -Math.PI / 2;
    this.mesh.position.z = -options.length / 2;
    this.webgl.scene.add(this.mesh);
  }

  update(time) {
    // Road doesn't need updates for basic version
  }
}

// Main Hyperspeed App Class
class HyperspeedApp {
  constructor(container, options) {
    this.container = container;
    this.options = { ...chauffeurPresets.luxury, ...options };
    this.clock = new THREE.Clock();
    this.speedUp = 0;
    this.speedUpTarget = 0;
    this.timeOffset = 0;
    this.fovTarget = this.options.fov;

    this.init();
  }

  init() {
    this.initScene();
    this.initCamera();
    this.initRenderer();
    this.initLights();
    this.initRoad();
    this.initCarLights();
    this.initEventListeners();
    this.animate();
  }

  initScene() {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(this.options.colors.background);
    this.scene.fog = new THREE.Fog(
      this.options.colors.background,
      0,
      this.options.length,
    );
  }

  initCamera() {
    this.camera = new THREE.PerspectiveCamera(
      this.options.fov,
      this.container.clientWidth / this.container.clientHeight,
      0.1,
      1000,
    );
    this.camera.position.set(0, 8, 0);
    this.camera.lookAt(0, 0, -this.options.length);
  }

  initRenderer() {
    try {
      this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
      this.renderer.setSize(
        this.container.clientWidth,
        this.container.clientHeight,
      );
      this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
      this.container.appendChild(this.renderer.domElement);
    } catch (error) {
      console.warn("WebGL not supported, falling back to CSS background");
      throw new Error("WebGL context creation failed");
    }
  }

  initLights() {
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    this.scene.add(ambientLight);
  }

  initRoad() {
    this.road = new Road(this, this.options);
  }

  initCarLights() {
    this.leftCarLights = new CarLights(
      this,
      this.options,
      this.options.colors.leftCars,
      this.options.movingCloserSpeed,
      this.options.carLightsFade,
    );

    this.rightCarLights = new CarLights(
      this,
      this.options,
      this.options.colors.rightCars,
      this.options.movingAwaySpeed,
      this.options.carLightsFade,
    );
  }

  initEventListeners() {
    // Speed up on click/touch
    this.container.addEventListener("click", () => this.speedUp());
    this.container.addEventListener("touchstart", () => this.speedUp());

    // Handle resize
    window.addEventListener("resize", () => this.onResize());
  }

  speedUp() {
    this.speedUpTarget = this.options.speedUp;
    this.fovTarget = this.options.fovSpeedUp;

    setTimeout(() => {
      this.speedUpTarget = 0;
      this.fovTarget = this.options.fov;
    }, 2000);
  }

  onResize() {
    this.camera.aspect =
      this.container.clientWidth / this.container.clientHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(
      this.container.clientWidth,
      this.container.clientHeight,
    );
  }

  animate() {
    requestAnimationFrame(() => this.animate());

    const delta = this.clock.getDelta();
    this.update(delta);
    this.render();
  }

  update(delta) {
    const lerpPercentage = Math.exp(-(-60 * Math.log2(1 - 0.1)) * delta);
    this.speedUp += lerp(
      this.speedUp,
      this.speedUpTarget,
      lerpPercentage,
      0.00001,
    );
    this.timeOffset += this.speedUp * delta;
    const time = this.clock.elapsedTime + this.timeOffset;

    this.rightCarLights.update(time);
    this.leftCarLights.update(time);
    this.road.update(time);

    // Update camera FOV
    const fovChange = lerp(this.camera.fov, this.fovTarget, lerpPercentage);
    if (fovChange !== 0) {
      this.camera.fov += fovChange * delta * 6;
      this.camera.updateProjectionMatrix();
    }
  }

  render() {
    this.renderer.render(this.scene, this.camera);
  }
}

const Hyperspeed = ({ effectOptions = {} }) => {
  const containerRef = useRef(null);
  const appRef = useRef(null);
  const [hasWebGL, setHasWebGL] = useState(true);

  useEffect(() => {
    if (!containerRef.current) return;

    try {
      const mergedOptions = { ...chauffeurPresets.luxury, ...effectOptions };
      appRef.current = new HyperspeedApp(containerRef.current, mergedOptions);
    } catch (error) {
      console.warn("WebGL initialization failed:", error);
      setHasWebGL(false);
    }

    return () => {
      if (appRef.current && appRef.current.renderer) {
        appRef.current.renderer.dispose();
        if (containerRef.current && appRef.current.renderer.domElement) {
          containerRef.current.removeChild(appRef.current.renderer.domElement);
        }
      }
    };
  }, [effectOptions]);

  // Fallback CSS background when WebGL is not available
  if (!hasWebGL) {
    return (
      <div
        className="hyperspeed-fallback"
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          background:
            "linear-gradient(180deg, #000000 0%, #1a1a1a 50%, #000000 100%)",
          zIndex: -1,
        }}
        data-oid="fallback-bg"
      />
    );
  }

  return (
    <div
      ref={containerRef}
      className="hyperspeed-container"
      data-oid="z_s1hnd"
    />
  );
};

export default Hyperspeed;
