import { useNavigate } from "react-router-dom";

import Magnet from "../../../content/Animations/Magnet/Magnet";
import AnimatedContent from "../../../content/Animations/AnimatedContent/AnimatedContent";
import Squares from "../../../content/Backgrounds/Squares/Squares";
import Waves from "../../../content/Backgrounds/Waves/Waves";
import LetterGlitch from "../../../content/Backgrounds/LetterGlitch/LetterGlitch";

import arrow from "../../../assets/common/icon-arrow.svg";
import "./HeroShowcase.css";
import MetaBalls from "../../../content/Animations/MetaBalls/MetaBalls";

const HeroShowcase = () => {
  const navigate = useNavigate();

  return (
    <nav className="component-nav-container" data-oid="h5boppb">
      <AnimatedContent reverse initialOpacity={0} data-oid="_auv2h7">
        <div
          className="circle feat-1"
          onClick={() => navigate("/animations/meta-balls")}
          data-oid="fp.ii0f"
        >
          <MetaBalls
            color="#ff87b2"
            cursorBallColor="#ff87b2"
            data-oid="046q0f6"
          />
        </div>
      </AnimatedContent>
      <AnimatedContent reverse initialOpacity={0} data-oid="hqmg3mv">
        <div
          className="square feat-2"
          onClick={() => navigate("/backgrounds/waves")}
          data-oid="ev21sq3"
        >
          <Waves lineColor="#ff9346" xGap={8} yGap={8} data-oid="0u4ocr6" />
        </div>
      </AnimatedContent>
      <AnimatedContent reverse initialOpacity={0} data-oid="4ncnx::">
        <div
          className="circle link"
          onClick={() => navigate("/text-animations/split-text")}
          data-oid="6b:zpaq"
        >
          <Magnet padding={25} data-oid="7g4c3i1">
            <div className="docs-link" data-oid="hv:9.ie">
              <img
                src={arrow}
                alt="arrow pointing diagonally to the upper right corner"
                data-oid="8.dvmiq"
              />

              <p data-oid="ghaj42g">Browse Docs</p>
            </div>
          </Magnet>
        </div>
      </AnimatedContent>
      <AnimatedContent reverse initialOpacity={0} data-oid="gt_euyw">
        <div
          className="square feat-3"
          onClick={() => navigate("/backgrounds/letter-glitch")}
          data-oid="dmyr_lb"
        >
          <LetterGlitch
            glitchSpeed={10}
            centerVignette={false}
            outerVignette={true}
            smooth={true}
            data-oid="wh_-10a"
          />
        </div>
      </AnimatedContent>
      <AnimatedContent reverse initialOpacity={0} data-oid="wk3:8r8">
        <div
          className="circle feat-4"
          onClick={() => navigate("/backgrounds/squares")}
          data-oid="o91h8-a"
        >
          <Squares
            speed={0.2}
            borderColor="#ffee51"
            hoverFillColor="#ffee51"
            data-oid="q915pmi"
          />
        </div>
      </AnimatedContent>
    </nav>
  );
};

export default HeroShowcase;
