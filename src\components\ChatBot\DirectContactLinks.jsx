import React from "react";
import { Fa<PERSON>hat<PERSON>pp, FaEnvelope, FaPhone } from "react-icons/fa";
import "../../styles/directContactLinks.css";

const DirectContactLinks = ({ language = "en" }) => {
  // WhatsApp message
  const whatsappMessage = encodeURIComponent(
    language === "en"
      ? "Hello, I'm interested in your premium chauffeur services."
      : "مرحبًا، أنا مهتم بخدمات السائق الخاص المميزة التي تقدمونها.",
  );
  const whatsappLink = `https://wa.me/4917631454340?text=${whatsappMessage}`;

  // Email details
  const emailSubject = encodeURIComponent(
    language === "en"
      ? "Inquiry about Premium Chauffeur Services"
      : "استفسار حول خدمات السائق الخاص المميزة",
  );
  const emailBody = encodeURIComponent(
    language === "en"
      ? "Hello,\n\nI'm interested in your premium chauffeur services.\n\nBest regards,"
      : "مرحبًا،\n\nأنا مهتم بخدمات السائق الخاص المميزة التي تقدمونها.\n\nمع أطيب التحيات،",
  );
  const emailLink = `mailto:<EMAIL>?subject=${emailSubject}&body=${emailBody}`;

  // Phone link
  const phoneLink = "tel:+4917631454340";

  // Contact info
  const phoneNumber = "+49 176 3145 4340";
  const emailAddress = "<EMAIL>";

  return (
    <div className="direct-contact-links" data-oid="-hjkipo">
      <div className="contact-header" data-oid="-v2lolj">
        {language === "en" ? "Contact Options" : "خيارات الاتصال"}
      </div>

      <a
        href={phoneLink}
        className="contact-link phone-link"
        data-oid="kb-6t38"
      >
        <div className="contact-icon-wrapper" data-oid="-b_sk3-">
          <FaPhone className="contact-icon" data-oid="wqoexfj" />
        </div>
        <div className="contact-info" data-oid="e0kb1xh">
          <span className="contact-label" data-oid="poansfq">
            {language === "en" ? "Phone" : "هاتف"}
          </span>
          <span className="contact-value" data-oid="clps.-d">
            {phoneNumber}
          </span>
        </div>
      </a>

      <a
        href={whatsappLink}
        target="_blank"
        rel="noopener noreferrer"
        className="contact-link whatsapp-link"
        data-oid="72:e406"
      >
        <div className="contact-icon-wrapper whatsapp-icon" data-oid="cjql4ek">
          <FaWhatsapp className="contact-icon" data-oid="r_fy6j8" />
        </div>
        <div className="contact-info" data-oid="vzvas68">
          <span className="contact-label" data-oid="p8w3sg0">
            {language === "en" ? "WhatsApp" : "واتساب"}
          </span>
          <span className="contact-value" data-oid="3kksuex">
            {phoneNumber}
          </span>
        </div>
      </a>

      <a
        href={emailLink}
        className="contact-link email-link"
        data-oid="5kzf:u1"
      >
        <div className="contact-icon-wrapper email-icon" data-oid="filp_vz">
          <FaEnvelope className="contact-icon" data-oid="kw4kbx8" />
        </div>
        <div className="contact-info" data-oid="ktmrc1h">
          <span className="contact-label" data-oid="-lton6g">
            {language === "en" ? "Email" : "بريد إلكتروني"}
          </span>
          <span className="contact-value" data-oid="hdzw92d">
            {emailAddress}
          </span>
        </div>
      </a>
    </div>
  );
};

export default DirectContactLinks;
