.dot-grid {
  padding: 1em;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  position: relative;
}

.dot-grid__wrap {
  width: 100%;
  height: 100%;
  position: relative;
}

.dot-grid__container {
  position: absolute;
  inset: 0;
  display: flex;
  flex-wrap: wrap;
  gap: var(--dot-gap);
  pointer-events: none;
}

.dot-grid__dot {
  width: var(--dot-size);
  height: var(--dot-size);
  border-radius: 50%;
  will-change: transform, background-color;
  transform-origin: center;
}