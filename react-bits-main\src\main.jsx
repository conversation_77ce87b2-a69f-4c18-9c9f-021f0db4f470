import ReactDOM from "react-dom/client";
import App from "./App.jsx";
import "./styles.css";

import { HelmetProvider } from "react-helmet-async";

import { <PERSON>kraProvider } from "@chakra-ui/react";
import { customTheme } from "./utils/customTheme.js";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";

ReactDOM.createRoot(document.createElement("div")).render(
  // eslint-disable-next-line react/no-children-prop
  <SyntaxHighlighter language="" children={""} data-oid="hdm:xj7" />,
);

ReactDOM.createRoot(document.getElementById("root")).render(
  <ChakraProvider theme={customTheme} data-oid="4sqx:a3">
    <HelmetProvider data-oid="qxzr-hm">
      <App data-oid="z_isx_6" />
    </HelmetProvider>
  </ChakraProvider>,
);
