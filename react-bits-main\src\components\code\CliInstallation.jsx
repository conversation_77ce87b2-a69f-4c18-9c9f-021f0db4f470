import { Box, Divider, Text } from "@chakra-ui/react";
import {
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
} from "@chakra-ui/react";
import CodeHighlighter from "./CodeHighlighter";

const CliInstallation = (cliData) => {
  const installConfigs = [
    { title: "JS + CSS", code: cliData.cliDefault },
    { title: "JS + Tailwind", code: cliData.cliTailwind },
    { title: "TS + CSS", code: cliData.cliTsDefault },
    { title: "TS + Tailwind", code: cliData.cliTsTailwind },
  ].filter(({ code }) => !!code);

  return (
    <Box data-oid="ri:h9v2">
      <h2 className="demo-title" data-oid="loearxf">
        One-Time Installation
      </h2>
      {installConfigs.map(({ title, code }) => (
        <div key={title} data-oid="f-2rn--">
          <Text
            mb={0}
            fontWeight="600"
            fontSize="1.4rem"
            color="#a6a6a6"
            className="demo-title-extra"
            data-oid="j-r28c3"
          >
            {title}
          </Text>
          <CodeHighlighter
            language="bash"
            codeString={code || ""}
            data-oid="v0n-2gb"
          />
        </div>
      ))}

      <div className="cli-divider" data-oid="khs4118"></div>

      <h2 className="demo-title" data-oid="2_3gfxr">
        Full CLI Setup
      </h2>
      <Text
        className="jsrepo-info"
        mb={2}
        mt={4}
        color="#a1a1aa"
        data-oid="kvfdphq"
      >
        React Bits uses{" "}
        <a href="https://jsrepo.dev/" target="_blank" data-oid="iyncic_">
          jsrepo
        </a>{" "}
        to help you install components via CLI - it can be set up project-wide!
      </Text>

      <Accordion allowToggle data-oid="n.b_ipa">
        <AccordionItem
          border="1px solid #222"
          backgroundColor="#060606"
          borderRadius="20px"
          data-oid="hbg6s.m"
        >
          <AccordionButton borderTop="none" py={4} px={6} data-oid=":n9-f2z">
            <Box
              flex="1"
              m={0}
              textAlign="left"
              className="demo-title"
              fontSize="1rem"
              data-oid="vewd:1h"
            >
              Setup Steps
            </Box>
            <AccordionIcon data-oid="1_-h0ec" />
          </AccordionButton>
          <AccordionPanel px={6} pb={4} data-oid="lr_64ut">
            <p className="demo-extra-info" data-oid="v2o88ar">
              1. Initialize a config file for your project
            </p>

            {[
              { label: "JavaScript (Default)", path: "default" },
              { label: "JavaScript (Tailwind)", path: "tailwind" },
              { label: "TypeScript (Default)", path: "ts/default" },
              { label: "TypeScript (Tailwind)", path: "ts/tailwind" },
            ].map(({ label, path }) => (
              <div key={path} data-oid="p2f33kr">
                <p className="demo-extra-info" data-oid="_lbzfl8">
                  {label}
                </p>
                <CodeHighlighter
                  language="bash"
                  codeString={`npx jsrepo init https://reactbits.dev/${path}`}
                  data-oid="nxm912f"
                />
              </div>
            ))}

            <Divider my={8} data-oid="igtnrn0" />

            <p className="demo-extra-info" data-oid="bd9in7r">
              2. Browse & add components from the list
            </p>
            <CodeHighlighter
              language="bash"
              codeString={`npx jsrepo add`}
              data-oid="scq67il"
            />

            <p className="demo-extra-info" data-oid="ebf5cky">
              3. Or just add a specific component
            </p>
            <CodeHighlighter
              language="bash"
              codeString={`npx jsrepo add Animations/AnimatedContainer`}
              data-oid="wlb4kw7"
            />
          </AccordionPanel>
        </AccordionItem>
      </Accordion>
    </Box>
  );
};

export default CliInstallation;
