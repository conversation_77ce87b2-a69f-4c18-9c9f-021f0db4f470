import { Box, Flex } from "@chakra-ui/react";

const Dependencies = ({ dependencyList = [] }) => {
  return (
    <Box mt={12} data-oid="43gwhp6">
      <h2 className="demo-title-extra" data-oid="1214_uk">
        Dependencies
      </h2>
      <Flex wrap="wrap" className="demo-details" data-oid=".69h2mc">
        {dependencyList.map((d) => (
          <span key={d} data-oid="hz.hy9n">
            {d}
          </span>
        ))}
      </Flex>
    </Box>
  );
};

export default Dependencies;
