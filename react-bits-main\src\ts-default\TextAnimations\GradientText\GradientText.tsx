import "./GradientText.css";
import React, { ReactNode } from "react";

interface GradientTextProps {
  children: ReactNode;
  className?: string;
  colors?: string[];
  animationSpeed?: number;
  showBorder?: boolean;
}

export default function GradientText({
  children,
  className = "",
  colors = ["#40ffaa", "#4079ff", "#40ffaa", "#4079ff", "#40ffaa"], // Default colors
  animationSpeed = 8, // Default animation speed in seconds
  showBorder = false, // Default overlay visibility
}: GradientTextProps) {
  const gradientStyle = {
    backgroundImage: `linear-gradient(to right, ${colors.join(", ")})`,
    animationDuration: `${animationSpeed}s`,
  };

  return (
    <div className={`animated-gradient-text ${className}`} data-oid="3_lexe3">
      {showBorder && (
        <div
          className="gradient-overlay"
          style={gradientStyle}
          data-oid="rm-3z34"
        ></div>
      )}
      <div className="text-content" style={gradientStyle} data-oid="0tq3c3i">
        {children}
      </div>
    </div>
  );
}
