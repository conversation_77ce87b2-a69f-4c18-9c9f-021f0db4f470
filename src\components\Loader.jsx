import { Html, useProgress } from "@react-three/drei";

const CanvasLoader = () => {
  const { progress } = useProgress();

  const containerStyle = {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "column",
  };

  const textStyle = {
    fontSize: 14,
    color: "#F1F1F1",
    fontWeight: 800,
    marginTop: 40,
  };

  return (
    <Html as="div" center style={containerStyle} data-oid="777rnm4">
      <span className="canvas-loader" data-oid="p3nx3z6" />
      <p style={textStyle} data-oid="dm9vvb.">
        {progress.toFixed(2)}%
      </p>
    </Html>
  );
};

export default CanvasLoader;
