import React from "react";
import CityTemplate from "./CityTemplate";
import { hamburg1 } from "../../../assets";

const BadenBaden = () => {
  // City images - using placeholder images since we don't have specific Baden-Baden images
  const cityImages = [
    hamburg1, // Using as placeholder
    "https://images.unsplash.com/photo-1559592413-7cec4d0cae2b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1632&q=80",
    "https://images.unsplash.com/photo-1528728329032-2972f65dfb3f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
  ];

  // Top attractions
  const attractions = [
    {
      name: "Caracalla Spa",
      description:
        "Modern thermal bath complex with indoor and outdoor pools, saunas, and wellness facilities.",
    },
    {
      name: "Friedrichsbad",
      description:
        "Historic Roman-Irish bath dating from 1877, offering a traditional 17-step bathing ritual.",
    },
    {
      name: "Lichtentaler Allee",
      description:
        "Beautiful 2.3 km park and arboretum along the River Oos with over 300 types of trees and plants.",
    },
    {
      name: "Casino Baden-Baden",
      description:
        "Elegant 19th-century casino described by Marlene Dietrich as 'the most beautiful casino in the world'.",
    },
    {
      name: "Museum Frieder Burda",
      description:
        "Contemporary art museum designed by Richard Meier, housing an impressive collection of modern art.",
    },
  ];

  // Recommended restaurants
  const restaurants = [
    {
      name: "Brenners Park-Restaurant",
      description:
        "Fine dining restaurant in the luxury Brenners Park Hotel with elegant atmosphere and creative cuisine.",
    },
    {
      name: "Le Jardin de France",
      description:
        "Michelin-starred restaurant offering exquisite French cuisine in an intimate setting.",
    },
    {
      name: "Rizzi WineBistro & Restaurant",
      description:
        "Stylish restaurant with terrace seating, serving Mediterranean-inspired dishes and fine wines.",
    },
    {
      name: "Weinstube Baldreit",
      description:
        "Traditional wine tavern in the Old Town serving regional Baden specialties and local wines.",
    },
  ];

  // Shopping areas
  const shopping = [
    {
      name: "Kurhaus Colonnades",
      description:
        "Elegant shopping arcade with luxury boutiques, jewelry stores, and art galleries.",
    },
    {
      name: "Sophienstrasse",
      description:
        "Charming shopping street in the Old Town with fashion boutiques, specialty shops, and cafés.",
    },
    {
      name: "Geroldsauer Mühle",
      description:
        "Traditional Black Forest mill with a gourmet market selling regional specialties and crafts.",
    },
    {
      name: "Wagener Galerie",
      description:
        "Upscale shopping center with designer brands and exclusive boutiques.",
    },
  ];

  // Accommodation options
  const hotels = [
    {
      name: "Brenners Park-Hotel & Spa",
      description:
        "Historic 5-star luxury hotel set in private park grounds with world-class spa and elegant rooms.",
    },
    {
      name: "Hotel Belle Epoque",
      description:
        "Boutique hotel in a 19th-century villa with period furnishings and beautiful gardens.",
    },
    {
      name: "Roomers Baden-Baden",
      description:
        "Modern design hotel with rooftop pool, spa, and contemporary styling.",
    },
    {
      name: "Der Kleine Prinz",
      description:
        "Charming hotel with individually decorated rooms and excellent restaurant in the city center.",
    },
  ];

  // Upcoming events
  const events = [
    {
      name: "International Horse Racing",
      date: "May and August",
      description:
        "Prestigious racing events at the Iffezheim Racecourse, including the Grand Week in August.",
    },
    {
      name: "Baden-Baden Summer Festival",
      date: "June-September",
      description:
        "Classical music festival with performances by world-renowned orchestras and soloists.",
    },
    {
      name: "Christmas Market",
      date: "November-December",
      description:
        "Festive market in front of the Kurhaus with traditional crafts, food, and entertainment.",
    },
    {
      name: "Museum Mile Festival",
      date: "April",
      description:
        "Cultural event with special exhibitions and programs at Baden-Baden's museums.",
    },
  ];

  // Google Maps embed URL
  const mapUrl =
    "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d42309.74311647!2d8.2220913!3d48.7580777!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47971c0c34388b05%3A0x41ffd3c8d0949cf0!2sBaden-Baden%2C%20Germany!5e0!3m2!1sen!2sus!4v1653065799729!5m2!1sen!2sus";

  return (
    <CityTemplate
      cityKey="baden-baden"
      cityImages={cityImages}
      attractions={attractions}
      restaurants={restaurants}
      shopping={shopping}
      hotels={hotels}
      events={events}
      mapUrl={mapUrl}
      data-oid="0xm7yrq"
    />
  );
};

export default BadenBaden;
