import { Table, Thead, Tbody, Tr, Th, Td, Box, Text } from "@chakra-ui/react";

const CodeCell = ({ content = "" }) => {
  return (
    <Box
      fontFamily="monospace"
      py={1}
      px={2}
      borderRadius="5px"
      width="fit-content"
      fontWeight={500}
      color="#e9e9e9"
      backgroundColor="#222"
      data-oid="p8.g5e2"
    >
      {content}
    </Box>
  );
};

const PropTable = ({ data }) => {
  return (
    <Box mt={12} data-oid="22slvq:">
      <h2 className="demo-title-extra" data-oid="hj6p5ek">
        Props
      </h2>
      <Box overflowX="auto" mt={6} data-oid="1h9q7vf">
        <Table
          variant="unstyled"
          colorScheme="whiteAlpha"
          size="sm"
          className="props-table"
          data-oid="fof6nzi"
        >
          <Thead borderBottom="1px solid #222" data-oid="bj-4h10">
            <Tr
              backgroundColor="#0D0D0D"
              borderRadius="20px"
              data-oid="a6fan6q"
            >
              <Th
                letterSpacing="-.5px"
                borderRight="1px solid #222"
                textTransform={"capitalize"}
                fontSize={"l"}
                py={4}
                color="white"
                data-oid=":04p8ds"
              >
                Property
              </Th>
              <Th
                letterSpacing="-.5px"
                borderRight="1px solid #222"
                textTransform={"capitalize"}
                fontSize={"l"}
                py={4}
                color="white"
                data-oid="6_hpvcu"
              >
                Type
              </Th>
              <Th
                letterSpacing="-.5px"
                borderRight="1px solid #222"
                textTransform={"capitalize"}
                fontSize={"l"}
                py={4}
                color="white"
                data-oid="_z7sd._"
              >
                Default
              </Th>
              <Th
                letterSpacing="-.5px"
                textTransform={"capitalize"}
                fontSize={"l"}
                py={4}
                color="white"
                data-oid="4jg8rk4"
              >
                Description
              </Th>
            </Tr>
          </Thead>
          <Tbody data-oid="o_ztl6a">
            {data.map((prop, index) => (
              <Tr
                key={index}
                borderBottom={
                  index === data.length - 1 ? "none" : "1px solid #222"
                }
                data-oid="zvw4:o-"
              >
                <Td
                  borderColor="#222"
                  py={4}
                  color="white"
                  width={0}
                  pr={8}
                  borderRight="1px solid #222"
                  data-oid="winsbsv"
                >
                  <CodeCell
                    rightJustified
                    content={prop.name}
                    data-oid="_o76avh"
                  />
                </Td>
                <Td
                  borderColor="#222"
                  py={4}
                  color="white"
                  whiteSpace="nowrap"
                  width={"120px"}
                  borderRight="1px solid #222"
                  data-oid="y5:9eie"
                >
                  <Text
                    fontFamily="monospace"
                    fontWeight={500}
                    data-oid=".aja_lu"
                  >
                    {prop.type}
                  </Text>
                </Td>
                <Td
                  borderColor="#222"
                  py={4}
                  color="white"
                  borderRight="1px solid #222"
                  whiteSpace="nowrap"
                  data-oid="up22j2o"
                >
                  <CodeCell
                    content={
                      prop.default && prop.default.length ? prop.default : "—"
                    }
                    data-oid="zq7fwr-"
                  />
                </Td>
                <Td borderColor="#222" py={4} color="white" data-oid="z4p-gu1">
                  <Text maxW={300} data-oid="_0._ggh">
                    {prop.description}
                  </Text>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>
    </Box>
  );
};

export default PropTable;
