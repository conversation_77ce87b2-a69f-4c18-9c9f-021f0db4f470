/* Card Showcase Styles */
.services-showcase {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2.5rem;
  justify-content: center;
  margin-top: 2rem;
  width: 100%;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.service-card {
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid #D4AF37; /* Gold border */
  border-radius: 20px;
  padding: 2.5rem;
  width: 100%;
  height: 450px; /* Fixed height to match other cards */
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6), 0 0 15px rgba(212, 175, 55, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(10px);
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(90, 122, 154, 0.05) 0%, rgba(90, 122, 154, 0.2) 100%);
  z-index: -1;
}

.service-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.service-icon {
  width: 150px;
  height: 150px;
  border-radius: 50%; /* Changed to circular */
  display: flex;
  justify-content: center;
  align-items: center;
  border: 3px solid #D4AF37;
  box-shadow: 0 0 20px rgba(212, 175, 55, 0.4);
  transition: all 0.3s ease;
  overflow: hidden;
  background-color: #0a0a0a;
  position: relative;
}

.service-icon-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.service-icon:hover .service-icon-img {
  transform: scale(1.1);
}

.service-title {
  flex: 1;
}

.service-name {
  color: #F0C14B; /* Brighter gold color */
  font-size: 2rem; /* Larger title as requested */
  font-weight: 800;
  margin-bottom: 0.75rem;
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
  letter-spacing: 0.5px;
}

.service-company {
  color: #fff;
  font-size: 1.2rem;
  opacity: 0.9;
}

.service-date {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: #D4AF37;
  opacity: 0.7;
}

.service-content {
  margin-top: 2rem;
  flex-grow: 1;
}

.service-points {
  list-style: none;
  padding: 0;
  margin: 0;
}

.service-point {
  position: relative;
  padding-left: 1.8rem;
  margin-bottom: 1.2rem;
  color: #fff;
  font-size: 1.1rem;
  line-height: 1.6;
  transition: all 0.2s ease;
}

html[dir="rtl"] .service-point {
  padding-left: 0;
  padding-right: 1.8rem;
}

.service-point::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #F0C14B; /* Brighter gold color */
  font-size: 1.5rem;
  top: -0.2rem;
}

html[dir="rtl"] .service-point::before {
  left: auto;
  right: 0;
}

.service-accent {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, transparent, #D4AF37, transparent);
}

/* Hover effects */
.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.8), 0 0 25px rgba(240, 193, 75, 0.4);
  border-color: #F0C14B;
}

.service-card:hover .service-icon {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 0 25px rgba(240, 193, 75, 0.8);
  border-color: #F0C14B;
}

.service-card:hover .service-name {
  text-shadow: 0 0 15px rgba(240, 193, 75, 0.5);
  color: #FFD700; /* Brighter gold on hover */
}

/* Card Link Styles */
.card-link, .card-link-wrapper {
  text-decoration: none;
  color: inherit;
  display: block;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

/* Special styling for carpool cards */
.carpool-card {
  cursor: pointer;
}

.carpool-card .service-card {
  position: relative;
}

/* CTA Button Styles */
.service-cta {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  text-align: center;
}

.view-details-btn {
  display: inline-block;
  color: #F0C14B; /* Brighter gold color */
  font-weight: 700;
  font-size: 1.2rem;
  padding: 0.7rem 2rem;
  border: 2px solid #F0C14B;
  border-radius: 30px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

/* Gallery indicator for carpool cards only */
.carpool-card .service-card::after {
  content: '🖼️';
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  background: rgba(0, 0, 0, 0.5);
  color: #D4AF37;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  opacity: 0.7;
  transition: all 0.3s ease;
  z-index: 2;
}

.carpool-card:hover .service-card::after {
  opacity: 1;
  transform: scale(1.1);
  box-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
}

.view-details-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.2), transparent);
  transition: all 0.5s ease;
}

.view-details-btn:hover {
  background: rgba(212, 175, 55, 0.1);
  box-shadow: 0 0 15px rgba(212, 175, 55, 0.5);
  transform: translateY(-2px);
}

.view-details-btn:hover::before {
  left: 100%;
}

.view-details-btn .arrow {
  display: inline-block;
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

html[dir="rtl"] .view-details-btn .arrow {
  transform: rotate(180deg);
  margin-left: 0;
  margin-right: 0.5rem;
}

.view-details-btn:hover .arrow {
  transform: translateX(5px);
}

html[dir="rtl"] .view-details-btn:hover .arrow {
  transform: rotate(180deg) translateX(5px);
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .services-showcase {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .service-card {
    padding: 2rem;
    height: 450px;
  }

  .service-icon {
    width: 120px;
    height: 120px;
  }

  .service-name {
    font-size: 1.8rem;
  }
}

@media (max-width: 992px) {
  .services-showcase {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .service-card {
    height: 450px;
  }
}

@media (max-width: 768px) {
  .services-showcase {
    grid-template-columns: 1fr;
    max-width: 500px;
    margin: 2rem auto 0;
  }

  .service-card {
    max-width: 100%;
    height: 450px;
    padding: 1.5rem;
  }

  .service-header {
    flex-direction: row;
    align-items: center;
    gap: 1.5rem;
    text-align: left;
  }

  html[dir="rtl"] .service-header {
    text-align: right;
  }

  .service-icon {
    width: 100px;
    height: 100px;
    flex-shrink: 0;
  }

  .service-title {
    margin-top: 0;
  }

  .service-name {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .service-company {
    font-size: 1rem;
  }

  .service-point {
    font-size: 1rem;
    margin-bottom: 0.8rem;
  }
}

@media (max-width: 480px) {
  .service-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  html[dir="rtl"] .service-header {
    text-align: center;
  }

  .service-title {
    margin-top: 0.5rem;
  }

  .service-icon {
    width: 120px;
    height: 120px;
  }
}
