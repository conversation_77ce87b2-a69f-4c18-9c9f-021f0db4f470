import { useEffect, useRef, Suspense, lazy } from "react";
import { useParams } from "react-router-dom";
import { componentMap } from "../constants/Components";
import { decodeLabel } from "../utils/utils";
import { Helmet } from "react-helmet-async";
import { Box } from "@chakra-ui/react";

import BackToTopButton from "../components/common/BackToTopButton";

const CategoryPage = () => {
  const { subcategory } = useParams();
  const scrollRef = useRef(null);

  const SubcategoryComponent = subcategory
    ? lazy(componentMap[subcategory])
    : null;

  useEffect(() => {
    scrollRef.current.scrollTo(0, 0);
  }, [subcategory]);

  return (
    <Box className="category-page" ref={scrollRef} data-oid="_9584rh">
      <Helmet data-oid="oi7m4l5">
        <title data-oid=":nlz6fd">
          React Bits - {decodeLabel(subcategory)}
        </title>
      </Helmet>

      <h2 className="sub-category" data-oid="l5xex3f">
        {decodeLabel(subcategory)}
      </h2>

      <Suspense data-oid="d6:s5fu">
        <SubcategoryComponent data-oid="1xt5bvh" />
      </Suspense>

      <BackToTopButton data-oid="6rc8mrp" />
    </Box>
  );
};

export default CategoryPage;
