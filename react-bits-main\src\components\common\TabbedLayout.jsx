// TabbedLayout.js
import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>bPanels,
  TabPanel,
  <PERSON>lex,
  Icon,
} from "@chakra-ui/react";
import { FiCode, FiEye, FiHeart, FiTerminal } from "react-icons/fi";
import ContributionSection from "./ContributionSection";

const tabStyles = {
  _selected: { color: "#fff", bg: "#111" },
  borderRadius: "10px",
  bg: "#060606",
  fontSize: "14px",
  border: "1px solid #ffffff1c",
  height: 9,
  padding: "0.5rem 1rem",
  transition: "background-color 0.3s",
  "&:hover": { bg: "#222" },
};

const TabbedLayout = ({ children, className }) => {
  const contentMap = {
    PreviewTab: null,
    CodeTab: null,
    CliTab: null,
  };

  React.Children.forEach(children, (child) => {
    if (child.type === PreviewTab) {
      contentMap.PreviewTab = child;
    } else if (child.type === CodeTab) {
      contentMap.CodeTab = child;
    } else if (child.type === CliTab) {
      contentMap.CliTab = child;
    }
  });

  return (
    <Tabs
      mt={4}
      variant="unstyled"
      isLazy
      lazyBehavior="unmountOnExit"
      className={className}
      w="100%"
      data-oid="45g:3mk"
    >
      <TabList justifyContent="space-between" data-oid="klllcw:">
        <Flex wrap="wrap" gap="0.5rem" data-oid="u:0x787">
          <Tab sx={tabStyles} data-oid="86t2b:w">
            <Icon as={FiEye} data-oid="9m7w3ow" />
            &nbsp;Preview
          </Tab>
          <Tab sx={tabStyles} data-oid="a-pmzni">
            <Icon as={FiCode} data-oid="hnqw9gh" />
            &nbsp;Code
          </Tab>
          <Tab
            sx={{ ...tabStyles, marginRight: "0.5rem" }}
            className="cli-tab"
            data-oid="t1m4-mp"
          >
            <Icon as={FiTerminal} data-oid="epy87ti" />
            &nbsp;CLI
          </Tab>
        </Flex>
        <Tab sx={tabStyles} className="contribute-tab" data-oid="v__zmxu">
          <Icon as={FiHeart} data-oid="u.5lnda" />
          &nbsp;Contribute
        </Tab>
      </TabList>

      <TabPanels data-oid="fc9sv7s">
        <TabPanel p={0} data-oid="oh.in74">
          {contentMap.PreviewTab}
        </TabPanel>
        <TabPanel p={0} data-oid="jbjclym">
          {contentMap.CodeTab}
        </TabPanel>
        <TabPanel p={0} data-oid="z-ztkmv">
          {contentMap.CliTab}
        </TabPanel>
        <TabPanel p={0} data-oid="ix8r06o">
          <ContributionSection data-oid=".6kkkb1" />
        </TabPanel>
      </TabPanels>
    </Tabs>
  );
};

// Helper components to wrap tab content
const PreviewTab = ({ children }) => <>{children}</>;
const CodeTab = ({ children }) => <>{children}</>;
const CliTab = ({ children }) => <>{children}</>;

export { TabbedLayout, PreviewTab, CodeTab, CliTab };
