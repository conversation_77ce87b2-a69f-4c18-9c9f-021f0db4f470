import { useState } from "react";
import { Box, Text } from "@chakra-ui/react";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";

import RefreshButton from "../../components/common/RefreshButton";
import CodeExample from "../../components/code/CodeExample";
import useForceRerender from "../../hooks/useForceRerender";
import PropTable from "../../components/common/PropTable";
import CliInstallation from "../../components/code/CliInstallation";
import PreviewSlider from "../../components/common/PreviewSlider";
import Customize from "../../components/common/Customize";

import Noise from "../../content/Animations/Noise/Noise";
import { noise } from "../../constants/code/Animations/noiseCode";

const NoiseDemo = () => {
  const [patternSize, setPatternSize] = useState(250);
  const [patternScaleX, setPatternScaleX] = useState(1);
  const [patternScaleY, setPatternScaleY] = useState(1);
  const [patternAlpha, setPatternAlpha] = useState(15);

  const [key, forceRerender] = useForceRerender();

  const propData = [
    {
      name: "patternSize",
      type: "number",
      default: 250,
      description: "Defines the size of the grain pattern.",
    },
    {
      name: "patternScaleX",
      type: "number",
      default: 1,
      description: "Scaling factor for the X-axis of the grain pattern.",
    },
    {
      name: "patternScaleY",
      type: "number",
      default: 1,
      description: "Scaling factor for the Y-axis of the grain pattern.",
    },
    {
      name: "patternRefreshInterval",
      type: "number",
      default: 2,
      description: "Number of frames before the grain pattern refreshes.",
    },
    {
      name: "patternAlpha",
      type: "number",
      default: 15,
      description: "Opacity of the grain pattern (0-255).",
    },
  ];

  return (
    <TabbedLayout data-oid="bwdy_f5">
      <PreviewTab data-oid=":kxdo20">
        <Box
          position="relative"
          className="demo-container"
          background="#060606"
          minH={400}
          overflow="hidden"
          data-oid="6mo810c"
        >
          <Text
            color="#111"
            fontSize="6rem"
            fontWeight={900}
            textAlign={"center"}
            data-oid="ysdvg-6"
          >
            Ooh, edgy!
          </Text>
          <Noise
            key={key}
            patternSize={patternSize}
            patternScaleX={patternScaleX}
            patternScaleY={patternScaleY}
            patternAlpha={patternAlpha}
            data-oid="acxs4vf"
          />

          <RefreshButton onClick={forceRerender} data-oid="itfpenm" />
        </Box>

        <Customize data-oid="4dipuyg">
          <PreviewSlider
            title="Pattern Size"
            min={50}
            max={500}
            step={10}
            value={patternSize}
            valueUnit="px"
            onChange={(val) => {
              setPatternSize(val);
              forceRerender();
            }}
            data-oid="2gwyi.r"
          />

          <PreviewSlider
            title="Scale X"
            min={0.1}
            max={5}
            step={0.1}
            value={patternScaleX}
            onChange={(val) => {
              setPatternScaleX(val);
              forceRerender();
            }}
            data-oid="f1_mlhx"
          />

          <PreviewSlider
            title="Scale Y"
            min={0.1}
            max={5}
            step={0.1}
            value={patternScaleY}
            onChange={(val) => {
              setPatternScaleY(val);
              forceRerender();
            }}
            data-oid="gxueo5c"
          />

          <PreviewSlider
            title="Pattern Alpha"
            min={0}
            max={25}
            step={5}
            value={patternAlpha}
            onChange={(val) => {
              setPatternAlpha(val);
              forceRerender();
            }}
            data-oid="6q.wsmb"
          />
        </Customize>

        <PropTable data={propData} data-oid="b:p019o" />
      </PreviewTab>

      <CodeTab data-oid="fp.hhgq">
        <CodeExample codeObject={noise} data-oid="r6mp5gq" />
      </CodeTab>

      <CliTab data-oid=":60baq2">
        <CliInstallation {...noise} data-oid="3_vebw:" />
      </CliTab>
    </TabbedLayout>
  );
};

export default NoiseDemo;
