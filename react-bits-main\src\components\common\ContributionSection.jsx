// ContributionSection.js
import { Box, Button, Flex, Icon, Text } from "@chakra-ui/react";
import { TbBug, TbBulb } from "react-icons/tb";
import { useParams } from "react-router-dom";

const ContributionSection = () => {
  const { subcategory, category } = useParams();

  return (
    <Box className="contribute-container" data-oid="9qjh-_w">
      <Text
        fontSize={{ base: "1rem", md: "1.3rem" }}
        className="demo-title-contribute"
        data-oid="94h_ffm"
      >
        Help us improve this component!
      </Text>
      <Flex
        gap={2}
        justifyContent="center"
        alignItems="center"
        direction={{ base: "column", md: "row" }}
        data-oid="kjaa4b0"
      >
        <Button
          cursor="pointer"
          as="a"
          href={`https://github.com/DavidHDev/react-bits/issues/new?template=1-bug-report.yml&title=${encodeURIComponent(`[BUG]: ${category}/${subcategory}`)}&labels=bug`}
          rel="noreferrer"
          target="_blank"
          fontSize="sm"
          height={9}
          rounded="xl"
          className="contribute-button"
          w={{ base: "90%", md: "auto" }}
          data-oid="ythfrkl"
        >
          <Icon as={TbBug} data-oid="dun26h1" />
          &nbsp;Report an issue
        </Button>
        <Text
          mx={2}
          color="#a1a1aa"
          display={{ base: "none", md: "inline" }}
          data-oid="zh:ag7x"
        >
          or
        </Text>
        <Button
          cursor="pointer"
          as="a"
          href={`https://github.com/DavidHDev/react-bits/issues/new?template=2-feature-request.yml&title=${encodeURIComponent(`[FEAT]: ${category}/${subcategory}`)}&labels=enhancement`}
          rel="noreferrer"
          target="_blank"
          fontSize="sm"
          height={9}
          rounded="xl"
          className="contribute-button"
          w={{ base: "90%", md: "auto" }}
          data-oid="1bcom.w"
        >
          <Icon as={TbBulb} data-oid="eu5cgms" />
          &nbsp;Request a feature
        </Button>
      </Flex>
    </Box>
  );
};

export default ContributionSection;
