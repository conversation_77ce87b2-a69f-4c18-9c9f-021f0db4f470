import { useState, useRef } from "react";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import {
  Box,
  Button,
  Flex,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Text,
} from "@chakra-ui/react";

import PropTable from "../../components/common/PropTable";
import CliInstallation from "../../components/code/CliInstallation";
import CodeExample from "../../components/code/CodeExample";
import Dependencies from "../../components/code/Dependencies";

import VariableProximity from "../../content/TextAnimations/VariableProximity/VariableProximity";
import { variableProximity } from "../../constants/code/TextAnimations/variableProximityCode";

const VariableProximityDemo = () => {
  const containerRef = useRef(null);

  const [radius, setRadius] = useState(100);
  const [falloff, setFalloff] = useState("linear");

  const propData = [
    {
      name: "label",
      type: "string",
      default: '""',
      description: "The text content to display.",
    },
    {
      name: "fromFontVariationSettings",
      type: "string",
      default: "'wght' 400, 'opsz' 9",
      description: "The starting variation settings.",
    },
    {
      name: "toFontVariationSettings",
      type: "string",
      default: "'wght' 800, 'opsz' 40",
      description: "The variation settings to reach at cursor proximity.",
    },
    {
      name: "containerRef",
      type: "RefObject<HTMLDivElement>",
      default: "undefined",
      description: "Reference to container for relative calculations.",
    },
    {
      name: "radius",
      type: "number",
      default: "50",
      description: "Proximity radius to influence the effect.",
    },
    {
      name: "falloff",
      type: "'linear' | 'exponential' | 'gaussian'",
      default: '"linear"',
      description: "Type of falloff for the effect.",
    },
  ];

  return (
    <TabbedLayout data-oid="p66ror1">
      <PreviewTab data-oid="g9x94mu">
        <Box
          ref={containerRef}
          position="relative"
          className="demo-container"
          minH={400}
          overflow="hidden"
          p={4}
          data-oid="js99o5n"
        >
          <VariableProximity
            label={"Hover me! And then star React Bits on GitHub, or else..."}
            className={"variable-proximity-demo"}
            fromFontVariationSettings="'wght' 400, 'opsz' 9"
            toFontVariationSettings="'wght' 1000, 'opsz' 40"
            containerRef={containerRef}
            radius={radius}
            falloff={falloff}
            data-oid="05.yip5"
          />
        </Box>

        <Box mt={6} className="preview-options" data-oid="vz4bx-n">
          <Text fontSize="xl" mb={2} data-oid="b6g:p7t">
            Customize
          </Text>
          <Flex gap={4} align="center" mt={4} data-oid="shj-_1o">
            <Text fontSize="sm" data-oid="6srl29-">
              Radius:
            </Text>
            <Slider
              min={50}
              max={300}
              step={10}
              value={radius}
              onChange={(val) => setRadius(val)}
              width="200px"
              data-oid="uiykeo-"
            >
              <SliderTrack data-oid="0g_xbh6">
                <SliderFilledTrack data-oid="8f1a1.j" />
              </SliderTrack>
              <SliderThumb data-oid="cl-lj8u" />
            </Slider>
            <Text fontSize="sm" data-oid="s_qygkg">
              {radius}px
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="z4v3icm">
            <Text fontSize="sm" data-oid="hz93ysz">
              Falloff:
            </Text>
            {["linear", "exponential", "gaussian"].map((type) => (
              <Button
                key={type}
                size="sm"
                colorScheme="gray"
                color={type === falloff ? "#00d8ff" : "white"}
                onClick={() => setFalloff(type)}
                data-oid="b3:o9iy"
              >
                {type}
              </Button>
            ))}
          </Flex>
        </Box>

        <PropTable data={propData} data-oid="dkzwf-f" />
        <Dependencies dependencyList={["framer-motion"]} data-oid="tjfge6_" />
      </PreviewTab>

      <CodeTab data-oid=".a0lwha">
        <CodeExample codeObject={variableProximity} data-oid="bu4hern" />
      </CodeTab>

      <CliTab data-oid="wiquh7m">
        <CliInstallation {...variableProximity} data-oid="grnw:jp" />
      </CliTab>
    </TabbedLayout>
  );
};

export default VariableProximityDemo;
