import React, { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { styles } from "../../../styles";
import { fadeIn, textVariant } from "../../../utils/motion";
import { StarsCanvas } from "../../../components/canvas";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../../../contexts/LanguageContext";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faLandmark,
  faUtensils,
  faShoppingBag,
  faHotel,
  faCalendarAlt,
  faMapMarkedAlt,
  faInfoCircle,
  faChevronRight,
  faChevronLeft,
} from "@fortawesome/free-solid-svg-icons";

// City Template Component
const CityTemplate = ({
  cityKey,
  cityImages,
  attractions = [],
  restaurants = [],
  shopping = [],
  hotels = [],
  events = [],
  mapUrl = "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2663.5369603049446!2d11.574216!3d48.137079!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x479e75f9a38c5fd9%3A0x10cb84a7db1987d!2sMunich%2C%20Germany!5e0!3m2!1sen!2sus!4v1653060799729!5m2!1sen!2sus",
}) => {
  const { t } = useTranslation();
  const { dir, language } = useLanguage();
  const [activeImage, setActiveImage] = useState(0);
  const [activeTab, setActiveTab] = useState("attractions");

  // Function to handle image navigation
  const nextImage = () => {
    setActiveImage((prev) => (prev + 1) % cityImages.length);
  };

  const prevImage = () => {
    setActiveImage(
      (prev) => (prev - 1 + cityImages.length) % cityImages.length,
    );
  };

  // Get city information from translations
  const cityName = t(
    `tourism-pages.popular-destinations.destinations.${cityKey}.name`,
  );
  const cityDescription = t(
    `tourism-pages.popular-destinations.destinations.${cityKey}.description`,
  );
  const cityLongDescription = t(
    `tourism-pages.popular-destinations.destinations.${cityKey}.long-description`,
    { defaultValue: cityDescription },
  );

  return (
    <div className="relative w-full h-auto mx-auto" data-oid=".xgqcc4">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid="gwxr.wr"
      >
        {/* City Header */}
        <motion.div variants={textVariant()} data-oid="ovl::is">
          <p
            className={`${styles.sectionSubText} text-center text-[#D4AF37]`}
            data-oid="2ecifk1"
          >
            {t("tourism-pages.city-template.subtitle")}
          </p>
          <h2
            className={`${styles.sectionHeadText} text-center`}
            data-oid="verq9th"
          >
            {cityName}
          </h2>
        </motion.div>

        {/* City Gallery */}
        <div className="mt-10 relative" data-oid="wtn4txy">
          <div
            className="w-full h-[400px] md:h-[500px] relative overflow-hidden rounded-xl border-2 border-[#D4AF37]"
            data-oid="0m-pc2o"
          >
            <motion.img
              key={activeImage}
              src={cityImages[activeImage]}
              alt={`${cityName} - ${activeImage + 1}`}
              className="w-full h-full object-cover"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              data-oid="1daz.m1"
            />

            {/* Navigation arrows */}
            {cityImages.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-[#D4AF37] p-3 rounded-full hover:bg-opacity-70 transition-all"
                  data-oid="_743ley"
                >
                  <FontAwesomeIcon
                    icon={faChevronLeft}
                    size="lg"
                    data-oid="co3hjk:"
                  />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-[#D4AF37] p-3 rounded-full hover:bg-opacity-70 transition-all"
                  data-oid="xjrhjq6"
                >
                  <FontAwesomeIcon
                    icon={faChevronRight}
                    size="lg"
                    data-oid="_3.952g"
                  />
                </button>
              </>
            )}

            {/* Image counter */}
            {cityImages.length > 1 && (
              <div
                className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full"
                data-oid="b_jgthp"
              >
                {activeImage + 1} / {cityImages.length}
              </div>
            )}
          </div>

          {/* Thumbnail navigation */}
          {cityImages.length > 1 && (
            <div
              className="mt-4 flex justify-center gap-2 overflow-x-auto pb-2"
              data-oid="3f5umxa"
            >
              {cityImages.map((img, idx) => (
                <div
                  key={idx}
                  className={`w-20 h-20 cursor-pointer rounded-md overflow-hidden border-2 ${activeImage === idx ? "border-[#D4AF37]" : "border-transparent"}`}
                  onClick={() => setActiveImage(idx)}
                  data-oid=":jz:w:o"
                >
                  <img
                    src={img}
                    alt={`${cityName} thumbnail ${idx + 1}`}
                    className="w-full h-full object-cover"
                    data-oid="aigb3ox"
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* City Description */}
        <motion.div
          variants={fadeIn("", "", 0.1, 1)}
          className="mt-10 bg-black bg-opacity-70 p-6 rounded-xl border border-[#D4AF37]"
          data-oid="vxug6i7"
        >
          <h3
            className="text-[#D4AF37] font-bold text-[24px] mb-4"
            data-oid="0d_6k2s"
          >
            <FontAwesomeIcon
              icon={faInfoCircle}
              className="mr-2"
              data-oid="1vtrp3w"
            />
            {t("tourism-pages.city-template.about")} {cityName}
          </h3>
          <p
            className="text-white text-[17px] leading-[30px]"
            style={{ direction: dir }}
            data-oid="cfszt_r"
          >
            {cityLongDescription}
          </p>
        </motion.div>

        {/* City Information Tabs */}
        <div className="mt-10" data-oid="zoqwrsc">
          <div
            className="flex flex-wrap justify-center gap-2 mb-6"
            data-oid="xh0frr-"
          >
            <button
              className={`px-4 py-2 rounded-lg border ${activeTab === "attractions" ? "bg-[#D4AF37] text-black font-bold" : "border-[#D4AF37] text-[#D4AF37]"}`}
              onClick={() => setActiveTab("attractions")}
              data-oid="5x9m-o6"
            >
              <FontAwesomeIcon
                icon={faLandmark}
                className="mr-2"
                data-oid="r7vb1bc"
              />

              {t("tourism-pages.city-template.attractions")}
            </button>
            <button
              className={`px-4 py-2 rounded-lg border ${activeTab === "dining" ? "bg-[#D4AF37] text-black font-bold" : "border-[#D4AF37] text-[#D4AF37]"}`}
              onClick={() => setActiveTab("dining")}
              data-oid="q48jjf7"
            >
              <FontAwesomeIcon
                icon={faUtensils}
                className="mr-2"
                data-oid="w97ezlt"
              />

              {t("tourism-pages.city-template.dining")}
            </button>
            <button
              className={`px-4 py-2 rounded-lg border ${activeTab === "shopping" ? "bg-[#D4AF37] text-black font-bold" : "border-[#D4AF37] text-[#D4AF37]"}`}
              onClick={() => setActiveTab("shopping")}
              data-oid="c-3.hhs"
            >
              <FontAwesomeIcon
                icon={faShoppingBag}
                className="mr-2"
                data-oid="uuzvji8"
              />

              {t("tourism-pages.city-template.shopping")}
            </button>
            <button
              className={`px-4 py-2 rounded-lg border ${activeTab === "accommodation" ? "bg-[#D4AF37] text-black font-bold" : "border-[#D4AF37] text-[#D4AF37]"}`}
              onClick={() => setActiveTab("accommodation")}
              data-oid="u7auhvw"
            >
              <FontAwesomeIcon
                icon={faHotel}
                className="mr-2"
                data-oid="4:o3it2"
              />

              {t("tourism-pages.city-template.accommodation")}
            </button>
            <button
              className={`px-4 py-2 rounded-lg border ${activeTab === "events" ? "bg-[#D4AF37] text-black font-bold" : "border-[#D4AF37] text-[#D4AF37]"}`}
              onClick={() => setActiveTab("events")}
              data-oid="pl53178"
            >
              <FontAwesomeIcon
                icon={faCalendarAlt}
                className="mr-2"
                data-oid="ssap6jb"
              />

              {t("tourism-pages.city-template.events")}
            </button>
            <button
              className={`px-4 py-2 rounded-lg border ${activeTab === "map" ? "bg-[#D4AF37] text-black font-bold" : "border-[#D4AF37] text-[#D4AF37]"}`}
              onClick={() => setActiveTab("map")}
              data-oid="z0bp-38"
            >
              <FontAwesomeIcon
                icon={faMapMarkedAlt}
                className="mr-2"
                data-oid="5mt6diy"
              />

              {t("tourism-pages.city-template.map")}
            </button>
          </div>

          {/* Tab Content */}
          <div
            className="bg-black bg-opacity-70 p-6 rounded-xl border border-[#D4AF37]"
            data-oid="ye4:04-"
          >
            {activeTab === "attractions" && (
              <div data-oid="6t.-37y">
                <h3
                  className="text-[#D4AF37] font-bold text-[24px] mb-4"
                  data-oid="cchm5_x"
                >
                  {t("tourism-pages.city-template.top-attractions")}
                </h3>
                <ul className="space-y-4" data-oid="h1vtel6">
                  {attractions.length > 0 ? (
                    attractions.map((attraction, index) => (
                      <li key={index} className="text-white" data-oid="6fadm3z">
                        <h4
                          className="text-[#D4AF37] font-semibold text-[18px]"
                          data-oid="iwrcqk6"
                        >
                          {attraction.name}
                        </h4>
                        <p className="text-[16px]" data-oid="5jj_m5a">
                          {attraction.description}
                        </p>
                      </li>
                    ))
                  ) : (
                    <p className="text-white text-[16px]" data-oid="lrn8dqk">
                      {t("tourism-pages.city-template.no-attractions")}
                    </p>
                  )}
                </ul>
              </div>
            )}

            {activeTab === "dining" && (
              <div data-oid="jj1j56t">
                <h3
                  className="text-[#D4AF37] font-bold text-[24px] mb-4"
                  data-oid="hhltbhw"
                >
                  {t("tourism-pages.city-template.recommended-restaurants")}
                </h3>
                <ul className="space-y-4" data-oid="mxrgxml">
                  {restaurants.length > 0 ? (
                    restaurants.map((restaurant, index) => (
                      <li key={index} className="text-white" data-oid="h77906.">
                        <h4
                          className="text-[#D4AF37] font-semibold text-[18px]"
                          data-oid="g39xih_"
                        >
                          {restaurant.name}
                        </h4>
                        <p className="text-[16px]" data-oid="gh3qfph">
                          {restaurant.description}
                        </p>
                      </li>
                    ))
                  ) : (
                    <p className="text-white text-[16px]" data-oid="3onfe61">
                      {t("tourism-pages.city-template.no-restaurants")}
                    </p>
                  )}
                </ul>
              </div>
            )}

            {activeTab === "shopping" && (
              <div data-oid="7by-o8e">
                <h3
                  className="text-[#D4AF37] font-bold text-[24px] mb-4"
                  data-oid="a37st17"
                >
                  {t("tourism-pages.city-template.shopping-areas")}
                </h3>
                <ul className="space-y-4" data-oid="gk-omfi">
                  {shopping.length > 0 ? (
                    shopping.map((shop, index) => (
                      <li key={index} className="text-white" data-oid="zwz5ws8">
                        <h4
                          className="text-[#D4AF37] font-semibold text-[18px]"
                          data-oid="q5x8.dw"
                        >
                          {shop.name}
                        </h4>
                        <p className="text-[16px]" data-oid="x5pkv4f">
                          {shop.description}
                        </p>
                      </li>
                    ))
                  ) : (
                    <p className="text-white text-[16px]" data-oid="3pw1w-:">
                      {t("tourism-pages.city-template.no-shopping")}
                    </p>
                  )}
                </ul>
              </div>
            )}

            {activeTab === "accommodation" && (
              <div data-oid="abm9lx.">
                <h3
                  className="text-[#D4AF37] font-bold text-[24px] mb-4"
                  data-oid="knroajc"
                >
                  {t("tourism-pages.city-template.accommodation-options")}
                </h3>
                <ul className="space-y-4" data-oid="noc4-4p">
                  {hotels.length > 0 ? (
                    hotels.map((hotel, index) => (
                      <li key={index} className="text-white" data-oid="qls6fhw">
                        <h4
                          className="text-[#D4AF37] font-semibold text-[18px]"
                          data-oid="fcskw4r"
                        >
                          {hotel.name}
                        </h4>
                        <p className="text-[16px]" data-oid=":89mibk">
                          {hotel.description}
                        </p>
                      </li>
                    ))
                  ) : (
                    <p className="text-white text-[16px]" data-oid="49snbm6">
                      {t("tourism-pages.city-template.no-hotels")}
                    </p>
                  )}
                </ul>
              </div>
            )}

            {activeTab === "events" && (
              <div data-oid="7cdfhfn">
                <h3
                  className="text-[#D4AF37] font-bold text-[24px] mb-4"
                  data-oid="tnj3nrk"
                >
                  {t("tourism-pages.city-template.upcoming-events")}
                </h3>
                <ul className="space-y-4" data-oid="agng3h3">
                  {events.length > 0 ? (
                    events.map((event, index) => (
                      <li key={index} className="text-white" data-oid=":h04xu2">
                        <h4
                          className="text-[#D4AF37] font-semibold text-[18px]"
                          data-oid="bonxm6e"
                        >
                          {event.name}
                        </h4>
                        <p className="text-[16px]" data-oid="dkaohdt">
                          {event.date} - {event.description}
                        </p>
                      </li>
                    ))
                  ) : (
                    <p className="text-white text-[16px]" data-oid="4rmm6iq">
                      {t("tourism-pages.city-template.no-events")}
                    </p>
                  )}
                </ul>
              </div>
            )}

            {activeTab === "map" && (
              <div data-oid="gcttylc">
                <h3
                  className="text-[#D4AF37] font-bold text-[24px] mb-4"
                  data-oid="mo5ixg_"
                >
                  {t("tourism-pages.city-template.map")}
                </h3>
                <div
                  className="w-full h-[400px] rounded-xl overflow-hidden"
                  data-oid="hhr-cie"
                >
                  <iframe
                    src={mapUrl}
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen=""
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title={`Map of ${cityName}`}
                    data-oid="xf4-b47"
                  ></iframe>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Call to Action */}
        <div
          className="mt-10 bg-black rounded-2xl p-8 border border-[#D4AF37]"
          data-oid="brmyf57"
        >
          <h3
            className="text-[#D4AF37] font-bold text-[24px] text-center"
            data-oid="nbrtkbm"
          >
            {t("tourism-pages.city-template.book-tour")}
          </h3>
          <p
            className="mt-4 text-white text-[17px] max-w-3xl leading-[30px] text-center mx-auto"
            style={{ direction: dir }}
            data-oid="2ajobgy"
          >
            {t("tourism-pages.city-template.book-tour-description")}
          </p>
          <div className="mt-5 flex justify-center" data-oid="0oeau:3">
            <Link
              to="/contact"
              className="bg-black py-3 px-8 rounded-xl outline-none w-fit text-[#D4AF37] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
              data-oid="zrrevpt"
            >
              {t("common.bookTour")}
            </Link>
          </div>
        </div>
      </div>
      <StarsCanvas data-oid="5j2nl54" />
    </div>
  );
};

export default CityTemplate;
