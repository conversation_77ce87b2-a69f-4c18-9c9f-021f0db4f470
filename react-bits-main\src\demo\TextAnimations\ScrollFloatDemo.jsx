import { useRef, useState, useEffect } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import {
  Box,
  Flex,
  Slider,
  SliderFilledTrack,
  SliderThumb,
  SliderTrack,
  Text,
} from "@chakra-ui/react";
import { gsap } from "gsap";

import useForceRerender from "../../hooks/useForceRerender";
import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";

import ScrollFloat from "../../content/TextAnimations/ScrollFloat/ScrollFloat";
import { scrollFloat } from "../../constants/code/TextAnimations/scrollFloatCode";

const ScrollFloatDemo = () => {
  const containerRef = useRef(null);
  const [stagger, setStagger] = useState(0.03);
  const [duration, setDuration] = useState(1);

  const [key, forceRerender] = useForceRerender();

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const smoothScroll = (e) => {
      e.preventDefault();
      const delta = e.deltaY || e.detail || e.wheelDelta;
      const scrollAmount = delta * 2;

      gsap.to(container, {
        scrollTop: container.scrollTop + scrollAmount,
        duration: 2,
        ease: "power3.out",
        overwrite: "auto",
      });
    };

    container.addEventListener("wheel", smoothScroll, { passive: false });

    return () => {
      container.removeEventListener("wheel", smoothScroll);
    };
  }, []);

  const propData = [
    {
      name: "children",
      type: "ReactNode",
      default: "—",
      description:
        "The content to animate. If a string, it will be split into individual characters.",
    },
    {
      name: "scrollContainerRef",
      type: "RefObject<HTMLElement>",
      default: "window",
      description:
        "Optional ref to the scroll container. Defaults to window if not provided.",
    },
    {
      name: "containerClassName",
      type: "string",
      default: '""',
      description: "Additional Tailwind classes for the container element.",
    },
    {
      name: "textClassName",
      type: "string",
      default: '""',
      description: "Additional Tailwind classes for the text element.",
    },
    {
      name: "animationDuration",
      type: "number",
      default: "1",
      description: "Duration (in seconds) of the animation.",
    },
    {
      name: "ease",
      type: "string",
      default: '"back.inOut(2)"',
      description: "Easing function used for the animation.",
    },
    {
      name: "scrollStart",
      type: "string",
      default: '"center bottom+=50%"',
      description: "The scroll trigger start position.",
    },
    {
      name: "scrollEnd",
      type: "string",
      default: '"bottom bottom-=40%"',
      description: "The scroll trigger end position.",
    },
    {
      name: "stagger",
      type: "number",
      default: "0.03",
      description: "Delay between the animation start of each character.",
    },
  ];

  return (
    <TabbedLayout data-oid="2df90on">
      <PreviewTab data-oid="dvjyk15">
        <Box
          className="demo-container"
          style={{ height: "500px", maxHeight: "500px" }}
          overflowY="scroll"
          overflowX="hidden"
          ref={containerRef}
          position="relative"
          data-oid="n.cwzx2"
        >
          <Text
            textAlign="center"
            color="#222"
            fontSize="clamp(4rem, 6vw, 4rem)"
            fontWeight={900}
            position="absolute"
            top="50%"
            transform="translateY(-50%)"
            data-oid="5.loorj"
          >
            Scroll Down
          </Text>
          <Box
            position="relative"
            pt={1600}
            pb={600}
            px="3rem"
            data-oid="46hp2nz"
          >
            <ScrollFloat
              stagger={stagger}
              animationDuration={duration}
              key={key}
              scrollContainerRef={containerRef}
              data-oid="2cnqvzl"
            >
              reactbits
            </ScrollFloat>
          </Box>
        </Box>

        <div className="preview-options" data-oid="rlkx5-o">
          <h2 className="demo-title-extra" data-oid="7sgevli">
            Customize
          </h2>

          <Flex gap={4} align="center" mt={4} data-oid="y1o4xjw">
            <Text fontSize="sm" data-oid="1g8b8aq">
              Stagger
            </Text>
            <Slider
              min={0.01}
              max={0.1}
              step={0.01}
              value={stagger}
              onChange={(val) => {
                containerRef.current.scrollTo({ top: 0, behavior: "smooth" });
                setStagger(val);
                forceRerender();
              }}
              width="150px"
              data-oid="v-07t7r"
            >
              <SliderTrack data-oid="5hxgx.a">
                <SliderFilledTrack data-oid="a6_anc1" />
              </SliderTrack>
              <SliderThumb data-oid="dmz_51o" />
            </Slider>
            <Text fontSize="sm" data-oid="_.scqdj">
              {stagger}
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="y87bjxf">
            <Text fontSize="sm" data-oid="3p:b.ys">
              Duration
            </Text>
            <Slider
              min={1}
              max={10}
              step={0.1}
              value={duration}
              onChange={(val) => {
                containerRef.current.scrollTo({ top: 0, behavior: "smooth" });
                setDuration(val);
                forceRerender();
              }}
              width="150px"
              data-oid="ffe9zoc"
            >
              <SliderTrack data-oid="-13f:40">
                <SliderFilledTrack data-oid=":7nh7l1" />
              </SliderTrack>
              <SliderThumb data-oid="6.-g.y." />
            </Slider>
            <Text fontSize="sm" data-oid="7n5c938">
              {duration}
            </Text>
          </Flex>
        </div>

        <PropTable data={propData} data-oid="b1ha_86" />
        <Dependencies dependencyList={["gsap"]} data-oid="jia7nal" />
      </PreviewTab>

      <CodeTab data-oid="dd0wgdl">
        <CodeExample codeObject={scrollFloat} data-oid="5pjy:0h" />
      </CodeTab>

      <CliTab data-oid=":ja7oba">
        <CliInstallation {...scrollFloat} data-oid="z_rvi27" />
      </CliTab>
    </TabbedLayout>
  );
};

export default ScrollFloatDemo;
