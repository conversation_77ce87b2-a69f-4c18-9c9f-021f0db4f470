import { Box, Flex, Text } from "@chakra-ui/react";
import { CodeTab, PreviewTab, TabbedLayout } from "../common/TabbedLayout";

import FadeContent from "../../content/Animations/FadeContent/FadeContent";
import CodeExample from "../code/CodeExample";

import variants from "../../assets/common/variants.svg";
import Iridescence from "../../content/Backgrounds/Iridescence/Iridescence";
import { iridescenceMock } from "../../constants/code/Backgrounds/iridescenceCode";

const LandingDemo = () => (
  <Flex
    w="100%"
    justifyContent="center"
    alignItems="center"
    direction="column"
    mb={12}
    mt={12}
    data-oid=".qvqg0c"
  >
    <FadeContent blur data-oid="2empgc6">
      <Flex
        w="100%"
        justifyContent="center"
        position="relative"
        top="1.6em"
        data-oid="o053m6t"
      >
        <img width={300} src={variants} alt="Variants" data-oid="x779glt" />
      </Flex>
      <Text
        textAlign="center"
        maxW="20ch"
        mb={4}
        lineHeight={1}
        color="#fff"
        fontSize="clamp(2rem, 6vw, 3rem)"
        data-oid="22lq4qm"
      >
        Simply copy & paste
      </Text>
      <Text
        textAlign="center"
        maxW={{ base: "25ch", sm: "100%" }}
        lineHeight={1}
        mb={6}
        fontSize="clamp(1rem, 2vw, 1.2rem)"
        letterSpacing="-0.5px"
        data-oid="g5b1g2g"
      >
        Pick your favourite technologies, copy, enjoy!
      </Text>
    </FadeContent>

    <FadeContent className="fade-full" blur data-oid="9sz.i91">
      <Flex
        maxH={300}
        maxW={1080}
        overflow="hidden"
        className="demo-landing"
        data-oid="_3jsumd"
      >
        <TabbedLayout className="landing-tabs" data-oid="so8wftz">
          <PreviewTab data-oid="dmv-9li">
            <Box
              position="relative"
              className="demo-container"
              h={230}
              p={0}
              w="100%"
              maxW={1080}
              overflow="hidden"
              data-oid="dih_6hr"
            >
              <Iridescence
                mouseReact={false}
                color={[0, 1, 1]}
                data-oid="0ggwpp."
              />

              <Text
                position="absolute"
                fontWeight={900}
                fontSize="2rem"
                mixBlendMode={"color-burn"}
                letterSpacing={-2}
                textAlign="center"
                color="#555"
                userSelect="none"
                data-oid="2gh5:yd"
              >
                {"<Iridescence />"}
              </Text>
            </Box>
          </PreviewTab>
          <CodeTab data-oid="z.ny7q8">
            <CodeExample codeObject={iridescenceMock} data-oid="unxvyoq" />
          </CodeTab>
        </TabbedLayout>
      </Flex>
    </FadeContent>
  </Flex>
);

export default LandingDemo;
