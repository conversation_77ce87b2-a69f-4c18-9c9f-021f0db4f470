import "./GlassIcons.css";

const gradientMapping = {
  blue: "linear-gradient(hsl(223, 90%, 50%), hsl(208, 90%, 50%))",
  purple: "linear-gradient(hsl(283, 90%, 50%), hsl(268, 90%, 50%))",
  red: "linear-gradient(hsl(3, 90%, 50%), hsl(348, 90%, 50%))",
  indigo: "linear-gradient(hsl(253, 90%, 50%), hsl(238, 90%, 50%))",
  orange: "linear-gradient(hsl(43, 90%, 50%), hsl(28, 90%, 50%))",
  green: "linear-gradient(hsl(123, 90%, 40%), hsl(108, 90%, 40%))",
};

const GlassIcons = ({ items, className }) => {
  const getBackgroundStyle = (color) => {
    if (gradientMapping[color]) {
      return { background: gradientMapping[color] };
    }
    return { background: color };
  };

  return (
    <div className={`icon-btns ${className || ""}`} data-oid="v6qfw97">
      {items.map((item, index) => (
        <button
          key={index}
          className={`icon-btn ${item.customClass || ""}`}
          aria-label={item.label}
          type="button"
          data-oid="9l336xx"
        >
          <span
            className="icon-btn__back"
            style={getBackgroundStyle(item.color)}
            data-oid="13c2g.5"
          ></span>
          <span className="icon-btn__front" data-oid="ilshshc">
            <span
              className="icon-btn__icon"
              aria-hidden="true"
              data-oid="cxtb0hu"
            >
              {item.icon}
            </span>
          </span>
          <span className="icon-btn__label" data-oid="p-9:lbb">
            {item.label}
          </span>
        </button>
      ))}
    </div>
  );
};

export default GlassIcons;
