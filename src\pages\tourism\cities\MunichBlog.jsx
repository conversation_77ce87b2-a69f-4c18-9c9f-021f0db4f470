import React, { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { styles } from "../../../styles";
import { fadeIn, textVariant } from "../../../utils/motion";
import { StarsCanvas } from "../../../components/canvas";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../../../contexts/LanguageContext";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faMapMarkerAlt,
  faUtensils,
  faClock,
  faEuroSign,
  faChevronRight,
  faChevronLeft,
  faHeart,
  faCamera,
  faGlassCheers,
  faLeaf,
  faLandmark,
  faShoppingBag,
  faHashtag,
} from "@fortawesome/free-solid-svg-icons";
import { faInstagram } from "@fortawesome/free-brands-svg-icons";
import { InstagramEmbed } from "react-social-media-embed";

import { muenchen, muenchen1, muenchen2 } from "../../../assets";

const MunichBlog = () => {
  const { t } = useTranslation();
  const { dir, language } = useLanguage();
  const [activeImage, setActiveImage] = useState(0);

  // City images
  const cityImages = [muenchen, muenchen1, muenchen2];

  // Function to handle image navigation
  const nextImage = () => {
    setActiveImage((prev) => (prev + 1) % cityImages.length);
  };

  const prevImage = () => {
    setActiveImage(
      (prev) => (prev - 1 + cityImages.length) % cityImages.length,
    );
  };

  // Blog posts for best places
  const blogPosts = [
    {
      title: "Marienplatz: The Heart of Munich",
      icon: faLandmark,
      image: muenchen,
      content: `
        <p>Standing in Marienplatz, you're at the very heart of Munich, where the city's pulse has been beating for over 850 years. This magnificent square is dominated by the Neo-Gothic New Town Hall (Neues Rathaus), whose façade is a playground for the eye with its intricate carvings and statues of Bavarian dukes, kings, and saints.</p>

        <p>The real star of the show is the famous Glockenspiel, which comes to life daily at 11 a.m., 12 p.m., and 5 p.m. (from March to October). As the 43 bells chime, 32 life-sized figures reenact two stories from Munich's history: a royal wedding from the 16th century and the Schäfflertanz (Cooper's Dance), which according to legend, was performed during the plague of 1517 to bring life back to the fearful city.</p>

        <p>For the best view of this spectacle, grab a coffee at one of the surrounding cafés and watch as tourists from around the world crane their necks upward in unified delight. After the show, climb the Town Hall tower for a breathtaking panorama of Munich's red rooftops stretching toward the Alps.</p>

        <p>Marienplatz is not just a tourist attraction but a living space where locals meet, musicians perform, and seasonal markets bring color and life throughout the year. During Christmas, the square transforms into one of Europe's most enchanting Christmas markets, with wooden stalls selling handcrafted gifts, mulled wine, and traditional treats under twinkling lights.</p>
      `,
      details: {
        location: "City Center, Munich",
        openingHours: "Always accessible",
        cost: "Free to visit",
        bestTime:
          "Early morning for fewer crowds or during Glockenspiel performances",
      },
    },
    {
      title: "English Garden: Munich's Green Oasis",
      icon: faLeaf,
      image: muenchen2,
      content: `
        <p>Larger than New York's Central Park and London's Hyde Park, the English Garden (Englischer Garten) is one of the world's greatest urban parks and Munich's beloved green lung. Created in 1789, this sprawling 910-acre paradise offers a refreshing escape from city life with its meadows, woodlands, and meandering streams.</p>

        <p>One of the park's most surprising attractions is the Eisbach wave, where river surfers in wetsuits ride a standing wave year-round, creating one of the most unusual urban spectacles in Europe. Gather with other spectators on the small bridge near the Haus der Kunst art museum to watch these skilled surfers perform tricks on this perpetual wave.</p>

        <p>As you wander deeper into the park, you'll discover architectural surprises like the Chinese Tower (Chinesischer Turm), a 25-meter wooden pagoda surrounded by Munich's second-largest beer garden. Here, under chestnut trees, join locals in the quintessential Munich experience: enjoying a Mass (liter) of beer and a pretzel while listening to traditional Bavarian brass bands on weekends.</p>

        <p>For a more tranquil experience, follow the paths along the Isar River's canals, where you might spot locals practicing tai chi or yoga in secluded meadows. In summer, some areas of the park become unofficial clothing-optional sunbathing spots, reflecting Munich's relaxed attitude.</p>

        <p>Don't miss the Monopteros, a Greek-style temple on a hilltop offering one of the best views of Munich's skyline, especially magical at sunset when the city's spires are bathed in golden light.</p>
      `,
      details: {
        location: "Northern Munich",
        openingHours: "24/7",
        cost: "Free",
        bestTime: "Sunny afternoons, especially on weekends",
      },
    },
    {
      title: "Viktualienmarkt: A Feast for the Senses",
      icon: faUtensils,
      image: muenchen1,
      content: `
        <p>Just a stone's throw from Marienplatz, Viktualienmarkt has been Munich's premier food market since 1807. What began as a simple farmers' market has evolved into a gourmet paradise spanning 22,000 square meters with over 140 stalls offering everything from fresh produce to exotic delicacies.</p>

        <p>Walking through the market is a sensory adventure: the vibrant colors of seasonal fruits and vegetables, the aroma of freshly baked bread, the sound of friendly banter between vendors and regulars, and of course, the taste of samples generously offered at many stalls.</p>

        <p>The market is organized into specialized sections. In the cheese section, discover over 200 varieties of regional German cheeses alongside international selections. The butchers offer traditional Bavarian specialties like Weisswurst (white sausage) and Leberkäse (a meatloaf-like delicacy). The flower market creates a perpetual spring with its kaleidoscope of blooms, while specialty shops sell everything from honey to exotic spices.</p>

        <p>At the heart of the market stands a maypole (Maibaum) decorated with figurines representing Munich's crafts and trades. Surrounding it is the market's beloved beer garden, where the unique "bring-your-own-food" policy allows you to purchase delicacies from any stall and enjoy them with a fresh beer served under chestnut trees.</p>

        <p>For the ultimate local experience, assemble a picnic of Bavarian specialties: obatzda (a savory cheese spread), fresh pretzels, radishes, and perhaps some sliced meat from the award-winning butchers. Then find a spot in the beer garden to savor your feast while watching Munich life unfold around you.</p>
      `,
      details: {
        location: "Just south of Marienplatz",
        openingHours:
          "Monday to Saturday, 8 a.m. to 8 p.m. (some stalls close earlier)",
        cost: "Free to enter, pay for what you purchase",
        bestTime: "Weekday mornings for fewer crowds",
      },
    },
    {
      title: "Hofbräuhaus: Bavaria's Most Famous Beer Hall",
      icon: faGlassCheers,
      image: muenchen,
      content: `
        <p>No visit to Munich is complete without experiencing the legendary Hofbräuhaus, the world's most famous beer hall. Founded in 1589 as the royal brewery for the Bavarian dukes, this historic establishment has been serving beer to locals and visitors for over 400 years.</p>

        <p>Stepping through its doors is like entering a living museum of Bavarian culture. The vaulted ceilings are painted with traditional motifs, the wooden tables bear the carved initials of generations of patrons, and the atmosphere buzzes with conversation, laughter, and the occasional burst of traditional music.</p>

        <p>The heart of the experience is, of course, the beer. Served in traditional one-liter steins by waitstaff in dirndls and lederhosen, the house brews follow the Reinheitsgebot (German Beer Purity Law) of 1516, using only water, barley, and hops. The flagship Hofbräu Original is a crisp, malty lager that pairs perfectly with the hearty Bavarian cuisine served from the kitchen.</p>

        <p>Speaking of food, the menu is a showcase of Bavarian classics: crispy pork knuckle (Schweinshaxe) with crackling skin, roast pork with dumplings and sauerkraut, and of course, an array of sausages. For the full experience, order the Brotzeitteller, a wooden board laden with cold cuts, cheeses, and bread – perfect for sharing.</p>

        <p>The ground floor tends to be bustling with tourists, but venture upstairs to find rooms where locals gather, some bringing their personal beer steins which they store in over 600 locked cabinets on-site. If you're lucky, you might witness impromptu performances by traditional Bavarian musicians or see regulars in full traditional dress.</p>

        <p>While it's certainly a tourist attraction, Hofbräuhaus remains an authentic piece of Munich's soul and a place where the city's beer-loving tradition is celebrated daily.</p>
      `,
      details: {
        location: "Platzl 9, 80331 Munich",
        openingHours: "9 a.m. to midnight daily",
        cost: "€€",
        bestTime: "Weekday afternoons for a more relaxed atmosphere",
      },
    },
    {
      title: "BMW Welt & Museum: A Temple to German Engineering",
      icon: faCamera,
      image: muenchen2,
      content: `
        <p>Even if you're not an automotive enthusiast, BMW Welt (World) and Museum offer a fascinating glimpse into German engineering excellence and design innovation. Located in Munich's Olympic Park area, this architectural marvel is both a showcase for BMW's latest models and a celebration of the brand's storied history.</p>

        <p>BMW Welt's building itself is worth the visit – a futuristic swirl of glass and steel designed by Coop Himmelb(l)au, with a double cone structure that seems to defy gravity. Inside, the vast, light-filled space displays the latest BMW, MINI, and Rolls-Royce models, which visitors can sit in and explore (but sadly not drive off in, unless you're actually taking delivery of your new BMW, which many customers do here).</p>

        <p>Interactive exhibits throughout BMW Welt demonstrate the technology behind the vehicles, from electric drivetrains to advanced driver assistance systems. The Junior Campus offers hands-on activities for younger visitors, making complex engineering concepts accessible and fun.</p>

        <p>Across a pedestrian bridge lies the BMW Museum, housed in a striking bowl-shaped building. Here, the company's 100+ year journey unfolds through a chronological display of historic vehicles, from early motorcycles to concept cars that offer glimpses of future mobility. The museum doesn't shy away from difficult periods in the company's history, including its role during World War II, providing a nuanced view of how BMW evolved alongside Germany itself.</p>

        <p>Highlights include the iconic BMW 507 roadster from the 1950s, the revolutionary BMW i8 hybrid sports car, and the Art Cars collection – vehicles transformed into rolling canvases by renowned artists like Andy Warhol, Roy Lichtenstein, and Jeff Koons.</p>

        <p>After exploring both facilities, stop by the premium restaurant "Bavarie" for upscale dining with views of the exhibition space, or grab a quick bite at one of the casual cafés.</p>
      `,
      details: {
        location: "Am Olympiapark 1, 80809 Munich",
        openingHours:
          "BMW Welt: Daily 7:30 a.m. to 8 p.m., Museum: Tuesday to Sunday 10 a.m. to 6 p.m.",
        cost: "BMW Welt: Free, Museum: €10 for adults",
        bestTime: "Weekday mornings",
      },
    },
    {
      title: "Nymphenburg Palace: Royal Splendor on the Outskirts",
      icon: faHeart,
      image: muenchen1,
      content: `
        <p>Just a short tram ride from Munich's center lies the magnificent Nymphenburg Palace (Schloss Nymphenburg), the summer residence of the Bavarian rulers for over 300 years. Approaching the palace along the central canal, you'll understand why it's considered one of Europe's most beautiful royal residences.</p>

        <p>The palace began as a modest Italian villa in 1664, a gift from Elector Ferdinand Maria to his wife after the birth of their son, but subsequent rulers expanded it into the grand baroque complex we see today. The central pavilion houses the Stone Hall (Steinerner Saal), a masterpiece of rococo design with ceiling frescoes depicting the Olympian gods and goddesses against a sky of brilliant blue.</p>

        <p>As you wander through the royal apartments, you'll encounter the Gallery of Beauties commissioned by King Ludwig I – a collection of 36 portraits of beautiful women from all social classes who caught the king's appreciative eye. Don't miss the room where King Ludwig II of Bavaria was born, the eccentric monarch who would later build Neuschwanstein Castle.</p>

        <p>The real magic of Nymphenburg lies in its 490-acre park, designed in both formal French and natural English landscape styles. Hidden throughout the grounds are architectural treasures like the Amalienburg, a hunting lodge that represents the pinnacle of European rococo design with its Hall of Mirrors reflecting the light from all directions.</p>

        <p>The Badenburg, another park pavilion, contains one of Europe's earliest heated indoor swimming pools, while the Pagodenburg blends Chinese and European elements in a whimsical tea house. Perhaps most curious is the Magdalenenklause, a deliberately constructed artificial ruin designed as a place for contemplation and religious devotion.</p>

        <p>In summer, rent a rowboat to glide along the palace's central canal, or visit in winter when snow blankets the grounds and fewer tourists allow for a more intimate experience of royal Bavaria.</p>
      `,
      details: {
        location: "Schloß Nymphenburg 1, 80638 Munich",
        openingHours:
          "April to October 9 a.m. to 6 p.m., November to March 10 a.m. to 4 p.m.",
        cost: "Palace: €8 for adults, Park: Free",
        bestTime:
          "Early morning or late afternoon for the best light for photography",
      },
    },
  ];

  return (
    <div className="relative w-full h-auto mx-auto" data-oid="u2vrcyt">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid="gucd3bp"
      >
        {/* City Header */}
        <motion.div variants={textVariant()} data-oid="vxqef0x">
          <p
            className={`${styles.sectionSubText} text-center text-[#D4AF37]`}
            data-oid="m8lijji"
          >
            {t("tourism-pages.city-template.subtitle")}
          </p>
          <h2
            className={`${styles.sectionHeadText} text-center`}
            data-oid="4-4g7:r"
          >
            {t("tourism-pages.popular-destinations.destinations.munich.name")}
          </h2>
        </motion.div>

        {/* Hero Image */}
        <div className="mt-10 relative" data-oid="z2h1r1z">
          <div
            className="w-full h-[500px] md:h-[600px] relative overflow-hidden rounded-xl border-2 border-[#D4AF37]"
            data-oid="p96ia_1"
          >
            <motion.img
              key={activeImage}
              src={cityImages[activeImage]}
              alt={`Munich - ${activeImage + 1}`}
              className="w-full h-full object-cover"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              data-oid="k7ec:0y"
            />

            {/* Navigation arrows */}
            {cityImages.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-[#D4AF37] p-3 rounded-full hover:bg-opacity-70 transition-all"
                  data-oid="r3j7qyl"
                >
                  <FontAwesomeIcon
                    icon={faChevronLeft}
                    size="lg"
                    data-oid="ve5ill6"
                  />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-[#D4AF37] p-3 rounded-full hover:bg-opacity-70 transition-all"
                  data-oid="77010kg"
                >
                  <FontAwesomeIcon
                    icon={faChevronRight}
                    size="lg"
                    data-oid="340u0.j"
                  />
                </button>
              </>
            )}

            {/* Image counter */}
            {cityImages.length > 1 && (
              <div
                className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full"
                data-oid="l2ogeyc"
              >
                {activeImage + 1} / {cityImages.length}
              </div>
            )}
          </div>
        </div>

        {/* City Introduction */}
        <motion.div
          variants={fadeIn("", "", 0.1, 1)}
          className="mt-10 bg-black bg-opacity-70 p-6 rounded-xl border border-[#D4AF37]"
          data-oid="yscg7:2"
        >
          <h3
            className="text-[#D4AF37] font-bold text-[24px] mb-4"
            data-oid="2crf5aq"
          >
            {t("tourism-pages.popular-destinations.destinations.munich.name")}:{" "}
            {t("tourism-pages.city-blog.introduction")}
          </h3>
          <p
            className="text-white text-[17px] leading-[30px] mb-4"
            style={{ direction: dir }}
            data-oid="_8meh.:"
          >
            {t(
              "tourism-pages.popular-destinations.destinations.munich.long-description",
            )}
          </p>
          <p
            className="text-white text-[17px] leading-[30px]"
            style={{ direction: dir }}
            data-oid="obq_uxo"
          >
            {t("tourism-pages.city-blog.munich-intro")}
          </p>
        </motion.div>

        {/* Blog Posts */}
        <div className="mt-10" data-oid="xy2hblq">
          <h2
            className={`${styles.sectionHeadText} text-center mb-10`}
            data-oid="z0ofv3k"
          >
            {t("tourism-pages.city-blog.best-places")}
          </h2>

          {blogPosts.map((post, index) => (
            <motion.div
              key={index}
              variants={fadeIn("up", "spring", index * 0.5, 0.75)}
              className="mb-16 bg-black bg-opacity-70 rounded-xl overflow-hidden border border-[#D4AF37]"
              data-oid="w-6qwqh"
            >
              <div
                className="w-full h-[300px] md:h-[400px] relative"
                data-oid="szfbd:f"
              >
                <img
                  src={post.image}
                  alt={post.title}
                  className="w-full h-full object-cover"
                  data-oid="gx8qopc"
                />

                <div
                  className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70"
                  data-oid="wlrksii"
                ></div>
                <div
                  className="absolute bottom-0 left-0 right-0 p-6"
                  data-oid="7d.s44x"
                >
                  <div className="flex items-center mb-2" data-oid="7xxo70r">
                    <div
                      className="w-10 h-10 rounded-full bg-[#D4AF37] flex items-center justify-center mr-3"
                      data-oid="f_oratg"
                    >
                      <FontAwesomeIcon
                        icon={post.icon}
                        className="text-black"
                        data-oid="t0oi-af"
                      />
                    </div>
                    <h3
                      className="text-[#D4AF37] font-bold text-[24px] md:text-[28px]"
                      data-oid="i:3nuf:"
                    >
                      {post.title}
                    </h3>
                  </div>
                </div>
              </div>

              <div className="p-6" data-oid="_w5col4">
                <div
                  className="text-white text-[16px] leading-[28px] mb-6"
                  style={{ direction: dir }}
                  dangerouslySetInnerHTML={{ __html: post.content }}
                  data-oid="e9fnzv9"
                />

                {/* Details Box */}
                <div
                  className="bg-[#111111] p-4 rounded-lg border border-[#333333]"
                  data-oid="qdrie7a"
                >
                  <h4
                    className="text-[#D4AF37] font-bold text-[18px] mb-3"
                    data-oid="w1q8561"
                  >
                    {t("tourism-pages.city-blog.visitor-info")}
                  </h4>
                  <div
                    className="grid grid-cols-1 md:grid-cols-2 gap-3"
                    data-oid="sqkkk9z"
                  >
                    <div className="flex items-start" data-oid="w2eu:_3">
                      <FontAwesomeIcon
                        icon={faMapMarkerAlt}
                        className="text-[#D4AF37] mt-1 mr-2"
                        data-oid="cr89cs."
                      />

                      <div data-oid="5y:-whu">
                        <span
                          className="text-[#D4AF37] font-semibold"
                          data-oid="zyx58ea"
                        >
                          {t("tourism-pages.city-blog.location")}:{" "}
                        </span>
                        <span className="text-white" data-oid="ng2-0f:">
                          {post.details.location}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-start" data-oid="pfhfczo">
                      <FontAwesomeIcon
                        icon={faClock}
                        className="text-[#D4AF37] mt-1 mr-2"
                        data-oid="lixz8oc"
                      />

                      <div data-oid="huphbco">
                        <span
                          className="text-[#D4AF37] font-semibold"
                          data-oid="_.rgdpp"
                        >
                          {t("tourism-pages.city-blog.hours")}:{" "}
                        </span>
                        <span className="text-white" data-oid="foayn6z">
                          {post.details.openingHours}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-start" data-oid="os_8.2v">
                      <FontAwesomeIcon
                        icon={faEuroSign}
                        className="text-[#D4AF37] mt-1 mr-2"
                        data-oid="69-:4h3"
                      />

                      <div data-oid="pe:2-fy">
                        <span
                          className="text-[#D4AF37] font-semibold"
                          data-oid="z3mfayb"
                        >
                          {t("tourism-pages.city-blog.cost")}:{" "}
                        </span>
                        <span className="text-white" data-oid="gjfboeo">
                          {post.details.cost}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-start" data-oid="6o_vct.">
                      <FontAwesomeIcon
                        icon={faClock}
                        className="text-[#D4AF37] mt-1 mr-2"
                        data-oid="0bzx9rd"
                      />

                      <div data-oid="xtbnnhk">
                        <span
                          className="text-[#D4AF37] font-semibold"
                          data-oid="elns-8_"
                        >
                          {t("tourism-pages.city-blog.best-time")}:{" "}
                        </span>
                        <span className="text-white" data-oid="1j1kz0r">
                          {post.details.bestTime}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Instagram Section */}
        <div className="mt-16" data-oid="_ivh54m">
          <h2
            className={`${styles.sectionHeadText} text-center mb-10`}
            data-oid="hcr:x:w"
          >
            <FontAwesomeIcon
              icon={faInstagram}
              className="mr-3"
              data-oid="::.eyhh"
            />

            {t("tourism-pages.city-blog.instagram-title")}
          </h2>

          <div className="flex flex-col items-center mb-6" data-oid=".qg.hac">
            <div
              className="bg-black bg-opacity-70 p-4 rounded-xl border border-[#D4AF37] mb-6 max-w-3xl"
              data-oid="-vm_80g"
            >
              <p
                className="text-white text-center text-[17px] leading-[28px]"
                style={{ direction: dir }}
                data-oid="ar-:h9y"
              >
                {t("tourism-pages.city-blog.instagram-description")}
              </p>
              <div
                className="flex flex-wrap justify-center gap-2 mt-4"
                data-oid="23qasvh"
              >
                <span
                  className="bg-[#111] text-[#D4AF37] px-3 py-1 rounded-full text-sm"
                  data-oid="e5j0w3o"
                >
                  <FontAwesomeIcon
                    icon={faHashtag}
                    className="mr-1"
                    data-oid="j9osse2"
                  />
                  VisitMunich
                </span>
                <span
                  className="bg-[#111] text-[#D4AF37] px-3 py-1 rounded-full text-sm"
                  data-oid="yf9fvr0"
                >
                  <FontAwesomeIcon
                    icon={faHashtag}
                    className="mr-1"
                    data-oid="hng36.t"
                  />
                  München
                </span>
                <span
                  className="bg-[#111] text-[#D4AF37] px-3 py-1 rounded-full text-sm"
                  data-oid="zt_8i07"
                >
                  <FontAwesomeIcon
                    icon={faHashtag}
                    className="mr-1"
                    data-oid="w948o55"
                  />
                  MunichLife
                </span>
                <span
                  className="bg-[#111] text-[#D4AF37] px-3 py-1 rounded-full text-sm"
                  data-oid="8w_ya7b"
                >
                  <FontAwesomeIcon
                    icon={faHashtag}
                    className="mr-1"
                    data-oid="0c6p74:"
                  />
                  Bavaria
                </span>
              </div>
            </div>
          </div>

          <div
            className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10"
            data-oid="d9i.y9r"
          >
            <div className="flex justify-center" data-oid="1oqeiw9">
              <div
                style={{ width: "100%", maxWidth: "550px" }}
                data-oid="oh.4o9q"
              >
                <InstagramEmbed
                  url="https://www.instagram.com/p/CzXQJQNSIRQl/"
                  width="100%"
                  data-oid="ihqghwl"
                />
              </div>
            </div>
            <div className="flex justify-center" data-oid="psdp842">
              <div
                style={{ width: "100%", maxWidth: "550px" }}
                data-oid="xo3wc0k"
              >
                <InstagramEmbed
                  url="https://www.instagram.com/p/CzXKKXWIJJA/"
                  width="100%"
                  data-oid="2bylhli"
                />
              </div>
            </div>
            <div className="flex justify-center" data-oid="qh5m3r-">
              <div
                style={{ width: "100%", maxWidth: "550px" }}
                data-oid="scwq:ol"
              >
                <InstagramEmbed
                  url="https://www.instagram.com/p/CzXXXXXoXXX/"
                  width="100%"
                  data-oid="x6nmfl4"
                />
              </div>
            </div>
            <div className="flex justify-center" data-oid="ua94xbi">
              <div
                style={{ width: "100%", maxWidth: "550px" }}
                data-oid="7tvjmn."
              >
                <InstagramEmbed
                  url="https://www.instagram.com/p/CzXXXXXoXXX/"
                  width="100%"
                  data-oid="s713:bq"
                />
              </div>
            </div>
          </div>

          {/* Fallback message in case Instagram embeds don't load */}
          <div
            className="bg-black bg-opacity-70 p-4 rounded-xl border border-[#D4AF37] mb-6 max-w-3xl mx-auto"
            data-oid="flpocio"
          >
            <p
              className="text-white text-center text-[17px] leading-[28px]"
              data-oid="0kk_gwo"
            >
              Instagram posts may take a moment to load. If they don't appear,
              please check your internet connection or visit{" "}
              <a
                href="https://www.instagram.com/simply.munich/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-[#D4AF37] underline"
                data-oid="iyh.mrd"
              >
                @simply.munich
              </a>{" "}
              directly.
            </p>
          </div>

          <div className="flex justify-center mb-10" data-oid="xf9cvm-">
            <a
              href="https://www.instagram.com/simply.munich/"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-gradient-to-r from-[#833AB4] via-[#FD1D1D] to-[#FCAF45] text-white py-3 px-8 rounded-xl font-bold hover:opacity-90 transition-all duration-300 flex items-center"
              data-oid="tmbo9a:"
            >
              <FontAwesomeIcon
                icon={faInstagram}
                className="mr-2"
                data-oid="7_cmopi"
              />

              {t("tourism-pages.city-blog.follow-instagram")}
            </a>
          </div>
        </div>

        {/* Call to Action */}
        <div
          className="mt-10 bg-black rounded-2xl p-8 border border-[#D4AF37]"
          data-oid="lm46dyn"
        >
          <h3
            className="text-[#D4AF37] font-bold text-[24px] text-center"
            data-oid="vhb.nt4"
          >
            {t("tourism-pages.city-template.book-tour")}
          </h3>
          <p
            className="mt-4 text-white text-[17px] max-w-3xl leading-[30px] text-center mx-auto"
            style={{ direction: dir }}
            data-oid="5s08vvc"
          >
            {t("tourism-pages.city-template.book-tour-description")}
          </p>
          <div className="mt-5 flex justify-center" data-oid="k4rs-97">
            <Link
              to="/contact"
              className="bg-black py-3 px-8 rounded-xl outline-none w-fit text-[#D4AF37] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
              data-oid="s50lras"
            >
              {t("common.bookTour")}
            </Link>
          </div>
        </div>
      </div>
      <StarsCanvas data-oid="a08j51o" />
    </div>
  );
};

export default MunichBlog;
