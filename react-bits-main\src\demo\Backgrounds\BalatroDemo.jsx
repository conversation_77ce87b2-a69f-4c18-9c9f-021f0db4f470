import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import {
  Box,
  Flex,
  Image,
  Input,
  Slider,
  SliderFilledTrack,
  SliderThumb,
  SliderTrack,
  Switch,
  Text,
} from "@chakra-ui/react";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";

import Balatro from "../../content/Backgrounds/Balatro/Balatro";
import { balatro } from "../../constants/code/Backgrounds/balatroCode";

const BalatroDemo = () => {
  const [hideimage, setHideImage] = useState(false);
  const [color1, setColor1] = useState("#DE443B");
  const [color2, setColor2] = useState("#006BB4");
  const [color3, setColor3] = useState("#162325");
  const [rotate, setRotate] = useState(false);
  const [mouseInteraction, setMouseInteraction] = useState(true);
  const [pixelFilter, setPixelFilter] = useState(745.0);

  const propData = [
    {
      name: "spinRotation",
      type: "number",
      default: "-2.0",
      description: "Base rotation amount affecting the shader effect.",
    },
    {
      name: "spinSpeed",
      type: "number",
      default: "7.0",
      description: "Speed of the spin animation.",
    },
    {
      name: "offset",
      type: "[number, number]",
      default: "[0.0, 0.0]",
      description: "Offset for the shader effect.",
    },
    {
      name: "color1",
      type: "string",
      default: '"#DE443B"',
      description: "Primary color in HEX format.",
    },
    {
      name: "color2",
      type: "string",
      default: '"#006BB4"',
      description: "Secondary color in HEX format.",
    },
    {
      name: "color3",
      type: "string",
      default: '"#162325"',
      description: "Tertiary color in HEX format.",
    },
    {
      name: "contrast",
      type: "number",
      default: "3.5",
      description: "Contrast value affecting color blending.",
    },
    {
      name: "lighting",
      type: "number",
      default: "0.4",
      description: "Lighting factor affecting brightness.",
    },
    {
      name: "spinAmount",
      type: "number",
      default: "0.25",
      description: "Amount of spin influence based on UV length.",
    },
    {
      name: "pixelFilter",
      type: "number",
      default: "745.0",
      description: "Pixel filter factor determining pixelation.",
    },
    {
      name: "spinEase",
      type: "number",
      default: "1.0",
      description: "Ease factor for spin.",
    },
    {
      name: "isRotate",
      type: "boolean",
      default: "false",
      description: "Determines if the shader rotates continuously.",
    },
    {
      name: "mouseInteraction",
      type: "boolean",
      default: "true",
      description: "Enables or disables mouse interaction for rotation.",
    },
  ];

  return (
    <TabbedLayout data-oid="hd:ccsg">
      <PreviewTab data-oid="zn.n3s2">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          overflow="hidden"
          p={0}
          data-oid="mx6l.-d"
        >
          <Balatro
            color1={color1}
            color2={color2}
            color3={color3}
            isRotate={rotate}
            mouseInteraction={mouseInteraction}
            pixelFilter={pixelFilter}
            data-oid="mpjy93z"
          />

          {!hideimage && (
            <Image
              pointerEvents="none"
              position="absolute"
              w={200}
              src="https://oyster.ignimgs.com/mediawiki/apis.ign.com/balatro/e/ef/Joker.png"
              borderRadius="10px"
              data-oid="a96g0yh"
            />
          )}
        </Box>

        <Flex
          gap={4}
          align="center"
          mt={7}
          justifyContent="flex-end"
          position="absolute"
          right={0}
          data-oid="0k-les."
        >
          <Text fontSize="sm" data-oid="8cfe:y4">
            Hide Image
          </Text>
          <Switch
            isChecked={hideimage}
            onChange={(e) => {
              setHideImage(e.target.checked);
            }}
            data-oid="2_vorgw"
          />
        </Flex>

        <div className="preview-options" data-oid="6dblmn9">
          <h2 className="demo-title-extra" data-oid="webphzh">
            Customize
          </h2>

          <Flex gap={4} align="center" mt={4} data-oid="sl:.wmb">
            <Text fontSize="sm" data-oid="wezndyp">
              Colors
            </Text>
            <Input
              type="color"
              value={color1}
              onChange={(e) => {
                setColor1(e.target.value);
              }}
              width="50px"
              data-oid="9yh6plg"
            />

            <Input
              type="color"
              value={color3}
              onChange={(e) => {
                setColor3(e.target.value);
              }}
              width="50px"
              data-oid="g3h3acy"
            />

            <Input
              type="color"
              value={color2}
              onChange={(e) => {
                setColor2(e.target.value);
              }}
              width="50px"
              data-oid="34bldnu"
            />
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="6sp7teo">
            <Text fontSize="sm" data-oid="djt.u6p">
              Pixelate
            </Text>
            <Slider
              min={0}
              max={2000}
              step={10}
              value={pixelFilter}
              onChange={(val) => {
                setPixelFilter(val);
              }}
              width="150px"
              data-oid="p57g1_m"
            >
              <SliderTrack data-oid="814xfei">
                <SliderFilledTrack data-oid="ghr:x.d" />
              </SliderTrack>
              <SliderThumb data-oid="ynb5acm" />
            </Slider>
            <Text fontSize="sm" data-oid="bkrpxs6">
              {pixelFilter}
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="gwhcm8h">
            <Text fontSize="sm" data-oid="3rbkxa9">
              Mouse Interaction
            </Text>
            <Switch
              isChecked={mouseInteraction}
              onChange={(e) => {
                setMouseInteraction(e.target.checked);
              }}
              data-oid="80vg8v_"
            />
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="ge0y4j1">
            <Text fontSize="sm" data-oid="7.fue28">
              Rotate
            </Text>
            <Switch
              isChecked={rotate}
              onChange={(e) => {
                setRotate(e.target.checked);
              }}
              data-oid=":0ovyhk"
            />
          </Flex>
        </div>

        <PropTable data={propData} data-oid="6z:17_b" />
        <Dependencies dependencyList={["ogl"]} data-oid="nh.s7b4" />
      </PreviewTab>

      <CodeTab data-oid="q4jo0dg">
        <CodeExample codeObject={balatro} data-oid=":1lzywl" />
      </CodeTab>

      <CliTab data-oid="-7v_bin">
        <CliInstallation {...balatro} data-oid="3hvmh8-" />
      </CliTab>
    </TabbedLayout>
  );
};

export default BalatroDemo;
