import { useState } from "react";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { ArrowDownIcon, ArrowUpIcon, InfoOutlineIcon } from "@chakra-ui/icons";
import { Box, Button, ButtonGroup, Flex, Input } from "@chakra-ui/react";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";

import Squares from "../../content/Backgrounds/Squares/Squares";
import { squares } from "../../constants/code/Backgrounds/squaresCode";

const SquaresDemo = () => {
  const [direction, setDirection] = useState("diagonal");
  const [borderColor, setBorderColor] = useState("#222");
  const [hoverColor, setHoverColor] = useState("#222222");
  const [speed, setSpeed] = useState(0.5);
  const [size, setSize] = useState(40);

  return (
    <TabbedLayout data-oid="j17fes8">
      <PreviewTab data-oid="8wc2m1_">
        <Box
          direction="relative"
          minH={200}
          className="demo-container"
          overflow="hidden"
          data-oid="_y53j7d"
        >
          <Box
            w={"100%"}
            h={500}
            border={"1px solid #222"}
            borderRadius={"10px"}
            overflow="hidden"
            data-oid="c_gsgj0"
          >
            <Squares
              squareSize={size}
              speed={speed}
              direction={direction}
              borderColor={borderColor}
              hoverFillColor={hoverColor}
              data-oid="omuwj0p"
            />
          </Box>
        </Box>

        <div className="preview-options" data-oid="-o_7egw">
          <h2 className="demo-title-extra" data-oid="unzm5xx">
            Customize
          </h2>
          <Flex gap={6} direction="column" data-oid="9.10sgu">
            <ButtonGroup isAttached size="sm" data-oid=".a._9..">
              <Button
                fontSize="xs"
                h={8}
                bg="#a1a1aa"
                isDisabled
                _disabled={{
                  bg: "#222",
                  cursor: "not-allowed",
                  _hover: { bg: "#222" },
                }}
                data-oid="9azxd.f"
              >
                Direction
              </Button>
              <Button
                bg={direction === "diagonal" ? "#00f0ff" : "#111"}
                _hover={{
                  backgroundColor: `${direction === "diagonal" ? "#00f0ff" : "#111"}`,
                }}
                color={direction === "diagonal" ? "black" : "white"}
                fontSize="xs"
                h={8}
                onClick={() => {
                  setDirection("diagonal");
                }}
                data-oid="t--kepy"
              >
                Diagonal
              </Button>
              <Button
                bg={direction === "up" ? "#00f0ff" : "#111"}
                _hover={{
                  backgroundColor: `${direction === "up" ? "#00f0ff" : "#111"}`,
                }}
                color={direction === "up" ? "black" : "white"}
                fontSize="xs"
                h={8}
                onClick={() => {
                  setDirection("up");
                }}
                data-oid="f4n:9nk"
              >
                Up
              </Button>
              <Button
                bg={direction === "right" ? "#00f0ff" : "#111"}
                _hover={{
                  backgroundColor: `${direction === "right" ? "#00f0ff" : "#111"}`,
                }}
                color={direction === "right" ? "black" : "white"}
                fontSize="xs"
                h={8}
                onClick={() => {
                  setDirection("right");
                }}
                data-oid="jauufym"
              >
                Right
              </Button>
              <Button
                bg={direction === "down" ? "#00f0ff" : "#111"}
                _hover={{
                  backgroundColor: `${direction === "down" ? "#00f0ff" : "#111"}`,
                }}
                color={direction === "down" ? "black" : "white"}
                fontSize="xs"
                h={8}
                onClick={() => {
                  setDirection("down");
                }}
                data-oid="6pxdmxu"
              >
                Down
              </Button>
              <Button
                bg={direction === "left" ? "#00f0ff" : "#111"}
                _hover={{
                  backgroundColor: `${direction === "left" ? "#00f0ff" : "#111"}`,
                }}
                color={direction === "left" ? "black" : "white"}
                fontSize="xs"
                h={8}
                onClick={() => {
                  setDirection("left");
                }}
                data-oid="depygrx"
              >
                Left
              </Button>
            </ButtonGroup>

            <Flex alignItems="center" data-oid="o0f24-j">
              Border Color:&nbsp;&nbsp;
              <input
                type="color"
                value={borderColor}
                style={{ height: "22px", outline: "none", border: "none" }}
                onChange={(e) => setBorderColor(e.target.value)}
                data-oid="j-i6uoj"
              />
            </Flex>

            <Flex alignItems="center" data-oid="q6:.ur6">
              Hover Color:&nbsp;&nbsp;
              <input
                type="color"
                value={hoverColor}
                style={{ height: "22px", outline: "none", border: "none" }}
                onChange={(e) => setHoverColor(e.target.value)}
                data-oid="k0hgv7x"
              />
            </Flex>

            <Flex gap={2} alignItems="center" data-oid="ufvz:5e">
              <Button
                w={4}
                h={8}
                onClick={() => {
                  if (size === 100) return;
                  setSize(size + 1);
                }}
                data-oid="af3r0u5"
              >
                <ArrowUpIcon data-oid="5dd_y9p" />
              </Button>
              Size: {size}px
              <Button
                w={4}
                h={8}
                onClick={() => {
                  if (size === 10) return;
                  setSize(size - 1);
                }}
                data-oid="kg4e9dl"
              >
                <ArrowDownIcon data-oid="0ub.69q" />
              </Button>
            </Flex>

            <Flex gap={2} alignItems="center" data-oid="80oj2u1">
              Speed &nbsp;-{" "}
              <Input
                type="tel"
                w={50}
                h={8}
                px={2}
                onChange={(e) => {
                  setSpeed(e.target.value);
                }}
                maxLength={3}
                value={speed}
                data-oid="nry._ub"
              />
            </Flex>
          </Flex>
        </div>
        <p className="demo-extra-info" data-oid="b3-dae3">
          <InfoOutlineIcon position="relative" data-oid="w3s76af" />
          The ideal speed for the animation is between 0.5 - 1.
        </p>
      </PreviewTab>

      <CodeTab data-oid="jj4jyp8">
        <CodeExample codeObject={squares} data-oid=":sj3hd." />
      </CodeTab>

      <CliTab data-oid="ihif3ci">
        <CliInstallation {...squares} data-oid="jc:rpcw" />
      </CliTab>
    </TabbedLayout>
  );
};

export default SquaresDemo;
