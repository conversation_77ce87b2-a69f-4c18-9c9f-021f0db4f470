import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { styles } from "../../styles";
import { fadeIn, textVariant } from "../../utils/motion";
import { StarsCanvas } from "../../components/canvas";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../../contexts/LanguageContext";
import {
  disneylandParis,
  disneylandParis1,
  europaPark,
  europaPark1,
  phantasialand,
  phantasialand1,
  heidePark,
  heidePark1,
  legoland,
  legoland1,
  moviePark,
  moviePark1,
} from "../../assets";

// Define the theme parks using translations
const getThemeParks = (t) => [
  {
    name: t(`tourism-pages.theme-parks.parks.disneyland.name`),
    description: t(`tourism-pages.theme-parks.parks.disneyland.description`),
    image: disneylandParis,
    gallery: [disneylandParis1],
  },
  {
    name: t(`tourism-pages.theme-parks.parks.europa-park.name`),
    description: t(`tourism-pages.theme-parks.parks.europa-park.description`),
    image: europaPark,
    gallery: [europaPark1],
  },
  {
    name: t(`tourism-pages.theme-parks.parks.phantasialand.name`),
    description: t(`tourism-pages.theme-parks.parks.phantasialand.description`),
    image: phantasialand,
    gallery: [phantasialand1],
  },
  {
    name: t(`tourism-pages.theme-parks.parks.heide-park.name`),
    description: t(`tourism-pages.theme-parks.parks.heide-park.description`),
    image: heidePark,
    gallery: [heidePark1],
  },
  {
    name: t(`tourism-pages.theme-parks.parks.legoland.name`),
    description: t(`tourism-pages.theme-parks.parks.legoland.description`),
    image: legoland,
    gallery: [legoland1],
  },
  {
    name: t(`tourism-pages.theme-parks.parks.movie-park.name`),
    description: t(`tourism-pages.theme-parks.parks.movie-park.description`),
    image: moviePark,
    gallery: [moviePark1],
  },
];

const ThemeParkCard = ({ name, description, image, index }) => {
  const { t } = useTranslation();
  const { dir } = useLanguage();

  return (
    <motion.div
      variants={fadeIn("up", "spring", index * 0.5, 0.75)}
      className="bg-black p-5 rounded-2xl sm:w-[360px] w-full border border-[#D4AF37]"
      data-oid="y.a00je"
    >
      <div className="relative w-full h-[230px]" data-oid="-ugqeu_">
        <img
          src={image}
          alt={name}
          className="w-full h-full object-cover rounded-2xl border border-[#D4AF37]"
          data-oid="ltbnflv"
        />
      </div>

      <div
        className="mt-5"
        style={{
          textAlign: dir === "rtl" ? "right" : "left",
          minHeight: "120px",
        }}
        data-oid="twbtsfy"
      >
        <h3 className="text-[#D4AF37] font-bold text-[24px]" data-oid="1myw7xh">
          {name}
        </h3>
        <p className="mt-2 text-white text-[14px]" data-oid="5xkw-tg">
          {description}
        </p>
      </div>

      <div className="mt-4 flex justify-center" data-oid="1db.dpg">
        <Link
          to="/contact"
          className="bg-black py-2 px-4 rounded-xl outline-none w-fit text-[#D4AF37] text-[14px] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
          data-oid="bdtjk4c"
        >
          {t("common.bookTour")}
        </Link>
      </div>
    </motion.div>
  );
};

const Freizeitparks = () => {
  const { t } = useTranslation();
  const { dir } = useLanguage();

  // Get the theme parks with translations
  const themeparks = getThemeParks(t);

  return (
    <div className="relative w-full h-auto mx-auto" data-oid="93u2ru3">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid="qb4.h37"
      >
        <motion.div variants={textVariant()} data-oid="_-ztbzf">
          <p
            className={`${styles.sectionSubText} text-center text-[#D4AF37]`}
            data-oid="tik:ndt"
          >
            {t("tourism-pages.theme-parks.subtitle")}
          </p>
          <h2
            className={`${styles.sectionHeadText} text-center`}
            data-oid="80drjni"
          >
            {t("tourism-pages.theme-parks.title")}
          </h2>
        </motion.div>

        <motion.p
          variants={fadeIn("", "", 0.1, 1)}
          className="mt-4 text-white text-[17px] max-w-3xl leading-[30px] text-center mx-auto"
          style={{ direction: dir }}
          data-oid="9ieoe3."
        >
          {t("tourism-pages.theme-parks.description")}
        </motion.p>

        <div
          className="mt-20 flex flex-wrap gap-7 justify-center"
          data-oid="_kz_izf"
        >
          {themeparks.map((park, index) => (
            <ThemeParkCard
              key={`park-${index}`}
              index={index}
              {...park}
              data-oid=".978:yk"
            />
          ))}
        </div>

        <div
          className="mt-10 bg-black rounded-2xl p-8 border border-[#D4AF37]"
          data-oid="lvcn-er"
        >
          <h3
            className="text-[#D4AF37] font-bold text-[24px] text-center"
            data-oid="_ruqlhe"
          >
            {t("tourism-pages.theme-parks.custom-tours")}
          </h3>
          <p
            className="mt-4 text-white text-[17px] max-w-3xl leading-[30px] text-center mx-auto"
            style={{ direction: dir }}
            data-oid="p1k:cg2"
          >
            {t("tourism-pages.theme-parks.custom-tours-description")}
          </p>
          <div className="mt-5 flex justify-center" data-oid="4n_9unv">
            <Link
              to="/contact"
              className="bg-black py-3 px-8 rounded-xl outline-none w-fit text-[#D4AF37] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
              data-oid="kb07txy"
            >
              {t("common.planCustomTour")}
            </Link>
          </div>
        </div>
      </div>
      <StarsCanvas data-oid="ahpcypn" />
    </div>
  );
};

export default Freizeitparks;
