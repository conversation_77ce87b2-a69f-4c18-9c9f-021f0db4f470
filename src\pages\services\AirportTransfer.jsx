import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { styles } from "../../styles";
import { fadeIn, textVariant } from "../../utils/motion";
// import { StarsCanvas } from '../../components/canvas';
import { airport1, airport2, airport3 } from "../../assets";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../../contexts/LanguageContext";

// Error boundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo,
    });
    console.error("Error caught by ErrorBoundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div
          className="error-boundary p-4 bg-black border border-red-500 rounded-lg"
          data-oid="c9ykbmv"
        >
          <h2 className="text-red-500 text-xl mb-2" data-oid="t0a4mbz">
            Something went wrong
          </h2>
          <p className="text-white mb-4" data-oid="wi-ci1z">
            Please try refreshing the page or contact support if the issue
            persists.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
            data-oid="0-am1g9"
          >
            Refresh Page
          </button>
        </div>
      );
    }
    return this.props.children;
  }
}

const AirportTransferContent = () => {
  const { t } = useTranslation();
  const { dir } = useLanguage();

  return (
    <div className="relative w-full h-auto mx-auto" data-oid="6cxt-9m">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid="dse-rsn"
      >
        <motion.div variants={textVariant()} data-oid="rhkgv8q">
          <p
            className={`${styles.sectionSubText} text-center text-[#D4AF37]`}
            data-oid="i_odqla"
          >
            {t("service-pages.common.our-services")}
          </p>
          <h2
            className={`${styles.sectionHeadText} text-center`}
            data-oid="-cgboy."
          >
            {t("service-pages.airport-transfer.title")}
          </h2>
        </motion.div>

        <motion.p
          variants={fadeIn("", "", 0.1, 1)}
          className="mt-4 text-white text-[17px] max-w-3xl leading-[30px] text-center mx-auto"
          style={{ direction: dir }}
          data-oid="xc5zhsy"
        >
          {t("service-pages.airport-transfer.description")}
        </motion.p>

        <div
          className="mt-10 bg-black rounded-2xl p-8 border border-[#D4AF37]"
          data-oid="tszqxkl"
        >
          <h3
            className="text-[#D4AF37] font-bold text-[24px] text-center"
            style={{ direction: dir }}
            data-oid="i2c1n32"
          >
            {t("service-pages.common.service-features")}
          </h3>
          <ul
            className="mt-5 list-disc ml-5 space-y-2"
            style={{
              direction: dir,
              textAlign: dir === "rtl" ? "right" : "left",
            }}
            data-oid="hshyzl."
          >
            {Array.isArray(t("service-pages.airport-transfer.features")) ? (
              t("service-pages.airport-transfer.features").map(
                (feature, index) => (
                  <li
                    key={index}
                    className="text-white text-[17px] pl-1 tracking-wider flex items-center"
                    data-oid="flm.w34"
                  >
                    <span
                      className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-[#D4AF37]`}
                      data-oid="kvekxae"
                    >
                      •
                    </span>{" "}
                    {feature}
                  </li>
                ),
              )
            ) : (
              <li
                className="text-white text-[17px] pl-1 tracking-wider"
                data-oid="70cspx8"
              >
                <span
                  className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-[#D4AF37]`}
                  data-oid="qt4dy5."
                >
                  •
                </span>{" "}
                Loading features...
              </li>
            )}
          </ul>
        </div>

        <div className="mt-20" data-oid="5o2jbwu">
          <h3
            className="text-[#D4AF37] font-bold text-[24px] mb-6 text-center"
            style={{ direction: dir }}
            data-oid="v00s0xk"
          >
            {t("service-pages.airport-transfer.services-title")}
          </h3>
          <div
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
            data-oid=":o9lp8_"
          >
            <motion.div
              variants={fadeIn("right", "spring", 0.3, 0.75)}
              className="rounded-lg overflow-hidden shadow-lg border border-[#D4AF37]"
              data-oid="0coyipb"
            >
              <img
                src={airport1}
                alt={t("service-pages.airport-transfer.meet-greet")}
                className="w-full h-64 object-cover"
                data-oid="826p48x"
              />

              <div className="p-4 bg-black" data-oid="m6kk1u9">
                <h4
                  className="text-[#D4AF37] font-semibold text-[18px]"
                  style={{
                    direction: dir,
                    textAlign: dir === "rtl" ? "right" : "left",
                  }}
                  data-oid="rd_4l25"
                >
                  {t("service-pages.airport-transfer.meet-greet")}
                </h4>
                <p
                  className="text-white text-[14px] mt-2"
                  style={{
                    direction: dir,
                    textAlign: dir === "rtl" ? "right" : "left",
                  }}
                  data-oid="k17q-o:"
                >
                  {t("service-pages.airport-transfer.meet-greet-desc")}
                </p>
              </div>
            </motion.div>

            <motion.div
              variants={fadeIn("up", "spring", 0.4, 0.75)}
              className="rounded-lg overflow-hidden shadow-lg border border-[#D4AF37]"
              data-oid="g7wdxaw"
            >
              <img
                src={airport2}
                alt={t("service-pages.airport-transfer.luxury-vehicles")}
                className="w-full h-64 object-cover"
                data-oid="kqm1nnz"
              />

              <div className="p-4 bg-black" data-oid="enjsz.3">
                <h4
                  className="text-[#D4AF37] font-semibold text-[18px]"
                  style={{
                    direction: dir,
                    textAlign: dir === "rtl" ? "right" : "left",
                  }}
                  data-oid="p26hs29"
                >
                  {t("service-pages.airport-transfer.luxury-vehicles")}
                </h4>
                <p
                  className="text-white text-[14px] mt-2"
                  style={{
                    direction: dir,
                    textAlign: dir === "rtl" ? "right" : "left",
                  }}
                  data-oid="z4yr_9s"
                >
                  {t("service-pages.airport-transfer.luxury-vehicles-desc")}
                </p>
              </div>
            </motion.div>

            <motion.div
              variants={fadeIn("left", "spring", 0.5, 0.75)}
              className="rounded-lg overflow-hidden shadow-lg border border-[#D4AF37]"
              data-oid="0.j6itt"
            >
              <img
                src={airport3}
                alt={t("service-pages.airport-transfer.vip-terminal")}
                className="w-full h-64 object-cover"
                data-oid="v5dfnk."
              />

              <div className="p-4 bg-black" data-oid="bqip2x9">
                <h4
                  className="text-[#D4AF37] font-semibold text-[18px]"
                  style={{
                    direction: dir,
                    textAlign: dir === "rtl" ? "right" : "left",
                  }}
                  data-oid="i7nrsg9"
                >
                  {t("service-pages.airport-transfer.vip-terminal")}
                </h4>
                <p
                  className="text-white text-[14px] mt-2"
                  style={{
                    direction: dir,
                    textAlign: dir === "rtl" ? "right" : "left",
                  }}
                  data-oid="cm2ggx9"
                >
                  {t("service-pages.airport-transfer.vip-terminal-desc")}
                </p>
              </div>
            </motion.div>
          </div>
        </div>

        <div
          className="mt-10 bg-black rounded-2xl p-8 border border-[#D4AF37]"
          data-oid="r-lju2k"
        >
          <h3
            className="text-[#D4AF37] font-bold text-[24px] text-center"
            style={{ direction: dir }}
            data-oid="hzsimfg"
          >
            {t("service-pages.airport-transfer.airports-title")}
          </h3>
          <div
            className="mt-5 grid grid-cols-1 md:grid-cols-2 gap-10"
            data-oid="tweslsl"
          >
            <div
              className="bg-black p-5 rounded-2xl border border-[#D4AF37]"
              data-oid="7hj:krn"
            >
              <h4
                className="text-[#D4AF37] font-semibold text-[20px] text-center"
                style={{ direction: dir }}
                data-oid="fjn0d_7"
              >
                {t("service-pages.airport-transfer.major-airports")}
              </h4>
              <ul
                className="mt-2 space-y-2"
                style={{
                  direction: dir,
                  textAlign: dir === "rtl" ? "right" : "left",
                }}
                data-oid="g6zbfwm"
              >
                {Array.isArray(
                  t("service-pages.airport-transfer.major-airports-list"),
                ) ? (
                  t("service-pages.airport-transfer.major-airports-list").map(
                    (airport, index) => (
                      <li
                        key={index}
                        className="text-white text-[16px] flex items-center"
                        data-oid="bk4:y1u"
                      >
                        <span
                          className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-[#D4AF37]`}
                          data-oid="wyq-b--"
                        >
                          •
                        </span>{" "}
                        {airport}
                      </li>
                    ),
                  )
                ) : (
                  <li
                    className="text-white text-[16px] flex items-center"
                    data-oid="-5f4.gj"
                  >
                    <span
                      className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-[#D4AF37]`}
                      data-oid="q..5v_5"
                    >
                      •
                    </span>{" "}
                    Loading airports...
                  </li>
                )}
              </ul>
            </div>
            <div
              className="bg-black p-5 rounded-2xl border border-[#D4AF37]"
              data-oid="huk1.0x"
            >
              <h4
                className="text-[#D4AF37] font-semibold text-[20px] text-center"
                style={{ direction: dir }}
                data-oid="_vmjuv0"
              >
                {t("service-pages.airport-transfer.regional-airports")}
              </h4>
              <ul
                className="mt-2 space-y-2"
                style={{
                  direction: dir,
                  textAlign: dir === "rtl" ? "right" : "left",
                }}
                data-oid="mgyg.:3"
              >
                {Array.isArray(
                  t("service-pages.airport-transfer.regional-airports-list"),
                ) ? (
                  t(
                    "service-pages.airport-transfer.regional-airports-list",
                  ).map((airport, index) => (
                    <li
                      key={index}
                      className="text-white text-[16px] flex items-center"
                      data-oid="xcigb54"
                    >
                      <span
                        className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-[#D4AF37]`}
                        data-oid="59180m9"
                      >
                        •
                      </span>{" "}
                      {airport}
                    </li>
                  ))
                ) : (
                  <li
                    className="text-white text-[16px] flex items-center"
                    data-oid="bnaxq_u"
                  >
                    <span
                      className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-[#D4AF37]`}
                      data-oid="_0.kad3"
                    >
                      •
                    </span>{" "}
                    Loading regional airports...
                  </li>
                )}
              </ul>
            </div>
          </div>
        </div>

        <div
          className="mt-10 bg-black rounded-2xl p-8 border border-[#D4AF37]"
          data-oid="c_db8xd"
        >
          <h3
            className="text-[#D4AF37] font-bold text-[24px] text-center"
            style={{ direction: dir }}
            data-oid="rttfkc8"
          >
            {t("service-pages.airport-transfer.booking-process")}
          </h3>
          <ol
            className="mt-5 list-decimal ml-5 space-y-2"
            style={{
              direction: dir,
              textAlign: dir === "rtl" ? "right" : "left",
            }}
            data-oid="giynau."
          >
            {Array.isArray(
              t("service-pages.airport-transfer.booking-steps"),
            ) ? (
              t("service-pages.airport-transfer.booking-steps").map(
                (step, index) => (
                  <li
                    key={index}
                    className="text-white text-[17px] pl-1 tracking-wider flex items-center"
                    data-oid="qvoiuc9"
                  >
                    <span
                      className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-[#D4AF37]`}
                      data-oid="mpfe_fo"
                    >
                      {index + 1}.
                    </span>{" "}
                    {step}
                  </li>
                ),
              )
            ) : (
              <li
                className="text-white text-[17px] pl-1 tracking-wider flex items-center"
                data-oid="vp4f0xk"
              >
                <span
                  className={`${dir === "rtl" ? "ml-2" : "mr-2"} text-[#D4AF37]`}
                  data-oid=".njdth6"
                >
                  1.
                </span>{" "}
                Loading booking steps...
              </li>
            )}
          </ol>
        </div>

        <div className="mt-10 flex justify-center" data-oid="fuuxgw7">
          <Link
            to="/contact"
            className="bg-black py-3 px-8 rounded-xl outline-none w-fit text-[#D4AF37] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
            data-oid="knww7kp"
          >
            {t("service-pages.common.book-now")}
          </Link>
        </div>
      </div>
    </div>
  );
};

const AirportTransfer = () => {
  return (
    <ErrorBoundary data-oid="nqw.p8x">
      <AirportTransferContent data-oid="m1fbsbs" />
    </ErrorBoundary>
  );
};

export default AirportTransfer;
