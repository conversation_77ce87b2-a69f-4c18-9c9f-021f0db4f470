import React, { Suspense } from "react";
import { Carpool, Contact, Services, Hero, Tourism } from "../components";
import ErrorBoundary from "../components/ErrorBoundary";

// Loading components for better UX
const SectionSkeleton = () => (
  <div className="animate-pulse bg-black p-8" data-oid="8f:ywae">
    <div
      className="h-8 bg-gray-800 rounded mb-4 w-1/3"
      data-oid="-opcp-d"
    ></div>
    <div
      className="h-4 bg-gray-800 rounded mb-2 w-full"
      data-oid="kh-ct_s"
    ></div>
    <div
      className="h-4 bg-gray-800 rounded mb-2 w-3/4"
      data-oid="jg456ni"
    ></div>
    <div className="h-4 bg-gray-800 rounded w-1/2" data-oid="zbou29a"></div>
  </div>
);

const HomePage = () => {
  return (
    <ErrorBoundary data-oid="5ft_72o">
      <div className="black-gold-bg" data-oid="2cc1yaq">
        {/* Hero section with slideshow background */}
        <div className="relative z-0" data-oid="l6ye1m4">
          <Hero data-oid="i.bn33t" />
        </div>

        {/* Enhanced sections with Suspense for better loading */}
        <Suspense
          fallback={<SectionSkeleton data-oid="z.43.xt" />}
          data-oid="59pjekv"
        >
          <div
            id="carpool"
            className="relative z-10 bg-black section-transition"
            data-oid="kz_k3dl"
          >
            <Carpool data-oid="bd-e.sy" />
          </div>
        </Suspense>

        <Suspense
          fallback={<SectionSkeleton data-oid="-en66bw" />}
          data-oid="v4_05sg"
        >
          <div
            id="services"
            className="relative z-10 bg-black section-transition"
            data-oid="21m.w:2"
          >
            <Services data-oid="icciqcz" />
          </div>
        </Suspense>

        <Suspense
          fallback={<SectionSkeleton data-oid="3lqild6" />}
          data-oid=":nnvtoc"
        >
          <div
            id="tourism"
            className="relative z-10 bg-black section-transition"
            data-oid="zison8o"
          >
            <Tourism data-oid="msk4u:v" />
          </div>
        </Suspense>

        <Suspense
          fallback={<SectionSkeleton data-oid="gnj8k.l" />}
          data-oid="4dte_d-"
        >
          <div
            id="contact"
            className="relative z-10 bg-black section-transition"
            data-oid="-i3tfqe"
          >
            <Contact data-oid="u1c8.d2" />
          </div>
        </Suspense>
      </div>
    </ErrorBoundary>
  );
};

export default HomePage;
