import React from "react";
import { styles } from "../styles";
import { motion } from "framer-motion";
import { textVariant } from "../utils/motion";

import { Contact } from "../components";
import { useTranslation } from "react-i18next";

const ContactPage = () => {
  const { t } = useTranslation();

  return (
    <div className="relative z-0" data-oid="r0_b3wk">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid="x44le5x"
      >
        <motion.div variants={textVariant()} data-oid="_l:flj7">
          <p className={styles.sectionSubText} data-oid="4ulc-:t">
            {t("contact.subtitle")}
          </p>
          <h2 className={styles.sectionHeadText} data-oid="rizg2qf">
            {t("contact.title")}
          </h2>
        </motion.div>

        <Contact data-oid="yl2i.os" />
      </div>
    </div>
  );
};

export default ContactPage;
