import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Box, Text } from "@chakra-ui/react";

import Customize from "../../components/common/Customize";
import PreviewSlider from "../../components/common/PreviewSlider";
import PreviewSwitch from "../../components/common/PreviewSwitch";
import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import RefreshButton from "../../components/common/RefreshButton";
import useForceRerender from "../../hooks/useForceRerender";

import Lanyard from "../../content/Components/Lanyard/Lanyard";
import { lanyard } from "../../constants/code/Components/lanyardCode";

const LanyardDemo = () => {
  const [cameraDistance, setCameraDistance] = useState(24);
  const [stopGravity, setStopGravity] = useState(false);

  const [key, forceRerender] = useForceRerender();

  const propData = [
    {
      name: "position",
      type: "array",
      default: "[0, 0, 30]",
      description: "Initial camera position for the canvas.",
    },
    {
      name: "gravity",
      type: "array",
      default: "[0, -40, 0]",
      description: "Gravity vector for the physics simulation.",
    },
    {
      name: "fov",
      type: "number",
      default: "20",
      description: "Camera field of view.",
    },
    {
      name: "transparent",
      type: "boolean",
      default: "true",
      description: "Enables a transparent background for the canvas.",
    },
  ];

  return (
    <TabbedLayout data-oid="k4-d_af">
      <PreviewTab data-oid="862f-cp">
        <Box
          position="relative"
          className="demo-container"
          h={600}
          p={0}
          overflow="hidden"
          data-oid="tfijoyl"
        >
          <RefreshButton onClick={forceRerender} data-oid="26b3vo7" />
          <Text
            position="absolute"
            fontSize="clamp(2rem, 6vw, 6rem)"
            fontWeight={900}
            color="#222"
            data-oid="g-bt0xi"
          >
            Drag It!
          </Text>
          <Lanyard
            key={key}
            position={[0, 0, cameraDistance]}
            gravity={stopGravity ? [0, 0, 0] : [0, -40, 0]}
            data-oid="t2_-vpz"
          />
        </Box>

        <Customize data-oid="40lici4">
          <PreviewSlider
            title="Camera Distance"
            min={20}
            max={50}
            step={1}
            value={cameraDistance}
            onChange={(val) => {
              setCameraDistance(val);
              forceRerender();
            }}
            data-oid="rbsv:nw"
          />

          <PreviewSwitch
            title="Disable Gravity"
            isChecked={stopGravity}
            onChange={(e) => setStopGravity(e.target.checked)}
            data-oid="fxtoxle"
          />
        </Customize>

        <PropTable data={propData} data-oid="58xwlfk" />
        <Dependencies
          dependencyList={[
            "three",
            "meshline",
            "@react-three/fiber",
            "@react-three/drei",
            "@react-three/rapier",
          ]}
          data-oid="ko1pvp9"
        />
      </PreviewTab>

      <CodeTab data-oid="tz3xvqa">
        <CodeExample codeObject={lanyard} data-oid="qmxzg2a" />
      </CodeTab>

      <CliTab data-oid="rdvo-xe">
        <CliInstallation {...lanyard} data-oid="9._bnvz" />
      </CliTab>
    </TabbedLayout>
  );
};

export default LanyardDemo;
