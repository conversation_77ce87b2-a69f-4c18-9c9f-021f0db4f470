import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  FaArrowRight,
  FaArrowLeft,
  FaCar,
  FaShuttleVan,
  FaCarSide,
  FaWifi,
  FaWheelchair,
  FaBabyCarriage,
  FaRoute,
} from "react-icons/fa";
import { navLinks } from "../constants";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../contexts/LanguageContext";

const BookingForm = () => {
  const { t } = useTranslation();
  const { dir } = useLanguage();

  // Extract services from navLinks
  const serviceOptions =
    navLinks.find((link) => link.id === "services")?.dropdown || [];

  // Extract destinations from navLinks
  const destinationOptions =
    navLinks.find((link) => link.id === "tourism")?.dropdown || [];

  // Extract car models from navLinks
  const carOptions =
    navLinks.find((link) => link.id === "carpool")?.dropdown || [];
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    // Step 1: Travel Details
    startLocation: "",
    destination: "",
    customDestination: "",
    service: "",
    customService: "",
    date: "",
    time: "",
    duration: "1 Tag",
    passengers: "1 Person",

    // Step 2: Vehicle & Extras
    vehicleType: "",
    extras: {
      wifi: false,
      refreshments: false, // Now used for Rollstuhl
      childSeat: false,
      tourGuide: false,
    },

    // Step 3: Personal Data
    name: "",
    email: "",
    phone: "",
    specialRequests: "",
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      extras: {
        ...formData.extras,
        [name]: checked,
      },
    });
  };

  const handleVehicleSelect = (type) => {
    setFormData({
      ...formData,
      vehicleType: type,
    });
  };

  const nextStep = () => {
    setStep(step + 1);
  };

  const prevStep = () => {
    setStep(step - 1);
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Format extras for better readability in email
    const formattedExtras = [];
    if (formData.extras.wifi) formattedExtras.push("WiFi");
    if (formData.extras.refreshments) formattedExtras.push("Wheelchair Access");
    if (formData.extras.childSeat) formattedExtras.push("Child Seat");
    if (formData.extras.tourGuide) formattedExtras.push("Tour Guide");

    // Prepare data for Formspree
    const bookingData = {
      // Travel Details
      "Start Location": formData.startLocation,
      Destination:
        formData.destination === "Andere"
          ? formData.customDestination
          : formData.destination,
      Service:
        formData.service === "Andere"
          ? formData.customService
          : formData.service,
      Date: formData.date,
      Time: formData.time,
      Duration: formData.duration,
      Passengers: formData.passengers,

      // Vehicle & Extras
      "Vehicle Type": formData.vehicleType,
      Extras: formattedExtras.join(", ") || "None",

      // Personal Data
      Name: formData.name,
      Email: formData.email,
      Phone: formData.phone,
      "Special Requests": formData.specialRequests || "None",

      // For Formspree
      _subject: "New Booking Request",
    };

    // Send data to Formspree
    fetch("https://formspree.io/f/myzerjwz", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(bookingData),
    })
      .then((response) => {
        if (response.ok) {
          // Show success message
          alert(t("booking.successMessage"));

          // Reset form
          setStep(1);
          setFormData({
            startLocation: "",
            destination: "",
            customDestination: "",
            service: "",
            customService: "",
            date: "",
            time: "",
            duration: `1 ${t("booking.day")}`,
            passengers: `1 ${t("booking.person")}`,
            vehicleType: "",
            extras: {
              wifi: false,
              refreshments: false, // Now used for Rollstuhl
              childSeat: false,
              tourGuide: false,
            },
            name: "",
            email: "",
            phone: "",
            specialRequests: "",
          });
        } else {
          throw new Error("Network response was not ok");
        }
      })
      .catch((error) => {
        console.error("Error submitting form:", error);
        alert(
          t("booking.errorMessage") ||
            "There was an error submitting your booking. Please try again later.",
        );
      });
  };

  // Progress bar component
  const ProgressBar = () => (
    <div className="w-full mb-8" data-oid="52aj.m8">
      <div className="relative" data-oid=":jrfu22">
        {/* Background bar */}
        <div
          className="absolute top-1/2 left-0 w-full h-1 bg-gray-700 -translate-y-1/2"
          data-oid="pzurqfa"
        ></div>

        {/* Progress bar */}
        <div
          className="absolute top-1/2 left-0 h-1 bg-[#D4AF37] -translate-y-1/2 transition-all duration-300"
          style={{ width: `${(step / 3) * 100}%` }}
          data-oid="b4q-hrp"
        ></div>

        {/* Step indicators */}
        <div className="relative flex justify-between" data-oid="yh_u43.">
          {/* Step 1 */}
          <div className="flex flex-col items-center" data-oid="t4d:w2u">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-black text-xs font-bold ${
                step >= 1 ? "bg-[#D4AF37]" : "bg-black border border-[#D4AF37]"
              }`}
              data-oid="g61y0n5"
            >
              1
            </div>
            <span
              className={`mt-1 text-xs text-center ${step >= 1 ? "text-[#D4AF37] font-medium" : "text-gray-400"}`}
              data-oid="r1u_8.y"
            >
              {t("booking.step1").split(":")[0]}
            </span>
          </div>

          {/* Step 2 */}
          <div className="flex flex-col items-center" data-oid="dsvcn2l">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-black text-xs font-bold ${
                step >= 2 ? "bg-[#D4AF37]" : "bg-black border border-[#D4AF37]"
              }`}
              data-oid="w95s01g"
            >
              2
            </div>
            <span
              className={`mt-1 text-xs text-center ${step >= 2 ? "text-[#D4AF37] font-medium" : "text-gray-400"}`}
              data-oid="p.bp281"
            >
              {t("booking.step2").split(":")[0]}
            </span>
          </div>

          {/* Step 3 */}
          <div className="flex flex-col items-center" data-oid="vgc71.r">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-black text-xs font-bold ${
                step >= 3 ? "bg-[#D4AF37]" : "bg-black border border-[#D4AF37]"
              }`}
              data-oid="gpr6ss_"
            >
              3
            </div>
            <span
              className={`mt-1 text-xs text-center ${step >= 3 ? "text-[#D4AF37] font-medium" : "text-gray-400"}`}
              data-oid=":1_l6tx"
            >
              {t("booking.step3").split(":")[0]}
            </span>
          </div>

          {/* Step 4 */}
          <div className="flex flex-col items-center" data-oid="hv.7wk-">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-black text-xs font-bold ${
                step >= 4 ? "bg-[#D4AF37]" : "bg-black border border-[#D4AF37]"
              }`}
              data-oid="bzvdwnw"
            >
              4
            </div>
            <span
              className={`mt-1 text-xs text-center ${step >= 4 ? "text-[#D4AF37] font-medium" : "text-gray-400"}`}
              data-oid="wbq28yf"
            >
              {t("booking.step4")}
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div
      className="w-full h-full bg-black rounded-2xl p-6 overflow-y-auto shadow-card border border-[#D4AF37]"
      data-oid="_31vp75"
    >
      <div className="section-header" data-oid="4j62top">
        <h3 className="section-title text-[#D4AF37]" data-oid="gig1-j5">
          {t("booking.title")}
        </h3>
        <div className="section-title-underline" data-oid="cackjbx"></div>
      </div>

      <ProgressBar data-oid="2.c2ew1" />

      <form
        onSubmit={handleSubmit}
        className="relative z-10"
        data-oid="xicgkaq"
      >
        {step === 1 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
            data-oid="m_-mv8-"
          >
            <h4
              className="text-[#D4AF37] text-lg font-semibold mb-6 text-center"
              data-oid="pxuc01."
            >
              {t("booking.step1")}
            </h4>

            <div
              className="grid grid-cols-1 md:grid-cols-2 gap-6"
              data-oid="i7sf6_1"
            >
              <div data-oid="sxuh8ir">
                <label
                  className="block text-[#D4AF37] text-base font-medium mb-4"
                  data-oid="32zew1c"
                >
                  {t("booking.startLocation")}
                </label>
                <input
                  type="text"
                  name="startLocation"
                  value={formData.startLocation}
                  onChange={handleInputChange}
                  placeholder={t("booking.startLocationPlaceholder")}
                  className="bg-black py-4 px-6 placeholder:text-gray-400 text-white rounded-lg outline-none border border-[#D4AF37] font-medium w-full focus:shadow-[0_0_5px_#D4AF37] transition-all duration-300"
                  style={{
                    textAlign: dir === "rtl" ? "right" : "left",
                    direction: dir,
                  }}
                  required
                  data-oid="lqtm9uq"
                />
              </div>

              <div data-oid="ohob9fu">
                <label
                  className="block text-[#D4AF37] text-base font-medium mb-4"
                  data-oid="2.o9py1"
                >
                  {t("booking.service")}
                </label>
                <select
                  name="service"
                  value={formData.service}
                  onChange={handleInputChange}
                  className="bg-black py-4 px-6 text-white rounded-lg outline-none border border-[#D4AF37] font-medium appearance-none w-full focus:shadow-[0_0_5px_#D4AF37] transition-all duration-300"
                  style={{
                    textAlign: dir === "rtl" ? "right" : "left",
                    direction: dir,
                  }}
                  required
                  data-oid="dtw92ey"
                >
                  <option value="" data-oid="j-5arop">
                    {t("booking.servicePlaceholder")}
                  </option>
                  {serviceOptions.map((option) => (
                    <option
                      key={option.id}
                      value={option.title}
                      data-oid="etfe4kd"
                    >
                      {option.title}
                    </option>
                  ))}
                  <option value="Andere" data-oid="_tz:inx">
                    {t("booking.otherService")}
                  </option>
                </select>
                {formData.service === "Andere" && (
                  <input
                    type="text"
                    name="customService"
                    value={formData.customService || ""}
                    onChange={handleInputChange}
                    placeholder={t("booking.customServicePlaceholder")}
                    className="bg-black py-4 px-6 placeholder:text-gray-400 text-white rounded-lg outline-none border border-[#D4AF37] font-medium w-full mt-2 focus:shadow-[0_0_5px_#D4AF37] transition-all duration-300"
                    style={{
                      textAlign: dir === "rtl" ? "right" : "left",
                      direction: dir,
                    }}
                    data-oid="h2pry4-"
                  />
                )}
              </div>
            </div>

            <div
              className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4"
              data-oid="nx04.oz"
            >
              <div data-oid="u18dslu">
                <label
                  className="block text-[#D4AF37] text-base font-medium mb-4"
                  data-oid="l9heq25"
                >
                  {t("booking.destination")}
                </label>
                <select
                  name="destination"
                  value={formData.destination}
                  onChange={handleInputChange}
                  className="bg-black py-4 px-6 text-white rounded-lg outline-none border border-[#D4AF37] font-medium appearance-none w-full focus:shadow-[0_0_5px_#D4AF37] transition-all duration-300"
                  style={{
                    textAlign: dir === "rtl" ? "right" : "left",
                    direction: dir,
                  }}
                  required
                  data-oid="vnunq0f"
                >
                  <option value="" data-oid="sp4lki9">
                    {t("booking.destinationPlaceholder")}
                  </option>
                  {destinationOptions.map((option) => (
                    <option
                      key={option.id}
                      value={option.title}
                      data-oid="m2qbjso"
                    >
                      {option.title}
                    </option>
                  ))}
                  <option value="Andere" data-oid="pf5ul3v">
                    {t("booking.otherDestination")}
                  </option>
                </select>
                {formData.destination === "Andere" && (
                  <input
                    type="text"
                    name="customDestination"
                    value={formData.customDestination || ""}
                    onChange={handleInputChange}
                    placeholder={t("booking.customDestinationPlaceholder")}
                    className="bg-black py-4 px-6 placeholder:text-gray-400 text-white rounded-lg outline-none border border-[#D4AF37] font-medium w-full mt-2 focus:shadow-[0_0_5px_#D4AF37] transition-all duration-300"
                    style={{
                      textAlign: dir === "rtl" ? "right" : "left",
                      direction: dir,
                    }}
                    data-oid="uvi-z9a"
                  />
                )}
              </div>

              <div data-oid="di3iks5">
                <label
                  className="block text-[#D4AF37] text-base font-medium mb-4"
                  data-oid="aurcns."
                >
                  {t("booking.date")}
                </label>
                <input
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  className="bg-black py-4 px-6 text-white rounded-lg outline-none border border-[#D4AF37] font-medium w-full focus:shadow-[0_0_5px_#D4AF37] transition-all duration-300"
                  style={{
                    textAlign: dir === "rtl" ? "right" : "left",
                    direction: dir,
                  }}
                  required
                  data-oid="8x472vp"
                />
              </div>

              <div data-oid="j53n.vy">
                <label
                  className="block text-[#D4AF37] text-base font-medium mb-4"
                  data-oid="edd.b5b"
                >
                  {t("booking.time")}
                </label>
                <input
                  type="time"
                  name="time"
                  value={formData.time}
                  onChange={handleInputChange}
                  className="bg-black py-4 px-6 text-white rounded-lg outline-none border border-[#D4AF37] font-medium w-full focus:shadow-[0_0_5px_#D4AF37] transition-all duration-300"
                  style={{
                    textAlign: dir === "rtl" ? "right" : "left",
                    direction: dir,
                  }}
                  required
                  data-oid="--bek-1"
                />
              </div>
            </div>

            <div
              className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4"
              data-oid="f_83zq."
            >
              <div data-oid="lwo5iq.">
                <label
                  className="block text-[#D4AF37] text-base font-medium mb-4"
                  data-oid="ijqvm_j"
                >
                  {t("booking.duration")}
                </label>
                <select
                  name="duration"
                  value={formData.duration}
                  onChange={handleInputChange}
                  className="bg-black py-4 px-6 text-white rounded-lg outline-none border border-[#D4AF37] font-medium appearance-none w-full focus:shadow-[0_0_5px_#D4AF37] transition-all duration-300"
                  style={{
                    textAlign: dir === "rtl" ? "right" : "left",
                    direction: dir,
                  }}
                  data-oid="t6vw1xm"
                >
                  <option value={`1 ${t("booking.day")}`} data-oid="advk8nh">
                    1 {t("booking.day")}
                  </option>
                  <option value={`2 ${t("booking.days")}`} data-oid="62kpw_9">
                    2 {t("booking.days")}
                  </option>
                  <option value={`3 ${t("booking.days")}`} data-oid=":b9-jvg">
                    3 {t("booking.days")}
                  </option>
                  <option value={`4 ${t("booking.days")}`} data-oid="gmkx:mn">
                    4 {t("booking.days")}
                  </option>
                  <option value={`5 ${t("booking.days")}`} data-oid="r_nt8pk">
                    5 {t("booking.days")}
                  </option>
                  <option value={`6 ${t("booking.days")}`} data-oid="5tixs4x">
                    6 {t("booking.days")}
                  </option>
                  <option value={`7 ${t("booking.days")}`} data-oid="_915nya">
                    7 {t("booking.days")}
                  </option>
                  <option value={t("booking.moreThan7Days")} data-oid="551mkd7">
                    {t("booking.moreThan7Days")}
                  </option>
                </select>
              </div>

              <div data-oid="aef3:sh">
                <label
                  className="block text-[#D4AF37] text-base font-medium mb-4"
                  data-oid="vpm3-57"
                >
                  {t("booking.passengers")}
                </label>
                <select
                  name="passengers"
                  value={formData.passengers}
                  onChange={handleInputChange}
                  className="bg-black py-4 px-6 text-white rounded-lg outline-none border border-[#D4AF37] font-medium appearance-none w-full focus:shadow-[0_0_5px_#D4AF37] transition-all duration-300"
                  style={{
                    textAlign: dir === "rtl" ? "right" : "left",
                    direction: dir,
                  }}
                  data-oid="uo:wxzy"
                >
                  <option value={`1 ${t("booking.person")}`} data-oid="go0mays">
                    1 {t("booking.person")}
                  </option>
                  <option value={`2 ${t("booking.people")}`} data-oid="alyhesp">
                    2 {t("booking.people")}
                  </option>
                  <option value={`3 ${t("booking.people")}`} data-oid="tlbofr3">
                    3 {t("booking.people")}
                  </option>
                  <option value={`4 ${t("booking.people")}`} data-oid="aia.a82">
                    4 {t("booking.people")}
                  </option>
                  <option value={`5 ${t("booking.people")}`} data-oid="rgebf4z">
                    5 {t("booking.people")}
                  </option>
                  <option value={`6 ${t("booking.people")}`} data-oid="6:fy8zs">
                    6 {t("booking.people")}
                  </option>
                  <option value={`7 ${t("booking.people")}`} data-oid="h8y_g3c">
                    7 {t("booking.people")}
                  </option>
                  <option value={`8 ${t("booking.people")}`} data-oid="rt0esrk">
                    8 {t("booking.people")}
                  </option>
                  <option
                    value={t("booking.moreThan8People")}
                    data-oid="xq7iclk"
                  >
                    {t("booking.moreThan8People")}
                  </option>
                </select>
              </div>
            </div>

            <div className="flex justify-center mt-6" data-oid="453271v">
              <button
                type="button"
                onClick={nextStep}
                className="bg-black py-3 px-8 rounded-xl outline-none w-fit text-[#D4AF37] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
                data-oid="96_lw0l"
              >
                {t("booking.next")}{" "}
                <FaArrowRight
                  className={`${dir === "rtl" ? "mr-2" : "ml-2"} inline`}
                  data-oid="8pqteoq"
                />
              </button>
            </div>
          </motion.div>
        )}

        {step === 2 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
            data-oid="3:3twbc"
          >
            <h4
              className="text-[#D4AF37] text-lg font-semibold mb-6 text-center"
              data-oid="1iqx5hu"
            >
              {t("booking.step2")}
            </h4>

            <div className="mb-4" data-oid="-4n7j-q">
              <label
                className="block text-[#D4AF37] text-base font-medium mb-4 text-center"
                data-oid="9i2lter"
              >
                {t("booking.vehicleType")}
              </label>
              <div
                className="grid grid-cols-1 md:grid-cols-3 gap-4"
                data-oid="scvvrx5"
              >
                {carOptions.map((car) => (
                  <div
                    key={car.id}
                    className={`p-3 rounded-lg cursor-pointer flex flex-col items-center transition-all duration-300 ${
                      formData.vehicleType === car.title
                        ? "bg-black border border-[#D4AF37] shadow-[0_0_5px_#D4AF37]"
                        : "bg-black border border-gray-700 hover:border-[#D4AF37]/50"
                    }`}
                    onClick={() => handleVehicleSelect(car.title)}
                    data-oid="r4553h:"
                  >
                    {car.id === "mercedes-sclass" && (
                      <FaCar
                        className={`text-3xl mb-2 ${formData.vehicleType === car.title ? "text-[#D4AF37]" : "text-gray-500"}`}
                        data-oid="kzc84b4"
                      />
                    )}
                    {car.id === "bmw-7" && (
                      <FaCarSide
                        className={`text-3xl mb-2 ${formData.vehicleType === car.title ? "text-[#D4AF37]" : "text-gray-500"}`}
                        data-oid="e163.:g"
                      />
                    )}
                    {car.id === "mercedes-vclass" && (
                      <FaShuttleVan
                        className={`text-3xl mb-2 ${formData.vehicleType === car.title ? "text-[#D4AF37]" : "text-gray-500"}`}
                        data-oid="b_ptp5p"
                      />
                    )}
                    <h5
                      className={`font-medium text-xs ${formData.vehicleType === car.title ? "text-white" : "text-gray-400"}`}
                      data-oid=":w0a5wp"
                    >
                      {t(`carpool.${car.id}`)}
                    </h5>
                    <p
                      className="text-gray-500 text-xs text-center mt-1"
                      data-oid="bjm4y7s"
                    >
                      {car.id === "mercedes-sclass" && t("booking.upTo3People")}
                      {car.id === "bmw-7" && t("booking.premiumVehicle")}
                      {car.id === "mercedes-vclass" && t("booking.upTo8People")}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            <div data-oid="mcsdvhy">
              <label
                className="block text-[#D4AF37] text-base font-medium mb-4 text-center"
                data-oid="0fsuwa9"
              >
                {t("booking.extras")}
              </label>
              <div
                className="grid grid-cols-1 md:grid-cols-2 gap-3"
                data-oid="i_fbshn"
              >
                <div
                  className="flex items-center p-2 bg-black rounded-lg border border-gray-700 hover:border-[#D4AF37]/50 transition-all duration-300"
                  data-oid="_r6ujc1"
                >
                  <input
                    type="checkbox"
                    id="wifi"
                    name="wifi"
                    checked={formData.extras.wifi}
                    onChange={handleCheckboxChange}
                    className={`${dir === "rtl" ? "ml-2" : "mr-2"} h-4 w-4 accent-[#D4AF37] cursor-pointer`}
                    data-oid="rxkpide"
                  />

                  <FaWifi
                    className={`text-[#D4AF37] ${dir === "rtl" ? "ml-2" : "mr-2"} text-sm`}
                    data-oid="dz_yy5z"
                  />

                  <label
                    htmlFor="wifi"
                    className="text-white text-xs cursor-pointer"
                    data-oid="4:zxe5x"
                  >
                    {t("booking.wifi")}
                  </label>
                </div>

                <div
                  className="flex items-center p-2 bg-black rounded-lg border border-gray-700 hover:border-[#D4AF37]/50 transition-all duration-300"
                  data-oid="k7-68ax"
                >
                  <input
                    type="checkbox"
                    id="refreshments"
                    name="refreshments"
                    checked={formData.extras.refreshments}
                    onChange={handleCheckboxChange}
                    className={`${dir === "rtl" ? "ml-2" : "mr-2"} h-4 w-4 accent-[#D4AF37] cursor-pointer`}
                    data-oid="gc0utfx"
                  />

                  <FaWheelchair
                    className={`text-[#D4AF37] ${dir === "rtl" ? "ml-2" : "mr-2"} text-sm`}
                    data-oid="ufg7wuw"
                  />

                  <label
                    htmlFor="refreshments"
                    className="text-white text-xs cursor-pointer"
                    data-oid="2baremm"
                  >
                    {t("booking.wheelchair")}
                  </label>
                </div>

                <div
                  className="flex items-center p-2 bg-black rounded-lg border border-gray-700 hover:border-[#D4AF37]/50 transition-all duration-300"
                  data-oid=".888phm"
                >
                  <input
                    type="checkbox"
                    id="childSeat"
                    name="childSeat"
                    checked={formData.extras.childSeat}
                    onChange={handleCheckboxChange}
                    className={`${dir === "rtl" ? "ml-2" : "mr-2"} h-4 w-4 accent-[#D4AF37] cursor-pointer`}
                    data-oid="jdf.s3d"
                  />

                  <FaBabyCarriage
                    className={`text-[#D4AF37] ${dir === "rtl" ? "ml-2" : "mr-2"} text-sm`}
                    data-oid="0ze1krz"
                  />

                  <label
                    htmlFor="childSeat"
                    className="text-white text-xs cursor-pointer"
                    data-oid="-8jw:6x"
                  >
                    {t("booking.childSeat")}
                  </label>
                </div>

                <div
                  className="flex items-center p-2 bg-black rounded-lg border border-gray-700 hover:border-[#D4AF37]/50 transition-all duration-300"
                  data-oid="hxswtwx"
                >
                  <input
                    type="checkbox"
                    id="tourGuide"
                    name="tourGuide"
                    checked={formData.extras.tourGuide}
                    onChange={handleCheckboxChange}
                    className={`${dir === "rtl" ? "ml-2" : "mr-2"} h-4 w-4 accent-[#D4AF37] cursor-pointer`}
                    data-oid="w2rdcgw"
                  />

                  <FaRoute
                    className={`text-[#D4AF37] ${dir === "rtl" ? "ml-2" : "mr-2"} text-sm`}
                    data-oid="mobn6n-"
                  />

                  <label
                    htmlFor="tourGuide"
                    className="text-white text-xs cursor-pointer"
                    data-oid="ekbalml"
                  >
                    {t("booking.tourGuide")}
                  </label>
                </div>
              </div>
            </div>

            <div className="flex justify-between mt-4" data-oid="pridw_b">
              <button
                type="button"
                onClick={prevStep}
                className="bg-black py-3 px-8 rounded-xl outline-none w-fit text-[#D4AF37] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
                data-oid="blyb31r"
              >
                {dir === "rtl" ? (
                  <>
                    {t("booking.back")}{" "}
                    <FaArrowRight className="ml-2 inline" data-oid=":5bl352" />
                  </>
                ) : (
                  <>
                    <FaArrowLeft className="mr-2 inline" data-oid="rt5_nmu" />{" "}
                    {t("booking.back")}
                  </>
                )}
              </button>

              <button
                type="button"
                onClick={nextStep}
                className="bg-black py-3 px-8 rounded-xl outline-none w-fit text-[#D4AF37] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
                data-oid=":8u4ha-"
              >
                {dir === "rtl" ? (
                  <>
                    <FaArrowLeft className="mr-2 inline" data-oid="af9mtzk" />{" "}
                    {t("booking.next")}
                  </>
                ) : (
                  <>
                    {t("booking.next")}{" "}
                    <FaArrowRight className="ml-2 inline" data-oid="oxyaome" />
                  </>
                )}
              </button>
            </div>
          </motion.div>
        )}

        {step === 3 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
            data-oid="80l4by_"
          >
            <h4
              className="text-[#D4AF37] text-lg font-semibold mb-6 text-center"
              data-oid="vb0r5yv"
            >
              {t("booking.step3")}
            </h4>

            <div className="mb-4" data-oid="go0djv9">
              <label
                className="block text-[#D4AF37] text-base font-medium mb-4"
                data-oid="8jsa1u_"
              >
                {t("booking.name")}
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder={t("booking.namePlaceholder")}
                className="bg-black py-4 px-6 placeholder:text-gray-400 text-white rounded-lg outline-none border border-[#D4AF37] font-medium w-full focus:shadow-[0_0_5px_#D4AF37] transition-all duration-300"
                style={{
                  textAlign: dir === "rtl" ? "right" : "left",
                  direction: dir,
                }}
                required
                data-oid="qaswxsj"
              />
            </div>

            <div className="mb-4" data-oid="6._oq20">
              <label
                className="block text-[#D4AF37] text-base font-medium mb-4"
                data-oid="qs_e5f6"
              >
                {t("booking.email")}
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder={t("booking.emailPlaceholder")}
                className="bg-black py-4 px-6 placeholder:text-gray-400 text-white rounded-lg outline-none border border-[#D4AF37] font-medium w-full focus:shadow-[0_0_5px_#D4AF37] transition-all duration-300"
                style={{
                  textAlign: dir === "rtl" ? "right" : "left",
                  direction: dir,
                }}
                required
                data-oid="c3i666d"
              />
            </div>

            <div className="mb-4" data-oid="lwv9lg8">
              <label
                className="block text-[#D4AF37] text-base font-medium mb-4"
                data-oid="vu3o_u:"
              >
                {t("booking.phone")}
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                placeholder={t("booking.phonePlaceholder")}
                className="bg-black py-4 px-6 placeholder:text-gray-400 text-white rounded-lg outline-none border border-[#D4AF37] font-medium w-full focus:shadow-[0_0_5px_#D4AF37] transition-all duration-300"
                style={{
                  textAlign: dir === "rtl" ? "right" : "left",
                  direction: dir,
                }}
                required
                data-oid="vek6jiy"
              />
            </div>

            <div className="mb-4" data-oid="f.1elri">
              <label
                className="block text-[#D4AF37] text-base font-medium mb-4"
                data-oid="09va3u9"
              >
                {t("booking.notes")}
              </label>
              <textarea
                name="specialRequests"
                value={formData.specialRequests}
                onChange={handleInputChange}
                placeholder={t("booking.notesPlaceholder")}
                className="bg-black py-4 px-6 placeholder:text-gray-400 text-white rounded-lg outline-none border border-[#D4AF37] font-medium h-24 resize-none w-full focus:shadow-[0_0_5px_#D4AF37] transition-all duration-300"
                style={{
                  textAlign: dir === "rtl" ? "right" : "left",
                  direction: dir,
                }}
                data-oid="r2p6s9i"
              ></textarea>
            </div>

            <div className="flex items-center mt-3 mb-4" data-oid="8sxio3z">
              <input
                type="checkbox"
                id="privacy"
                className={`${dir === "rtl" ? "ml-2" : "mr-2"} h-3 w-3 accent-[#D4AF37] cursor-pointer`}
                required
                data-oid="zov4lb0"
              />

              <label
                htmlFor="privacy"
                className="text-white text-xs cursor-pointer"
                data-oid="-uq0-ul"
              >
                {t("booking.privacyPolicy")}{" "}
                <span className="text-[#D4AF37] underline" data-oid="em_j8bk">
                  {t("booking.privacyPolicyLink")}
                </span>
                .
              </label>
            </div>

            <div className="flex justify-between mt-4" data-oid="srvsbra">
              <button
                type="button"
                onClick={prevStep}
                className="bg-black py-3 px-8 rounded-xl outline-none w-fit text-[#D4AF37] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
                data-oid="4kidjeu"
              >
                {dir === "rtl" ? (
                  <>
                    {t("booking.back")}{" "}
                    <FaArrowRight className="ml-2 inline" data-oid="spi:ynj" />
                  </>
                ) : (
                  <>
                    <FaArrowLeft className="mr-2 inline" data-oid="pqjwzzq" />{" "}
                    {t("booking.back")}
                  </>
                )}
              </button>

              <button
                type="submit"
                className="bg-black py-3 px-8 rounded-xl outline-none w-fit text-[#D4AF37] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
                data-oid="59iwk1h"
              >
                {dir === "rtl" ? (
                  <>
                    <FaArrowLeft className="mr-2 inline" data-oid="i3yles-" />{" "}
                    {t("booking.requestTour")}
                  </>
                ) : (
                  <>
                    {t("booking.requestTour")}{" "}
                    <FaArrowRight className="ml-2 inline" data-oid="baug9-8" />
                  </>
                )}
              </button>
            </div>
          </motion.div>
        )}
      </form>
    </div>
  );
};

export default BookingForm;
