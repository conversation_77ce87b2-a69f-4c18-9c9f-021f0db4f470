import LandingHeader from "../components/landing/LandingHeader/LandingHeader";
import LandingHero from "../components/landing/LandingHero";
import LandingStats from "../components/landing/LandingStats";
import LandingDemo from "../components/landing/LandingDemo";
import LandingTestimonials from "../components/landing/LandingTestimonials";
import LandingFooter from "../components/landing/LandingFooter";

import { Helmet } from "react-helmet-async";

const LandingPage = () => {
  return (
    <section className="landing-wrapper" data-oid="xi-xwj7">
      <Helmet data-oid="nb:mc3g">
        <title data-oid="z25ku2t">
          React Bits - Animated UI Components For React
        </title>
      </Helmet>
      <LandingHeader data-oid="w9il:96" />
      <LandingHero data-oid="sz5kzt0" />
      <LandingStats data-oid="llawdjs" />
      <LandingDemo data-oid="0z_7tw8" />
      <LandingTestimonials data-oid="yzyt6uw" />
      <LandingFooter data-oid="v4g:azp" />
    </section>
  );
};

export default LandingPage;
