import React from "react";
import CityTemplate from "./CityTemplate";
import { hamburg, hamburg1 } from "../../../assets";

const Hamburg = () => {
  // City images
  const cityImages = [hamburg, hamburg1];

  // Top attractions
  const attractions = [
    {
      name: "Elbphilharmonie",
      description:
        "Iconic concert hall with stunning architecture, offering panoramic views of the harbor and city.",
    },
    {
      name: "Miniatur Wunderland",
      description:
        "The world's largest model railway exhibition with incredibly detailed miniature landscapes from around the world.",
    },
    {
      name: "Speicherstadt",
      description:
        "UNESCO World Heritage site featuring historic red-brick warehouses in the port area, now home to museums and attractions.",
    },
    {
      name: "St. Pauli and Reeperbahn",
      description:
        "Hamburg's famous entertainment district with theaters, clubs, restaurants, and vibrant nightlife.",
    },
    {
      name: "Alster Lakes",
      description:
        "Beautiful inner-city lakes perfect for walking, sailing, and enjoying Hamburg's scenic waterfront.",
    },
  ];

  // Recommended restaurants
  const restaurants = [
    {
      name: "Fischereihafen Restaurant",
      description:
        "Upscale seafood restaurant with harbor views, serving fresh fish and maritime specialties.",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      description:
        "Popular restaurant by celebrity chef <PERSON>, offering creative German cuisine in an industrial setting.",
    },
    {
      name: "Parlament",
      description:
        "Traditional Hamburg restaurant in a historic cellar, serving local specialties and craft beers.",
    },
    {
      name: "Jellyfish",
      description:
        "Michelin-starred restaurant specializing in innovative seafood dishes with Japanese influences.",
    },
  ];

  // Shopping areas
  const shopping = [
    {
      name: "Mönckebergstrasse",
      description:
        "Hamburg's main shopping street with department stores, fashion retailers, and specialty shops.",
    },
    {
      name: "Europa Passage",
      description:
        "Modern shopping mall in the city center with over 120 stores across five floors.",
    },
    {
      name: "Alsterhaus",
      description:
        "Luxury department store offering high-end fashion, beauty products, and gourmet food.",
    },
    {
      name: "Karolinenviertel",
      description:
        "Trendy district with independent boutiques, vintage shops, and local designers.",
    },
  ];

  // Accommodation options
  const hotels = [
    {
      name: "The Fontenay",
      description:
        "Luxury hotel on the shores of the Alster Lake with elegant design, spa, and rooftop restaurant.",
    },
    {
      name: "Fairmont Hotel Vier Jahreszeiten",
      description:
        "Historic 5-star hotel on the Alster with opulent interiors and exceptional service.",
    },
    {
      name: "25hours Hotel HafenCity",
      description:
        "Stylish design hotel with maritime theme in Hamburg's modern harbor district.",
    },
    {
      name: "Hotel Atlantic Kempinski",
      description:
        "Grand hotel with over 100 years of history, offering classic luxury and waterfront views.",
    },
  ];

  // Upcoming events
  const events = [
    {
      name: "Hamburger DOM",
      date: "March, July, and November",
      description:
        "Northern Germany's biggest funfair, held three times a year with rides, food, and entertainment.",
    },
    {
      name: "Hafengeburtstag",
      date: "May",
      description:
        "Hamburg Port Anniversary festival with ship parades, fireworks, and waterfront celebrations.",
    },
    {
      name: "Reeperbahn Festival",
      date: "September",
      description:
        "Europe's largest club festival featuring hundreds of concerts across St. Pauli venues.",
    },
    {
      name: "Christmas Markets",
      date: "November-December",
      description:
        "Multiple festive markets throughout the city, including the elegant Rathausmarkt market.",
    },
  ];

  // Google Maps embed URL
  const mapUrl =
    "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d152784.98407574175!2d9.9158906!3d53.5510846!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47b161837e1813b9%3A0x4263df27bd63aa0!2sHamburg%2C%20Germany!5e0!3m2!1sen!2sus!4v1653061799729!5m2!1sen!2sus";

  return (
    <CityTemplate
      cityKey="hamburg"
      cityImages={cityImages}
      attractions={attractions}
      restaurants={restaurants}
      shopping={shopping}
      hotels={hotels}
      events={events}
      mapUrl={mapUrl}
      data-oid="z7t37kb"
    />
  );
};

export default Hamburg;
