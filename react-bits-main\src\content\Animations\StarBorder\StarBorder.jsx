import "./StarBorder.css";

const StarBorder = ({
  as: Component = "button",
  className = "",
  color = "white",
  speed = "6s",
  children,
  ...rest
}) => {
  return (
    <Component
      className={`star-border-container ${className}`}
      {...rest}
      data-oid="fwvvlcf"
    >
      <div
        className="border-gradient-bottom"
        style={{
          background: `radial-gradient(circle, ${color}, transparent 10%)`,
          animationDuration: speed,
        }}
        data-oid="5bam4qd"
      ></div>
      <div
        className="border-gradient-top"
        style={{
          background: `radial-gradient(circle, ${color}, transparent 10%)`,
          animationDuration: speed,
        }}
        data-oid="f_l:2y."
      ></div>
      <div className="inner-content" data-oid="5rgcg3f">
        {children}
      </div>
    </Component>
  );
};

export default StarBorder;
