import { useEffect, useState } from "react";
import {
  Box,
  Flex,
  Input,
  Switch,
  FormControl,
  FormLabel,
  NumberInput,
  NumberInputField,
} from "@chakra-ui/react";

import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import useForceRerender from "../../hooks/useForceRerender";

import ASCIIText from "../../content/TextAnimations/ASCIIText/ASCIIText";
import { asciiText } from "../../constants/code/TextAnimations/asciiTextCode";

const propData = [
  {
    name: "text",
    type: "string",
    default: '"Hello World!"',
    description: "The text displayed on the plane in the ASCII scene.",
  },
  {
    name: "enableWaves",
    type: "boolean",
    default: "true",
    description: "If false, disables the wavy text animation.",
  },
  {
    name: "asciiFontSize",
    type: "number",
    default: "12",
    description: "Size of the ASCII glyphs in the overlay.",
  },
  {
    name: "textFontSize",
    type: "number",
    default: "200",
    description: "Pixel size for the text that’s drawn onto the plane texture.",
  },
  {
    name: "planeBaseHeight",
    type: "number",
    default: "8",
    description:
      "How tall the plane is in 3D. The plane width is auto-based on text aspect.",
  },
  {
    name: "textColor",
    type: "string",
    default: "#fdf9f3",
    description: "The color of the text drawn onto the plane texture.",
  },
  {
    name: "strokeColor",
    type: "string",
    default: "N/A",
    description:
      "Not used here, but you could add it if you want an outline effect.",
  },
];

const ASCIITextDemo = () => {
  const [text, setText] = useState("Hey!");
  const [enableWaves, setEnableWaves] = useState(true);
  const [asciiFontSize, setAsciiFontSize] = useState(8);

  const [key, forceRerender] = useForceRerender();

  const dependencyList = ["three"];

  useEffect(() => {
    forceRerender();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <TabbedLayout data-oid="i-_i80r">
      <PreviewTab data-oid="hb0gxex">
        <Box
          position="relative"
          className="demo-container"
          minH={400}
          maxH={400}
          overflow="hidden"
          mb={6}
          data-oid="g.bzc9g"
        >
          {/* The ASCII scene with live props */}
          <ASCIIText
            key={key}
            text={text}
            enableWaves={enableWaves}
            asciiFontSize={asciiFontSize}
            textFontSize={250}
            planeBaseHeight={12}
            data-oid="od:-y95"
          />
        </Box>

        <Box mb={6} data-oid="8juueks">
          <h2 className="demo-title-extra" data-oid="wrziawu">
            Customize
          </h2>
          <Flex
            alignItems="center"
            gap={4}
            flexWrap="wrap"
            mb={4}
            data-oid="qsiy2go"
          >
            {/* Text */}
            <FormControl w="200px" data-oid="ubx0god">
              <FormLabel fontSize="sm" data-oid=":_.t:wl">
                Text
              </FormLabel>
              <Input
                value={text}
                onChange={(e) => {
                  setText(e.target.value);
                  forceRerender();
                }}
                placeholder="Enter text..."
                data-oid="977:keq"
              />
            </FormControl>

            {/* ASCII Font Size */}
            <FormControl w="160px" data-oid="lfr5gpy">
              <FormLabel fontSize="sm" data-oid="m1wqsoh">
                ASCII Font
              </FormLabel>
              <NumberInput
                value={asciiFontSize}
                min={1}
                max={64}
                step={1}
                onChange={(_, valNumber) => {
                  setAsciiFontSize(valNumber || 1);
                  forceRerender();
                }}
                data-oid="hhkyull"
              >
                <NumberInputField data-oid="8ynii70" />
              </NumberInput>
            </FormControl>
          </Flex>

          <Flex alignItems="center" gap={4} flexWrap="wrap" data-oid="xotlrw8">
            <FormControl
              display="flex"
              alignItems="center"
              w="160px"
              data-oid="hneagzy"
            >
              <FormLabel mb="0" fontSize="sm" data-oid="yg3_bao">
                Waves
              </FormLabel>
              <Switch
                isChecked={enableWaves}
                onChange={(e) => setEnableWaves(e.target.checked)}
                data-oid="ifk77or"
              />
            </FormControl>
          </Flex>
        </Box>

        <PropTable data={propData} data-oid="2_6rb4c" />
        <Dependencies dependencyList={dependencyList} data-oid="7:s3vp9" />
      </PreviewTab>

      <CodeTab data-oid="bcyyy1y">
        <CodeExample codeObject={asciiText} data-oid="v4469pd" />
      </CodeTab>

      <CliTab data-oid="2pfw2w-">
        <CliInstallation {...asciiText} data-oid="14p1_zo" />
      </CliTab>
    </TabbedLayout>
  );
};

export default ASCIITextDemo;
