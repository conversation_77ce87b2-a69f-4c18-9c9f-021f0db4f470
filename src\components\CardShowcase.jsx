import React, { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../contexts/LanguageContext";
import GalleryModal from "./GalleryModal";
import "../styles/cardShowcase.css";

// Reusable Card Showcase component for Services, Carpool, and Tourism
const CardShowcase = ({ items, translationPrefix, iconBg = "#000000" }) => {
  const { t } = useTranslation();
  const { dir, language } = useLanguage();
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedCar, setSelectedCar] = useState(null);

  // Function to open gallery modal for carpool items
  const openGalleryModal = (e, item) => {
    // Only open gallery for carpool items
    if (translationPrefix === "carpool-details") {
      e.preventDefault();
      setSelectedCar(item.key);
      setModalOpen(true);
    }
  };

  const CardWrapper = ({ children, item }) => {
    // Only apply special gallery handling for carpool cards
    if (item.link && translationPrefix === "carpool-details") {
      return (
        <div
          className="card-link-wrapper carpool-card"
          onClick={(e) => openGalleryModal(e, item)}
          data-oid="951_2tq"
        >
          {children}
        </div>
      );
    }
    // For all other cards with links (services, tourism)
    else if (item.link) {
      return (
        <Link to={item.link} className="card-link" data-oid="bes.4f1">
          {children}
        </Link>
      );
    }
    // For cards without links
    return <>{children}</>;
  };

  return (
    <>
      <div className="services-showcase" dir={dir} data-oid="_kfnjoy">
        {items.map((item, index) => {
          return (
            <CardWrapper
              item={item}
              key={`card-wrapper-${index}`}
              data-oid="u-le-k7"
            >
              <motion.div
                key={`card-${index}`}
                className="service-card"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  type: "spring",
                  stiffness: 100,
                  damping: 12,
                  delay: index * 0.2,
                }}
                whileHover={{
                  scale: 1.03,
                  boxShadow:
                    "0 20px 50px rgba(0, 0, 0, 0.8), 0 0 25px rgba(240, 193, 75, 0.4)",
                }}
                data-oid="fybs2jk"
              >
                {/* Card Header */}
                <div className="service-header" data-oid="ke_u3yz">
                  <motion.div
                    className="service-icon"
                    whileHover={{
                      scale: 1.1,
                      rotate: 5,
                      boxShadow: "0 0 20px rgba(240, 193, 75, 0.8)",
                    }}
                    style={{ background: item.iconBg || iconBg }}
                    data-oid=".93r13r"
                  >
                    <img
                      src={item.icon}
                      alt={t(`${translationPrefix}.${item.key}.title`)}
                      className="service-icon-img"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src =
                          "https://via.placeholder.com/150?text=Image+Not+Found";
                      }}
                      data-oid="8.b83md"
                    />
                  </motion.div>

                  <div className="service-title" data-oid="64h7zw6">
                    <h3 className="service-name" data-oid="k9ao6s5">
                      {t(`${translationPrefix}.${item.key}.title`)}
                    </h3>
                    <p className="service-company" data-oid="ieoa8ij">
                      {t(`${translationPrefix}.${item.key}.subtitle`)}
                    </p>
                    {item.date && (
                      <div className="service-date" data-oid="pnf-ngw">
                        <span data-oid="i0nixn0">
                          {t(`${translationPrefix}.${item.key}.date`)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Card Content */}
                <div className="service-content" data-oid="nuf-9a8">
                  <ul className="service-points" data-oid="ey95z9.">
                    {(() => {
                      const points = t(
                        `${translationPrefix}.${item.key}.points`,
                        { returnObjects: true },
                      );
                      if (Array.isArray(points)) {
                        return points.map((point, pointIndex) => (
                          <motion.li
                            key={`point-${index}-${pointIndex}`}
                            className="service-point"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.1 * pointIndex + 0.3 }}
                            whileHover={{
                              color: "#D4AF37",
                              transition: { duration: 0.2 },
                            }}
                            data-oid="pbhrfef"
                          >
                            {point}
                          </motion.li>
                        ));
                      } else {
                        // If points is not an array, display a default message
                        return (
                          <motion.li
                            key={`point-${index}-default`}
                            className="service-point"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            data-oid="y:374g0"
                          >
                            Premium luxury vehicle with exceptional comfort and
                            style.
                          </motion.li>
                        );
                      }
                    })()}
                  </ul>
                </div>

                {/* Gold Accent Line */}
                <div className="service-accent" data-oid="-.resj4"></div>
                {/* View Details Button (only for items with links) */}
                {item.link && (
                  <div className="service-cta" data-oid="t7d6ez-">
                    <span className="view-details-btn" data-oid="-v5rgaj">
                      {t("common.viewDetails")}{" "}
                      <span className="arrow" data-oid="u82w7qx">
                        →
                      </span>
                    </span>
                  </div>
                )}
              </motion.div>
            </CardWrapper>
          );
        })}
      </div>

      {/* Gallery Modal for Carpool Cards */}
      {translationPrefix === "carpool-details" && selectedCar && (
        <GalleryModal
          isOpen={modalOpen}
          onClose={() => setModalOpen(false)}
          carType={selectedCar}
          data-oid="3jqxbez"
        />
      )}
    </>
  );
};

export default CardShowcase;
