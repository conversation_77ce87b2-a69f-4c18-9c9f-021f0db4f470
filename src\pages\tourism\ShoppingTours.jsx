import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { styles } from "../../styles";
import { fadeIn, textVariant } from "../../utils/motion";
import { StarsCanvas } from "../../components/canvas";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../../contexts/LanguageContext";
import {
  laVallee,
  laVallee1,
  serravalle,
  serravalle1,
  metzingen,
  metzingen1,
  wertheim,
  wertheim1,
  roermond,
  roermond1,
} from "../../assets";

// Define the shopping destinations using translations
const getShoppingDestinations = (t) => [
  {
    name: t(`tourism-pages.shopping-tours.destinations.la-vallee.name`),
    description: t(
      `tourism-pages.shopping-tours.destinations.la-vallee.description`,
    ),
    image: laVallee,
    gallery: [laVallee1],
  },
  {
    name: t(`tourism-pages.shopping-tours.destinations.serravalle.name`),
    description: t(
      `tourism-pages.shopping-tours.destinations.serravalle.description`,
    ),
    image: serravalle,
    gallery: [serravalle1],
  },
  {
    name: t(`tourism-pages.shopping-tours.destinations.metzingen.name`),
    description: t(
      `tourism-pages.shopping-tours.destinations.metzingen.description`,
    ),
    image: metzingen,
    gallery: [metzingen1],
  },
  {
    name: t(`tourism-pages.shopping-tours.destinations.wertheim.name`),
    description: t(
      `tourism-pages.shopping-tours.destinations.wertheim.description`,
    ),
    image: wertheim,
    gallery: [wertheim1],
  },
  {
    name: t(`tourism-pages.shopping-tours.destinations.roermond.name`),
    description: t(
      `tourism-pages.shopping-tours.destinations.roermond.description`,
    ),
    image: roermond,
    gallery: [roermond1],
  },
  {
    name: t(`tourism-pages.shopping-tours.destinations.ingolstadt.name`),
    description: t(
      `tourism-pages.shopping-tours.destinations.ingolstadt.description`,
    ),
    image: laVallee1, // Using another image as placeholder
    gallery: [],
  },
];

const ShoppingDestinationCard = ({ name, description, image, index }) => {
  const { t } = useTranslation();
  const { dir } = useLanguage();

  return (
    <motion.div
      variants={fadeIn("up", "spring", index * 0.5, 0.75)}
      className="bg-black p-5 rounded-2xl sm:w-[360px] w-full border border-[#D4AF37]"
      data-oid="q:.hh4-"
    >
      <div className="relative w-full h-[230px]" data-oid="tmwizu3">
        <img
          src={image}
          alt={name}
          className="w-full h-full object-cover rounded-2xl border border-[#D4AF37]"
          data-oid="j_242_j"
        />
      </div>

      <div
        className="mt-5"
        style={{
          textAlign: dir === "rtl" ? "right" : "left",
          minHeight: "120px",
        }}
        data-oid="9:4pbhu"
      >
        <h3 className="text-[#D4AF37] font-bold text-[24px]" data-oid="37..9ti">
          {name}
        </h3>
        <p className="mt-2 text-white text-[14px]" data-oid="x2s59c6">
          {description}
        </p>
      </div>

      <div className="mt-4 flex justify-center" data-oid="t4_cca.">
        <Link
          to="/contact"
          className="bg-black py-2 px-4 rounded-xl outline-none w-fit text-[#D4AF37] text-[14px] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
          data-oid="s1m4hqv"
        >
          {t("common.bookTour")}
        </Link>
      </div>
    </motion.div>
  );
};

const ShoppingTours = () => {
  const { t } = useTranslation();
  const { dir } = useLanguage();

  // Get the shopping destinations with translations
  const shoppingDestinations = getShoppingDestinations(t);

  return (
    <div className="relative w-full h-auto mx-auto" data-oid="kwkvuv1">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid=":.vj6dw"
      >
        <motion.div variants={textVariant()} data-oid="emxuht2">
          <p
            className={`${styles.sectionSubText} text-center text-[#D4AF37]`}
            data-oid="84zs0g2"
          >
            {t("tourism-pages.shopping-tours.subtitle")}
          </p>
          <h2
            className={`${styles.sectionHeadText} text-center`}
            data-oid="irq:sqe"
          >
            {t("tourism-pages.shopping-tours.title")}
          </h2>
        </motion.div>

        <motion.p
          variants={fadeIn("", "", 0.1, 1)}
          className="mt-4 text-white text-[17px] max-w-3xl leading-[30px] text-center mx-auto"
          style={{ direction: dir }}
          data-oid=":-a-t8l"
        >
          {t("tourism-pages.shopping-tours.description")}
        </motion.p>

        <div
          className="mt-20 flex flex-wrap gap-7 justify-center"
          data-oid="a7jy59j"
        >
          {shoppingDestinations.map((destination, index) => (
            <ShoppingDestinationCard
              key={`shopping-${index}`}
              index={index}
              {...destination}
              data-oid="9wijmcu"
            />
          ))}
        </div>

        <div
          className="mt-10 bg-black rounded-2xl p-8 border border-[#D4AF37]"
          data-oid="0vcfv88"
        >
          <h3
            className="text-[#D4AF37] font-bold text-[24px] text-center"
            data-oid=":inkira"
          >
            {t("tourism-pages.shopping-tours.custom-tours")}
          </h3>
          <p
            className="mt-4 text-white text-[17px] max-w-3xl leading-[30px] text-center mx-auto"
            style={{ direction: dir }}
            data-oid="pgpe3-v"
          >
            {t("tourism-pages.shopping-tours.custom-tours-description")}
          </p>
          <div className="mt-5 flex justify-center" data-oid="2pa-m5j">
            <Link
              to="/contact"
              className="bg-black py-3 px-8 rounded-xl outline-none w-fit text-[#D4AF37] font-bold shadow-md border border-[#D4AF37] hover:shadow-[0_0_10px_#D4AF37] transition-all duration-300"
              data-oid="bu1ykj1"
            >
              {t("common.planCustomTour")}
            </Link>
          </div>
        </div>
      </div>
      <StarsCanvas data-oid="xk:cihd" />
    </div>
  );
};

export default ShoppingTours;
