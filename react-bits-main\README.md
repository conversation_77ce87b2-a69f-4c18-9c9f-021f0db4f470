<div align="center">
	<br>
	<br>
    <picture>
      <source media="(prefers-color-scheme: light)" srcset="src/assets/logos/reactbits-gh-black.svg">
      <source media="(prefers-color-scheme: dark)" srcset="src/assets/logos/reactbits-gh-white.svg">
      <img src="src/assets/logos/reactbits-gh-black.svg" alt="react-bits logo" width="1000">
    </picture>
	<br>
	<br>
</div>

<div align="center">
  The largest & most creative library of animated React components.
</div>

<br />

<div align="center">
  <a href="https://github.com/davidhdev/react-bits/stargazers"><img alt="GitHub Repo stars" src="https://img.shields.io/github/stars/davidhdev/react-bits"></a>
  <a href="https://github.com/davidhdev/react-bits/blob/main/LICENSE.md"><img alt="License" src="https://img.shields.io/badge/License-MIT-cyan.svg"></a>
  
</div>

## Documentation

Go to [reactbits.dev](https://reactbits.dev/) to view the documentation.

## About

React Bits is a large collection of animated React components made to spice up your web creations. We've got animations, components, backgrounds, and awesome stuff that you won't be able to find anywhere else - all free for you to use! These components are all enhanced with customization options as props, to make it easy for you to get exactly what you need.

## Key Features
- 60 total components (text animations, animations, components, backgrounds), growing every day
- All components are lightweight, with minimal dependencies, and highly customizable
- Designed to integrate seamlessly with any modern React project
- Each component comes in 4 variants, to keep everyone happy:
  - JS + CSS
  - JS + Tailwind CSS
  - TS + CSS
  - TS + Tailwind CSS

## CLI (<a href="https://jsrepo.dev"><img src="https://jsrepo.dev/badges/jsrepo.svg" width="50" alt="jsrepo"></a>)
React Bits uses [jsrepo](https://jsrepo.dev) for installing components via CLI. </br>
The setup steps can be found on each component's page in the [documentation](https://reactbits.dev/).

## How To Contribute?

Contributions are welcome! Check the [Open Issues](https://github.com/DavidHDev/react-bits/issues) to see how you can help or submit ideas using the [Feature Request template](https://github.com/DavidHDev/react-bits/issues/new?template=2-feature-request.yml).</br>
Please review the [Contribution Guide](https://github.com/DavidHDev/react-bits/blob/main/CONTRIBUTING.md) and follow our standards. Thanks for your time!

## Contributors

<a href="https://github.com/davidhdev/react-bits/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=davidhdev/react-bits" />
</a>

## Maintainers

[David Haz](https://github.com/DavidHDev)

## Stats
![Alt](https://repobeats.axiom.co/api/embed/b1bf4dc0226458617adbdbf5586f2df953eb0922.svg "Repobeats analytics image")

## License

Licensed under the [MIT license](https://github.com/davidhdev/react-bits/blob/main/LICENSE.md).
