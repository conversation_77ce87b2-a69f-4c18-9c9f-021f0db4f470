import { <PERSON>, Spinner } from "@chakra-ui/react";
import { HeroType, PerspectiveGrid } from "../svg/SvgComponents";

import { useNavigate } from "react-router-dom";
import { useMediaQuery } from "react-haiku";
import { useStars } from "../../hooks/useStars";
import { useActiveBeams } from "../../hooks/useActiveBeams";
import { useScrollVisibility } from "../../hooks/useScrollVisibility";

import AnimatedContent from "../../content/Animations/AnimatedContent/AnimatedContent";
import FadeContent from "../../content/Animations/FadeContent/FadeContent";
import HeroShowcase from "./HeroShowcase/HeroShowcase";

import githubIcon from "../../assets/common/icon-github.svg";
import starIcon from "../../assets/common/icon-star.svg";
import docsIcon from "../../assets/common/icon-docs.svg";

const LandingHero = () => {
  const navigate = useNavigate();
  const isMobile = useMediaQuery("(max-width: 1024px)");

  const stars = useStars();
  const activeBeams = useActiveBeams();
  const isVisible = useScrollVisibility();

  return (
    <div className="hero-content" data-oid="vpz2b78">
      <div className="type-logo" data-oid="9566vi4">
        <AnimatedContent
          initialOpacity={isMobile ? 0 : 1}
          scale={0.8}
          reverse={isMobile}
          data-oid="p_kxgp1"
        >
          <HeroType data-oid="lq.-b0k" />
        </AnimatedContent>
      </div>

      <div className="hero-info" data-oid="1eeau5_">
        <HeroShowcase data-oid=":w2cedx" />
        <div className="headline" data-oid="fh:j4c1">
          <div className="landing-bottom" data-oid="vxud7tp">
            <div className="divider" data-oid="c8sffjj"></div>
            <FadeContent blur duration={1000} data-oid=":yh79e0">
              <p data-oid="6tgk9.6">
                Hand-picked animated components collection for{" "}
                <span data-oid="y-sbpxu">creative developers</span>
              </p>
            </FadeContent>
            <div className="divider" data-oid="2b6opo_"></div>
            <Link
              href="https://github.com/DavidHDev/react-bits"
              target="_blank"
              className="landing-button"
              data-oid="2wnbzs:"
            >
              <img src={githubIcon} alt="GitHub Octocat" data-oid="y00uj:c" />
              Star on GitHub
              <div className="button-divider" data-oid="vkgm.v9"></div>
              <img
                className="star-icon"
                src={starIcon}
                alt="Star Icon"
                data-oid="kodoabz"
              />
              {stars ? (
                <FadeContent blur data-oid="68wce.u">
                  {stars}
                </FadeContent>
              ) : (
                <Spinner boxSize={3} data-oid="5f4s9is" />
              )}
            </Link>
          </div>
          <div
            className="landing-button docs-button"
            onClick={() => navigate("/text-animations/split-text")}
            data-oid="p1o6279"
          >
            <img src={docsIcon} alt="Docs Icon" data-oid="vjiqtb-" /> Read Docs
          </div>
        </div>
      </div>

      <div className="perspective-grid" data-oid="jxdvrgj">
        <PerspectiveGrid activeBeams={activeBeams} data-oid="rb27jyr" />
      </div>

      <div
        className="scroll-indicator"
        style={{ opacity: isVisible ? 1 : 0, transition: "opacity 0.3s ease" }}
        data-oid="h7hc5fc"
      ></div>
    </div>
  );
};

export default LandingHero;
