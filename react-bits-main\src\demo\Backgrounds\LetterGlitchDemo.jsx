import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import {
  Box,
  Button,
  Flex,
  Slider,
  SliderFilledTrack,
  SliderThumb,
  SliderTrack,
  Text,
} from "@chakra-ui/react";
import { randomHex } from "../../utils/utils";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";

import LetterGlitch from "../../content/Backgrounds/LetterGlitch/LetterGlitch";
import { letterGlitch } from "../../constants/code/Backgrounds/letterGlitchCode";
import useForceRerender from "../../hooks/useForceRerender";

const LetterGlitchDemo = () => {
  const [smooth, setSmooth] = useState(true);
  const [speed, setSpeed] = useState(10);
  const [colors, setColors] = useState(["#2b4539", "#61dca3", "#61b3dc"]);
  const [showCenterText, setShowCenterText] = useState(true);
  const [showCenterVignette, setShowCenterVignette] = useState(true);
  const [showOuterVignette, setShowOuterVignette] = useState(false);

  const [key, forceRerender] = useForceRerender();

  const propData = [
    {
      name: "glitchColors",
      type: "string[]",
      default: "['#2b4539', '#61dca3', '#61b3dc']",
      description: "Controls the colors of the letters rendered in the canvas.",
    },
    {
      name: "glitchSpeed",
      type: "number",
      default: "50",
      description:
        "Controls the speed at which letters scramble in the animation.",
    },
    {
      name: "centerVignette",
      type: "boolean",
      default: "false",
      description:
        "When true, renders a radial gradient in the center of the container",
    },
    {
      name: "outerVignette",
      type: "boolean",
      default: "true",
      description:
        "When true, renders an inner radial gradient around the edges of the container.",
    },
    {
      name: "smooth",
      type: "boolean",
      default: "true",
      description:
        "When true, smoothens the animation of the letters for a more subtle feel.",
    },
  ];

  return (
    <TabbedLayout data-oid="sjsuke8">
      <PreviewTab data-oid="3vbugn4">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          overflow="hidden"
          p={0}
          data-oid="96u4_d2"
        >
          <LetterGlitch
            key={key}
            glitchColors={colors}
            glitchSpeed={speed}
            centerVignette={showCenterVignette}
            outerVignette={showOuterVignette}
            smooth={smooth}
            data-oid="rk_vran"
          />

          {showCenterText && (
            <Text
              fontSize="clamp(4rem, 4vw, 8rem)"
              fontWeight={900}
              position="absolute"
              zIndex={0}
              color="#fff"
              mixBlendMode="color-dodge"
              data-oid="p:94wfq"
            >
              react_bits
            </Text>
          )}
        </Box>

        <div className="preview-options" data-oid=".v5jq28">
          <h2 className="demo-title-extra" data-oid="6p794mv">
            Customize
          </h2>
          <Flex wrap="wrap" alignItems="center" gap={4} data-oid="5z29thm">
            <Flex gap={2} wrap="wrap" data-oid="gmpeais">
              <Button
                fontSize="xs"
                h={8}
                onClick={() => {
                  setShowCenterText(!showCenterText);
                }}
                data-oid="uwpxqdu"
              >
                Center Text:{" "}
                <Text
                  color={showCenterText ? "lightgreen" : "coral"}
                  data-oid="0352do6"
                >
                  &nbsp;{String(showCenterText)}
                </Text>
              </Button>
            </Flex>
            <Flex gap={2} wrap="wrap" data-oid=".td_opq">
              <Button
                fontSize="xs"
                h={8}
                onClick={() => {
                  setSmooth(!smooth);
                }}
                data-oid="_-0vi_:"
              >
                Smooth:{" "}
                <Text
                  color={smooth ? "lightgreen" : "coral"}
                  data-oid="pd2g_2q"
                >
                  &nbsp;{String(smooth)}
                </Text>
              </Button>
            </Flex>
            <Flex gap={2} wrap="wrap" data-oid=".ok:1_j">
              <Button
                fontSize="xs"
                h={8}
                onClick={() => {
                  setShowCenterVignette(!showCenterVignette);
                }}
                data-oid="8tpqlp6"
              >
                Inner Vignette:{" "}
                <Text
                  color={showCenterVignette ? "lightgreen" : "coral"}
                  data-oid="jl61pq6"
                >
                  &nbsp;{String(showCenterVignette)}
                </Text>
              </Button>
            </Flex>

            <Flex gap={2} wrap="wrap" data-oid="eq12700">
              <Button
                fontSize="xs"
                h={8}
                onClick={() => {
                  setShowOuterVignette(!showOuterVignette);
                }}
                data-oid="rayfqjc"
              >
                Outer Vignette:{" "}
                <Text
                  color={showOuterVignette ? "lightgreen" : "coral"}
                  data-oid="yr2te8c"
                >
                  &nbsp;{String(showOuterVignette)}
                </Text>
              </Button>
            </Flex>

            <Flex gap={4} align="center" data-oid="43o5hxa">
              <Text fontSize="sm" data-oid="fohg-jx">
                Glitch Speed
              </Text>
              <Slider
                min={0}
                max={100}
                step={5}
                value={speed}
                onChange={(val) => {
                  setSpeed(val);
                }}
                width="100px"
                data-oid="9:xi43g"
              >
                <SliderTrack data-oid="9:4s_dy">
                  <SliderFilledTrack data-oid="vmdoq5t" />
                </SliderTrack>
                <SliderThumb data-oid="8gec:19" />
              </Slider>
              <Text fontSize="sm" data-oid="r:vfb8k">
                {speed}
              </Text>
            </Flex>
          </Flex>

          <Flex gap={2} wrap="wrap" mt={4} data-oid="o0wjn-a">
            <Button
              fontSize="xs"
              h={8}
              onClick={() => {
                setColors([randomHex(), randomHex(), randomHex()]);
                forceRerender();
              }}
              data-oid="m9:t-5_"
            >
              Randomize Colors
            </Button>
          </Flex>
        </div>

        <PropTable data={propData} data-oid="wrrq7x." />
      </PreviewTab>

      <CodeTab data-oid="f1gd52h">
        <CodeExample codeObject={letterGlitch} data-oid="21u8crr" />
      </CodeTab>

      <CliTab data-oid="z1_5c-e">
        <CliInstallation {...letterGlitch} data-oid="j-4vzc2" />
      </CliTab>
    </TabbedLayout>
  );
};

export default LetterGlitchDemo;
