import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import "./i18n";
import { LanguageProvider } from "./contexts/LanguageContext";

const rootElement = document.getElementById("root");
const root = createRoot(rootElement);

root.render(
  <React.StrictMode data-oid="i27:tqw">
    <LanguageProvider data-oid="2hwep:7">
      <App data-oid="fz5-qe:" />
    </LanguageProvider>
  </React.StrictMode>,
);
