/* Mobile Optimizations for Premium Chauffeur Website */

/* ===== Global Mobile Optimizations ===== */

/* Better touch handling */
* {
  -webkit-tap-highlight-color: rgba(212, 175, 55, 0.2); /* Subtle gold highlight on tap */
  touch-action: manipulation; /* Improves touch responsiveness */
}

/* Prevent text size adjustment on orientation change */
html {
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* Improve scrolling on iOS */
body {
  -webkit-overflow-scrolling: touch;
}

/* Optimize buttons and interactive elements for touch */
button, 
a, 
.vehicle-link, 
.cta-button, 
.dropdown-toggle, 
.mobile-dropdown-toggle {
  min-height: 44px; /* Apple's recommended minimum touch target size */
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* ===== Responsive Typography ===== */
@media (max-width: 480px) {
  h1, .hero-title, .sectionHeadText {
    font-size: 2rem !important;
    line-height: 1.2 !important;
  }
  
  h2, .section-title, .cta-title {
    font-size: 1.6rem !important;
    line-height: 1.3 !important;
  }
  
  h3, .vehicle-title, .feature-title {
    font-size: 1.3rem !important;
  }
  
  p, .section-description, .feature-description, .cta-description {
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
  }
  
  .vehicle-link, .cta-button {
    font-size: 0.9rem !important;
    padding: 0.7rem 1.5rem !important;
  }
}

/* ===== Navigation Improvements ===== */
@media (max-width: 768px) {
  /* Larger tap targets for mobile menu */
  .mobile-dropdown-item {
    padding: 12px 16px !important;
    margin: 4px 0 !important;
  }
  
  /* Improved mobile dropdown menu */
  .mobile-dropdown-menu {
    margin-left: 0 !important;
    padding: 8px 0 8px 16px !important;
  }
  
  /* Better mobile menu positioning */
  nav .dropdown-menu {
    width: 90vw;
    max-width: 300px;
    right: 0;
    left: auto;
  }
  
  /* Larger hamburger menu icon */
  .menu-icon {
    width: 32px !important;
    height: 24px !important;
  }
}

/* ===== Card and Grid Layouts ===== */
@media (max-width: 480px) {
  /* Single column layout for small screens */
  .vehicle-grid,
  .tourism-grid,
  .features-grid {
    grid-template-columns: 1fr !important;
    gap: 1.5rem !important;
  }
  
  /* Smaller card padding on mobile */
  .vehicle-card .vehicle-content,
  .tourism-card .tourism-content,
  .feature-card {
    padding: 1.2rem !important;
  }
  
  /* Optimize image heights for mobile */
  .vehicle-image-container,
  .tourism-card-image {
    height: 200px !important;
  }
  
  /* Adjust section spacing */
  .section-header {
    margin-bottom: 1.5rem !important;
  }
  
  .features-section,
  .cta-section {
    margin: 3rem 0 !important;
    padding: 2rem 1.5rem !important;
  }
}

/* ===== Touch-Friendly Interactions ===== */
@media (max-width: 768px) {
  /* Disable hover-only effects on mobile */
  .vehicle-card:hover,
  .tourism-card:hover,
  .feature-card:hover,
  .cta-button:hover {
    transform: none !important;
  }
  
  /* Add active state for touch */
  .vehicle-card:active,
  .tourism-card:active,
  .feature-card:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }
  
  .cta-button:active,
  .vehicle-link:active {
    transform: scale(0.95) !important;
    transition: transform 0.1s ease !important;
    background-color: rgba(212, 175, 55, 0.3) !important;
  }
  
  /* Optimize animations for mobile */
  .icon-circle::after {
    animation-duration: 3s !important; /* Slower animations save battery */
  }
  
  /* Disable certain animations on mobile */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}

/* ===== iOS Specific Fixes ===== */
@supports (-webkit-touch-callout: none) {
  /* iOS-specific styles */
  
  /* Fix for iOS input styling */
  input, textarea, select {
    -webkit-appearance: none;
    border-radius: 0;
  }
  
  /* Fix for iOS momentum scrolling */
  .dropdown-menu,
  .mobile-dropdown-menu {
    -webkit-overflow-scrolling: touch;
  }
  
  /* Fix for iOS fixed positioning issues */
  .fixed-nav {
    position: -webkit-sticky;
    position: sticky;
  }
}

/* ===== Android Specific Fixes ===== */
@supports not (-webkit-touch-callout: none) {
  /* Android-specific styles */
  
  /* Fix for some Android browsers' handling of fixed elements */
  .fixed-element {
    transform: translateZ(0);
    will-change: transform;
  }
}

/* ===== Orientation Specific Styles ===== */
@media screen and (orientation: portrait) {
  /* Portrait-specific styles */
  .hero-section {
    min-height: 90vh !important;
  }
}

@media screen and (orientation: landscape) and (max-height: 500px) {
  /* Landscape on small devices (likely keyboard open) */
  .hero-section {
    min-height: 450px !important;
  }
  
  /* Adjust navbar in landscape */
  nav {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
}

/* ===== Fix for notched phones ===== */
@supports (padding: max(0px)) {
  body {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* ===== Fix for sticky hover on mobile ===== */
@media (hover: hover) {
  /* Only apply hover effects on devices that support hover */
  .vehicle-card:hover,
  .tourism-card:hover,
  .feature-card:hover {
    transform: translateY(-5px) !important;
  }
  
  .cta-button:hover,
  .vehicle-link:hover {
    transform: translateY(-3px) !important;
  }
}
