import React, { Suspense, useRef, useState, useEffect } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
import {
  useGLTF,
  OrbitControls,
  Environment,
  ContactShadows,
  PresentationControls,
  Float
} from "@react-three/drei";
import { motion } from "framer-motion";
import * as THREE from "three";
import "./ModelViewer.css";

// Loading component
const ModelLoader = () => (
  <div className="model-loader">
    <div className="loader-spinner"></div>
    <p>Loading 3D Model...</p>
  </div>
);

// 3D Model Component
const Model = ({ modelPath, scale = 1, position = [0, 0, 0], rotation = [0, 0, 0] }) => {
  const modelRef = useRef();
  const [hovered, setHovered] = useState(false);
  
  // Load the GLTF model
  const { scene } = useGLTF(modelPath);
  
  // Clone the scene to avoid issues with multiple instances
  const clonedScene = scene.clone();
  
  // Optimize materials for better performance
  useEffect(() => {
    clonedScene.traverse((child) => {
      if (child.isMesh) {
        child.castShadow = true;
        child.receiveShadow = true;
        
        if (child.material) {
          // Enhance material quality
          child.material.envMapIntensity = 0.8;
          child.material.needsUpdate = true;
        }
      }
    });
  }, [clonedScene]);

  // Smooth rotation animation
  useFrame((state) => {
    if (modelRef.current && !hovered) {
      modelRef.current.rotation.y += 0.005;
    }
  });

  return (
    <Float
      speed={2}
      rotationIntensity={0.1}
      floatIntensity={0.1}
    >
      <primitive
        ref={modelRef}
        object={clonedScene}
        scale={scale}
        position={position}
        rotation={rotation}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      />
    </Float>
  );
};

// Main ModelViewer Component
const ModelViewer = ({ 
  modelPath, 
  scale = 0.5, 
  position = [0, -1, 0], 
  rotation = [0, 0, 0],
  enableControls = true,
  autoRotate = true,
  environment = "studio",
  className = "",
  height = "400px"
}) => {
  const [error, setError] = useState(false);

  const handleError = (err) => {
    console.error("Model loading error:", err);
    setError(true);
  };

  if (error) {
    return (
      <div className="model-error">
        <div className="error-content">
          <h3>Unable to load 3D model</h3>
          <p>The 3D model could not be loaded. Please check the model path.</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div 
      className={`model-viewer-container ${className}`}
      style={{ height }}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8 }}
    >
      <Canvas
        camera={{ 
          position: [4, 2, 6], 
          fov: 45,
          near: 0.1,
          far: 1000
        }}
        gl={{ 
          antialias: true, 
          alpha: true,
          powerPreference: "high-performance"
        }}
        onError={handleError}
        shadows
      >
        <Suspense fallback={null}>
          {/* Lighting */}
          <ambientLight intensity={0.4} />
          <directionalLight 
            position={[10, 10, 5]} 
            intensity={1}
            castShadow
            shadow-mapSize={[2048, 2048]}
            shadow-camera-far={50}
            shadow-camera-left={-10}
            shadow-camera-right={10}
            shadow-camera-top={10}
            shadow-camera-bottom={-10}
          />
          <pointLight position={[-10, -10, -10]} intensity={0.5} />
          
          {/* Environment */}
          <Environment preset={environment} />
          
          {/* Controls */}
          {enableControls && (
            <PresentationControls
              global
              config={{ mass: 2, tension: 500 }}
              snap={{ mass: 4, tension: 1500 }}
              rotation={[0, 0.3, 0]}
              polar={[-Math.PI / 3, Math.PI / 3]}
              azimuth={[-Math.PI / 1.4, Math.PI / 2]}
            >
              <Model 
                modelPath={modelPath}
                scale={scale}
                position={position}
                rotation={rotation}
              />
            </PresentationControls>
          )}
          
          {!enableControls && (
            <Model 
              modelPath={modelPath}
              scale={scale}
              position={position}
              rotation={rotation}
            />
          )}
          
          {/* Ground shadow */}
          <ContactShadows 
            position={[0, -1.4, 0]} 
            opacity={0.4} 
            scale={10} 
            blur={1.5} 
            far={4.5} 
          />
        </Suspense>
      </Canvas>
      
      {/* Loading overlay */}
      <Suspense fallback={<ModelLoader />}>
        <div />
      </Suspense>
    </motion.div>
  );
};

export default ModelViewer;
