import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";

import {
  Flex,
  Text,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Input,
} from "@chakra-ui/react";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";
import useForceRerender from "../../hooks/useForceRerender";

import PixelTransition from "../../content/Animations/PixelTransition/PixelTransition";
import { pixelTransition } from "../../constants/code/Animations/pixelTransitionCode";

const propData = [
  {
    name: "firstContent",
    type: "ReactNode | string",
    default: "—",
    description: "Content to show by default (e.g., an <img> or text).",
  },
  {
    name: "secondContent",
    type: "ReactNode | string",
    default: "—",
    description: "Content revealed upon hover or click.",
  },
  {
    name: "gridSize",
    type: "number",
    default: "7",
    description: "Number of rows/columns in the pixel grid.",
  },
  {
    name: "pixelColor",
    type: "string",
    default: "currentColor",
    description: "Background color used for each pixel block.",
  },
  {
    name: "animationStepDuration",
    type: "number",
    default: "0.3",
    description: "Length of the pixel reveal/hide in seconds.",
  },
  {
    name: "aspectRatio",
    type: "string",
    default: `"100%"`,
    description: "Sets the 'padding-top' (or aspect-ratio) for the container.",
  },
  {
    name: "className",
    type: "string",
    default: "—",
    description: "Optional additional class names for styling.",
  },
  {
    name: "style",
    type: "object",
    default: "{}",
    description: "Optional inline styles for the container.",
  },
];

const PixelTransitionDemo = () => {
  const [gridSize, setGridSize] = useState(8);
  const [pixelColor, setPixelColor] = useState("#ffffff");
  const [animationStepDuration, setAnimationStepDuration] = useState(0.4);
  const [key, forceRerender] = useForceRerender();

  return (
    <TabbedLayout data-oid="wuj7-dz">
      <PreviewTab data-oid="06dz2ya">
        <Flex
          direction="column"
          position="relative"
          className="demo-container"
          minH={400}
          maxH={400}
          overflow="hidden"
          data-oid="cxg5wa."
        >
          <PixelTransition
            key={key}
            firstContent={
              <img
                src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/3a/Cat03.jpg/1200px-Cat03.jpg"
                alt="Default"
                style={{ width: "100%", height: "100%", objectFit: "cover" }}
                data-oid="gfeg:ws"
              />
            }
            secondContent={
              <div
                style={{
                  width: "100%",
                  height: "100%",
                  display: "grid",
                  placeItems: "center",
                  backgroundColor: "#111",
                }}
                data-oid="md-nauj"
              >
                <p
                  style={{
                    fontWeight: 900,
                    fontSize: "3rem",
                    color: "#ffffff",
                  }}
                  data-oid="axr4tiy"
                >
                  Meow!
                </p>
              </div>
            }
            gridSize={gridSize}
            pixelColor={pixelColor}
            animationStepDuration={animationStepDuration}
            className="custom-pixel-card"
            data-oid="ip7p1am"
          />

          <Text mt={2} color="#a6a6a6" data-oid="aqruv9e">
            Psst, hover the card!
          </Text>
        </Flex>

        <div className="preview-options" data-oid="79j_eri">
          <h2 className="demo-title-extra" data-oid="-tl63q9">
            Customize
          </h2>

          <Flex gap={4} align="center" mt={4} data-oid="t5607p6">
            <Text fontSize="sm" data-oid="pwx8_1e">
              Grid Size:
            </Text>
            <Slider
              min={2}
              max={50}
              step={1}
              value={gridSize}
              onChange={(val) => {
                setGridSize(val);
                forceRerender();
              }}
              width="200px"
              data-oid="w_d7m6m"
            >
              <SliderTrack data-oid="z9mloip">
                <SliderFilledTrack data-oid="jupb_s6" />
              </SliderTrack>
              <SliderThumb data-oid="lnip4ao" />
            </Slider>
            <Text fontSize="sm" data-oid=".m6yq6j">
              {gridSize}
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="._26oel">
            <Text fontSize="sm" data-oid="x4mm_6u">
              Pixel Color:
            </Text>
            <Input
              type="color"
              value={pixelColor}
              onChange={(e) => {
                setPixelColor(e.target.value);
                forceRerender();
              }}
              width="60px"
              p={0}
              data-oid="xi8e494"
            />

            <Text fontSize="sm" data-oid="odm5pz8">
              {pixelColor}
            </Text>
          </Flex>

          <Flex gap={4} align="center" mt={4} data-oid="ukyl8p8">
            <Text fontSize="sm" data-oid="z3x2eno">
              Animation Duration (s):
            </Text>
            <Slider
              min={0.1}
              max={2}
              step={0.1}
              value={animationStepDuration}
              onChange={(val) => {
                setAnimationStepDuration(val);
                forceRerender();
              }}
              width="200px"
              data-oid="jnd--ae"
            >
              <SliderTrack data-oid="8jf202j">
                <SliderFilledTrack data-oid="19r0_z1" />
              </SliderTrack>
              <SliderThumb data-oid="4a2.gdx" />
            </Slider>
            <Text fontSize="sm" data-oid="xp_k050">
              {animationStepDuration.toFixed(1)}
            </Text>
          </Flex>
        </div>

        <PropTable data={propData} data-oid="g3hz8i5" />
        <Dependencies dependencyList={["gsap"]} data-oid=":m12h43" />
      </PreviewTab>

      <CodeTab data-oid="x57ag:c">
        <CodeExample codeObject={pixelTransition} data-oid="j24m3b8" />
      </CodeTab>

      <CliTab data-oid="yjomfir">
        <CliInstallation {...pixelTransition} data-oid=":9hz0fo" />
      </CliTab>
    </TabbedLayout>
  );
};

export default PixelTransitionDemo;
