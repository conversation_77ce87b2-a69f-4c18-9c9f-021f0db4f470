// This file is deprecated and has been replaced by TourismShowcase.jsx
// Keeping it for reference only
import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useLanguage } from "../contexts/LanguageContext";

import { styles } from "../styles";
import { github } from "../assets";
import { SectionWrapper } from "../hoc";
import { projects } from "../constants";
import { fadeIn, textVariant } from "../utils/motion";

const ProjectCard = ({ name, description, tags, image, source_code_link }) => {
  const { dir } = useLanguage();

  return (
    <motion.div
      variants={fadeIn("up", "spring", 0.1, 0.3)}
      whileHover={{
        y: -5,
        transition: { duration: 0.1 },
      }}
      data-oid="p:4-sl-"
    >
      <div
        className="bg-black p-5 rounded-2xl sm:w-[400px] w-full mx-auto hover:shadow-xl transition-all duration-200 border border-[#D4AF37]"
        data-oid="vkeg4_q"
      >
        <div
          className="relative w-full h-[230px] sm:h-[280px] overflow-hidden rounded-2xl"
          data-oid="4og5xeo"
        >
          <img
            src={image}
            alt={name}
            className="w-full h-full object-cover transform scale-110 hover:scale-125 transition-transform duration-700"
            loading="eager" // Force eager loading for important images
            data-oid="se85-gv"
          />

          {source_code_link && (
            <div
              className="absolute inset-0 flex justify-end m-3 card-img_hover"
              data-oid="f3fi_:w"
            >
              <div
                onClick={() => window.open(source_code_link, "_blank")}
                className="black-gradient w-10 h-10 rounded-full flex justify-center items-center cursor-pointer hover:scale-110 transition-transform duration-200"
                data-oid="8gmn8tm"
              >
                <img
                  src={github}
                  alt="source code"
                  className="w-1/2 h-1/2 object-contain"
                  data-oid="nna3pze"
                />
              </div>
            </div>
          )}
        </div>
        <div
          className="mt-5"
          style={{ textAlign: dir === "rtl" ? "right" : "left" }}
          data-oid="6w7o-g2"
        >
          <h3
            className="text-[#D4AF37] font-bold text-[22px] sm:text-[26px]"
            data-oid="66wlwep"
          >
            {name}
          </h3>
          <p
            className="mt-2 text-white text-[14px] sm:text-[16px]"
            data-oid="749b2yh"
          >
            {description}
          </p>
        </div>
        <div
          className="mt-4 flex flex-wrap gap-2"
          style={{ justifyContent: dir === "rtl" ? "flex-end" : "flex-start" }}
          data-oid="4qjwe_k"
        >
          {tags &&
            tags.map((tag) => (
              <p
                key={tag.name}
                className={`text-[14px] sm:text-[16px] ${tag.color} hover:scale-105 transition-transform duration-200`}
                data-oid="-r_9m2q"
              >
                #{tag.name}
              </p>
            ))}
        </div>
      </div>
    </motion.div>
  );
};

const TourismContent = () => {
  const { t } = useTranslation();
  const { dir } = useLanguage();

  return (
    <>
      <motion.div
        variants={textVariant()}
        initial="hidden"
        animate="show"
        data-oid="w.-_-xo"
      >
        <p
          className={`${styles.sectionSubText} text-center text-[#D4AF37]`}
          data-oid="t_ozoj3"
        >
          {t("tourism.subtitle")}
        </p>
        <h2
          className={`${styles.sectionHeadText} text-center`}
          data-oid="0hx762l"
        >
          {t("tourism.title")}
        </h2>
      </motion.div>
      <div className="w-full flex justify-center" data-oid="adzi4w_">
        <motion.p
          variants={fadeIn("", "", 0.05, 0.5)}
          initial="hidden"
          animate="show"
          className="mt-3 text-gray-200 text-[17px] max-w-3xl leading-[30px] text-center"
          style={{ direction: dir }}
          data-oid="lbbfr-v"
        >
          {t("tourism.description")}
        </motion.p>
      </div>
      <div
        className="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 sm:gap-8 justify-items-center"
        data-oid="hdunk2w"
      >
        {projects.map((project, index) => (
          <ProjectCard
            key={`project-${index}`}
            {...project}
            index={index}
            data-oid="cgf2un."
          />
        ))}
      </div>
    </>
  );
};

const Tourism = () => {
  const { t } = useTranslation();
  const { dir } = useLanguage();

  return (
    <div className="relative z-0" data-oid="p.2h0g0">
      <div
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
        data-oid="_.trdct"
      >
        <TourismContent data-oid="mqg2y_q" />

        <div className="mt-10" data-oid="e8lv6ez">
          <h3
            className="text-[#D4AF37] font-bold text-[24px] text-center"
            data-oid="-iqrd-9"
          >
            {t("tourism.exploreCategories")}
          </h3>
          <div
            className="mt-5 grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-5"
            data-oid="0zdssc0"
          >
            <Link
              to="/tourism/beliebte-zielorte"
              className="bg-black p-4 sm:p-5 rounded-xl border border-[#D4AF37] hover:bg-black hover:shadow-[0_0_10px_#D4AF37] transition-all duration-200"
              data-oid="ofkdsuq"
            >
              <h4
                className="text-[#D4AF37] font-semibold text-[18px] sm:text-[20px]"
                style={{ textAlign: dir === "rtl" ? "right" : "left" }}
                data-oid="aup02zi"
              >
                {t("tourism-pages.popular-destinations.title")}
              </h4>
              <p
                className="text-white mt-2 text-[14px] sm:text-[16px]"
                style={{ textAlign: dir === "rtl" ? "right" : "left" }}
                data-oid="_..87e."
              >
                {t("tourism.popularDestinationsDesc")}
              </p>
            </Link>
            <Link
              to="/tourism/shoppingtours"
              className="bg-black p-4 sm:p-5 rounded-xl border border-[#D4AF37] hover:bg-black hover:shadow-[0_0_10px_#D4AF37] transition-all duration-200"
              data-oid="dutsm10"
            >
              <h4
                className="text-[#D4AF37] font-semibold text-[18px] sm:text-[20px]"
                style={{ textAlign: dir === "rtl" ? "right" : "left" }}
                data-oid=":2ybobh"
              >
                {t("tourism-pages.shopping-tours.title")}
              </h4>
              <p
                className="text-white mt-2 text-[14px] sm:text-[16px]"
                style={{ textAlign: dir === "rtl" ? "right" : "left" }}
                data-oid="pv2gcfv"
              >
                {t("tourism.shoppingToursDesc")}
              </p>
            </Link>
            <Link
              to="/tourism/freizeitparks"
              className="bg-black p-4 sm:p-5 rounded-xl border border-[#D4AF37] hover:bg-black hover:shadow-[0_0_10px_#D4AF37] transition-all duration-200"
              data-oid="fi.oehm"
            >
              <h4
                className="text-[#D4AF37] font-semibold text-[18px] sm:text-[20px]"
                style={{ textAlign: dir === "rtl" ? "right" : "left" }}
                data-oid="21h6ngo"
              >
                {t("tourism-pages.theme-parks.title")}
              </h4>
              <p
                className="text-white mt-2 text-[14px] sm:text-[16px]"
                style={{ textAlign: dir === "rtl" ? "right" : "left" }}
                data-oid="r8xo.kd"
              >
                {t("tourism.themeParksDesc")}
              </p>
            </Link>
            <Link
              to="/tourism/bauernhofe"
              className="bg-black p-4 sm:p-5 rounded-xl border border-[#D4AF37] hover:bg-black hover:shadow-[0_0_10px_#D4AF37] transition-all duration-200"
              data-oid="cwss7g."
            >
              <h4
                className="text-[#D4AF37] font-semibold text-[18px] sm:text-[20px]"
                style={{ textAlign: dir === "rtl" ? "right" : "left" }}
                data-oid=":p2pmbq"
              >
                {t("tourism-pages.farms.title")}
              </h4>
              <p
                className="text-white mt-2 text-[14px] sm:text-[16px]"
                style={{ textAlign: dir === "rtl" ? "right" : "left" }}
                data-oid="m1xx3.5"
              >
                {t("tourism.farmExperiencesDesc")}
              </p>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

const WrappedTourism = SectionWrapper(TourismContent, "tourism");

// Export both the wrapped version (for the home page) and the standalone version (for the route)
export { Tourism };
export default WrappedTourism;
