import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import {
  Box,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Select,
  Slider,
  SliderFilledTrack,
  SliderThumb,
  SliderTrack,
  Text,
} from "@chakra-ui/react";

import CodeExample from "../../components/code/CodeExample";
import CliInstallation from "../../components/code/CliInstallation";
import PropTable from "../../components/common/PropTable";
import Dependencies from "../../components/code/Dependencies";

import CircularText from "../../content/TextAnimations/CircularText/CircularText";
import { circularText } from "../../constants/code/TextAnimations/circularTextCode";

const CircularTextDemo = () => {
  const [text, setText] = useState("REACT*BITS*COMPONENTS*");
  const [onHover, setOnHover] = useState("speedUp");
  const [spinDuration, setSpinDuration] = useState(20);

  const propData = [
    {
      name: "text",
      type: "string",
      default: "''",
      description: "The text to display in a circular layout.",
    },
    {
      name: "spinDuration",
      type: "number",
      default: "20",
      description: "The duration (in seconds) for one full rotation.",
    },
    {
      name: "onHover",
      type: "'slowDown' | 'speedUp' | 'pause' | 'goBonkers'",
      default: "undefined",
      description:
        "Specifies the hover behavior variant. Options include 'slowDown', 'speedUp', 'pause', and 'goBonkers'.",
    },
    {
      name: "className",
      type: "string",
      default: "''",
      description: "Optional additional CSS classes to apply to the component.",
    },
  ];

  return (
    <TabbedLayout data-oid="67_mlqb">
      <PreviewTab data-oid=":gin.um">
        <Box
          position="relative"
          className="demo-container"
          h={400}
          overflow="hidden"
          data-oid="nb1_i4q"
        >
          <CircularText
            text={text}
            onHover={onHover}
            spinDuration={spinDuration}
            data-oid="fnhe4dw"
          />
        </Box>

        <div className="preview-options" data-oid="jmdvw2w">
          <h2 className="demo-title-extra" data-oid="qr9cj9d">
            Customize
          </h2>

          <Flex
            alignItems="center"
            gap={4}
            flexWrap="wrap"
            mb={4}
            data-oid="ufsq9ml"
          >
            <FormControl data-oid="t8u::dq">
              <FormLabel fontSize="sm" data-oid="d.r1dg9">
                Text
              </FormLabel>
              <Input
                rounded="xl"
                w={"300px"}
                maxLength={25}
                value={text}
                onChange={(e) => {
                  setText(e.target.value);
                }}
                placeholder="Enter text..."
                data-oid="4v0:8-l"
              />
            </FormControl>
          </Flex>

          <Flex
            alignItems="center"
            gap={4}
            flexWrap="wrap"
            mb={6}
            data-oid="o_0se3i"
          >
            <FormControl data-oid="m5jhxs7">
              <FormLabel fontSize="sm" data-oid="mhlctpi">
                On Hover
              </FormLabel>
              <Select
                defaultValue="one"
                rounded="xl"
                w={"300px"}
                onChange={(e) => {
                  setOnHover(e.target.value);
                }}
                data-oid="qm5kff2"
              >
                <option value="speedUp" data-oid="oif:zol">
                  Speed Up
                </option>
                <option value="slowDown" data-oid="00rgwvc">
                  Slow Down
                </option>
                <option value="pause" data-oid="dpv6ub-">
                  Pause
                </option>
                <option value="goBonkers" data-oid="k0_s7ck">
                  Go Bonkers
                </option>
              </Select>
            </FormControl>
          </Flex>

          <Flex gap={4} align="center" data-oid="_xh66ze">
            <Text fontSize="sm" data-oid="r8tiebe">
              Speed
            </Text>
            <Slider
              min={1}
              max={60}
              step={1}
              value={spinDuration}
              onChange={(val) => {
                setSpinDuration(val);
              }}
              width="200px"
              data-oid="o7kr54i"
            >
              <SliderTrack data-oid="enyk-ie">
                <SliderFilledTrack data-oid="evrk7kc" />
              </SliderTrack>
              <SliderThumb data-oid="f3:609n" />
            </Slider>
            <Text fontSize="sm" data-oid="ojv-qtg">
              {spinDuration}
            </Text>
          </Flex>
        </div>

        <PropTable data={propData} data-oid=":_pwzp3" />
        <Dependencies dependencyList={["framer-motion"]} data-oid="lnf0.ju" />
      </PreviewTab>

      <CodeTab data-oid="4dkg_ws">
        <CodeExample codeObject={circularText} data-oid="c0gv6ji" />
      </CodeTab>

      <CliTab data-oid="s9homp0">
        <CliInstallation {...circularText} data-oid="r0_q:.z" />
      </CliTab>
    </TabbedLayout>
  );
};

export default CircularTextDemo;
