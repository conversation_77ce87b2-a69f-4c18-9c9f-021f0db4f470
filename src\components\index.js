import { ComputersCanvas, StarsCanvas } from "./canvas";

import Hero from "./Hero";
import Navbar from "./Navbar";
import Carpool from "./About"; // Original component (renamed)
import { CarpoolPage } from "./CarpoolShowcase"; // New standalone component
import Tech from "./Tech";
import Services from "./Experience"; // Original component (renamed)
import { Services as ServicesPage } from "./Experience"; // Standalone component
import Tourism from "./TourismShowcase"; // Updated component
import { Tourism as TourismPage } from "./TourismShowcase"; // Standalone component
import Contact from "./Contact";
import Footer from "./Footer";
import RotatingLogo from "./RotatingLogo";
import Slideshow from "./Slideshow";
import CardShowcase from "./CardShowcase";
import ServicesShowcase from "./ServicesShowcase";
import GalleryModal from "./GalleryModal";
import ElegantTimeline from "./ElegantTimeline";

export {
  Hero,
  Navbar,
  Carpool,
  CarpoolPage,
  Tech,
  Services,
  ServicesPage,
  Tourism,
  TourismPage,
  Contact,
  Footer,
  RotatingLogo,
  ComputersCanvas,
  StarsCanvas,
  Slideshow,
  CardShowcase,
  ServicesShowcase,
  GalleryModal,
  ElegantTimeline,
};
