import { useState } from "react";
import { Box, Flex, Text } from "@chakra-ui/react";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";

import CodeExample from "../../components/code/CodeExample";
import PropTable from "../../components/common/PropTable";
import CliInstallation from "../../components/code/CliInstallation";
import Dependencies from "../../components/code/Dependencies";
import PreviewSlider from "../../components/common/PreviewSlider";
import PreviewSwitch from "../../components/common/PreviewSwitch";
import Customize from "../../components/common/Customize";

import TrueFocus from "../../content/TextAnimations/TrueFocus/TrueFocus";
import { trueFocus } from "../../constants/code/TextAnimations/trueFocusCode";

const TrueFocusDemo = () => {
  const [manualMode, setManualMode] = useState(false);
  const [blurAmount, setBlurAmount] = useState(5);
  const [animationDuration, setAnimationDuration] = useState(0.5);
  const [pauseBetweenAnimations, setPauseBetweenAnimations] = useState(1);
  const [borderColor, setBorderColor] = useState("#00d8ff");

  const config = {
    sentence: "True Focus",
    manualMode,
    blurAmount,
    borderColor,
    animationDuration: animationDuration,
    pauseBetweenAnimations,
  };

  const propData = [
    {
      name: "sentence",
      type: "string",
      default: "'True Focus'",
      description: "The text to display with the focus animation.",
    },
    {
      name: "manualMode",
      type: "boolean",
      default: "false",
      description: "Disables automatic animation when set to true.",
    },
    {
      name: "blurAmount",
      type: "number",
      default: "5",
      description: "The amount of blur applied to non-active words.",
    },
    {
      name: "borderColor",
      type: "string",
      default: "'green'",
      description: "The color of the focus borders.",
    },
    {
      name: "glowColor",
      type: "string",
      default: "'rgba(0, 255, 0, 0.6)'",
      description: "The color of the glowing effect on the borders.",
    },
    {
      name: "animationDuration",
      type: "number",
      default: "0.5",
      description: "The duration of the animation for each word.",
    },
    {
      name: "pauseBetweenAnimations",
      type: "number",
      default: "1",
      description:
        "Time to pause between focusing on each word (in auto mode).",
    },
  ];

  return (
    <TabbedLayout data-oid="u2n8ic_">
      <PreviewTab data-oid="9qr_2.g">
        <Box
          position="relative"
          className="demo-container"
          minH={200}
          data-oid="t01tcy:"
        >
          <TrueFocus {...config} data-oid=".z7qj6a" />
        </Box>

        <Customize data-oid="plccvlp">
          <Flex align="center" gap={2} mt={4} data-oid="jv:if1f">
            <Text fontSize="sm" data-oid="94as7nf">
              Border Color
            </Text>
            <input
              type="color"
              value={borderColor}
              onChange={(e) => setBorderColor(e.target.value)}
              style={{
                width: "40px",
                border: "none",
                padding: "0",
                background: "none",
                cursor: "pointer",
              }}
              data-oid="co8l54j"
            />
          </Flex>

          <PreviewSwitch
            title="Hover Mode"
            isChecked={manualMode}
            onChange={(e) => setManualMode(e.target.checked)}
            data-oid="sxifvfg"
          />

          <PreviewSlider
            title="Blur Amount"
            min={0}
            max={15}
            step={0.5}
            value={blurAmount}
            valueUnit="px"
            onChange={setBlurAmount}
            data-oid="ekgj22-"
          />

          <PreviewSlider
            title="Animation Duration"
            min={0.1}
            max={3}
            step={0.1}
            value={animationDuration}
            valueUnit="s"
            isDisabled={!manualMode}
            onChange={setAnimationDuration}
            data-oid="_zel5l:"
          />

          <PreviewSlider
            title="Pause Between Animations"
            min={0}
            max={5}
            step={0.5}
            value={pauseBetweenAnimations}
            valueUnit="s"
            isDisabled={manualMode}
            onChange={setPauseBetweenAnimations}
            data-oid="0jcoqku"
          />
        </Customize>

        <PropTable data={propData} data-oid="_teqacs" />
        <Dependencies dependencyList={["framer-motion"]} data-oid="9ob.oyl" />
      </PreviewTab>

      <CodeTab data-oid="e4kkp..">
        <CodeExample codeObject={trueFocus} data-oid="zr6lcyk" />
      </CodeTab>

      <CliTab data-oid="m3fy:oj">
        <CliInstallation {...trueFocus} data-oid="yx:g_rr" />
      </CliTab>
    </TabbedLayout>
  );
};

export default TrueFocusDemo;
