import { getLanguage } from "../../utils/utils";
import CodeHighlighter from "./CodeHighlighter";
import {
  CodeOptions,
  CSSTab,
  TailwindTab,
  TSCSSTab,
  TSTailwindTab,
} from "./CodeOptions";

const CodeExample = ({ codeObject }) => {
  return (
    <>
      {Object.entries(codeObject).map(([key, codeString]) => {
        if (
          [
            "tailwind",
            "css",
            "tsTailwind",
            "tsCode",
            "cliDefault",
            "cliTailwind",
            "cliTsTailwind",
            "cliTsDefault",
          ].includes(key)
        )
          return null;

        return key === "code" || key === "tsCode" ? (
          <div key={codeString} data-oid="m5:t5xq">
            <h2 className="demo-title" data-oid="9d1-48d">
              {key}
            </h2>
            <CodeOptions key={codeString} data-oid="0j:23x8">
              {/* JavaScript Tailwind Code */}
              <TailwindTab data-oid="rjgm_u_">
                <CodeHighlighter
                  language="jsx"
                  codeString={codeObject.tailwind}
                  data-oid="3jpdeb5"
                />
              </TailwindTab>

              {/* JavaScript Default CSS Code */}
              <CSSTab data-oid="shqspd.">
                <CodeHighlighter
                  language="jsx"
                  codeString={codeObject.code}
                  data-oid="b:ng8d-"
                />

                {codeObject.css && (
                  <>
                    <h2 className="demo-title" data-oid="nuh8x8b">
                      CSS
                    </h2>
                    <CodeHighlighter
                      language="css"
                      codeString={codeObject.css}
                      data-oid="nszw-er"
                    />
                  </>
                )}
              </CSSTab>

              {/* TypeScript Tailwind Code */}
              {codeObject.tsTailwind && (
                <TSTailwindTab data-oid="c42nksu">
                  <CodeHighlighter
                    language="tsx"
                    codeString={codeObject.tsTailwind}
                    data-oid="op4n1x6"
                  />
                </TSTailwindTab>
              )}

              {/* TypeScript Default CSS Code */}
              {codeObject.tsCode && (
                <TSCSSTab data-oid="l9:ad-6">
                  <CodeHighlighter
                    language="tsx"
                    codeString={codeObject.tsCode}
                    data-oid="w7-2l5o"
                  />

                  {codeObject.css && (
                    <>
                      <h2 className="demo-title" data-oid="7:rvjm2">
                        CSS
                      </h2>
                      <CodeHighlighter
                        language="css"
                        codeString={codeObject.css}
                        data-oid="yn2ygrd"
                      />
                    </>
                  )}
                </TSCSSTab>
              )}
            </CodeOptions>
          </div>
        ) : (
          <div key={codeString} data-oid="y0k8kio">
            <h2 className="demo-title" data-oid="27-d34b">
              {key}
            </h2>
            <CodeHighlighter
              language={getLanguage(key)}
              codeString={codeString}
              data-oid="oj69n4c"
            />
          </div>
        );
      })}
    </>
  );
};

export default CodeExample;
