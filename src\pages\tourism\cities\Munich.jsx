import React from "react";
import CityTemplate from "./CityTemplate";
import { muenchen, muenchen1, muenchen2 } from "../../../assets";

const Munich = () => {
  // City images
  const cityImages = [muenchen, muenchen1, muenchen2];

  // Top attractions
  const attractions = [
    {
      name: "Marienplatz",
      description:
        "The central square in Munich's Old Town, featuring the New Town Hall with its famous Glockenspiel.",
    },
    {
      name: "English Garden",
      description:
        "One of the world's largest urban parks, offering beautiful landscapes, beer gardens, and even surfing on the Eisbach wave.",
    },
    {
      name: "BMW World & Museum",
      description:
        "An architectural masterpiece showcasing BMW's history, current models, and future concepts.",
    },
    {
      name: "Nymphenburg Palace",
      description:
        "A magnificent baroque palace with beautiful gardens, once the summer residence of Bavarian rulers.",
    },
    {
      name: "Deutsches Museum",
      description:
        "One of the world's largest science and technology museums with fascinating interactive exhibits.",
    },
  ];

  // Recommended restaurants
  const restaurants = [
    {
      name: "Hofbräuhaus",
      description:
        "Historic beer hall dating back to 1589, serving traditional Bavarian cuisine and world-famous beer.",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      description:
        "Michelin-starred restaurant offering innovative German and international cuisine in a unique setting.",
    },
    {
      name: "Viktualienmarkt Biergarten",
      description:
        "Beer garden in the heart of Munich's daily food market, perfect for sampling local specialties.",
    },
    {
      name: "Augustiner Keller",
      description:
        "One of Munich's oldest beer gardens with excellent traditional food and a great atmosphere.",
    },
  ];

  // Shopping areas
  const shopping = [
    {
      name: "Maximilianstrasse",
      description:
        "Munich's premier luxury shopping street with high-end boutiques and designer stores.",
    },
    {
      name: "Kaufingerstrasse & Neuhauser Strasse",
      description:
        "The main pedestrian shopping area in the city center with department stores and popular brands.",
    },
    {
      name: "Fünf Höfe",
      description:
        "Elegant shopping arcade with upscale boutiques, art galleries, and cafés in a beautiful architectural setting.",
    },
    {
      name: "Olympia Shopping Center",
      description:
        "Large mall near the Olympic Park with over 135 shops and restaurants.",
    },
  ];

  // Accommodation options
  const hotels = [
    {
      name: "Hotel Bayerischer Hof",
      description:
        "Historic 5-star luxury hotel in the heart of Munich with elegant rooms and excellent amenities.",
    },
    {
      name: "Mandarin Oriental Munich",
      description:
        "Sophisticated luxury hotel offering impeccable service and a rooftop pool with panoramic views.",
    },
    {
      name: "Hotel Vier Jahreszeiten Kempinski",
      description:
        "Prestigious hotel on Maximilianstrasse with classic elegance and modern comforts.",
    },
    {
      name: "Louis Hotel",
      description:
        "Stylish boutique hotel overlooking the Viktualienmarkt with contemporary design and a Japanese restaurant.",
    },
  ];

  // Upcoming events
  const events = [
    {
      name: "Oktoberfest",
      date: "September-October",
      description:
        "The world's largest beer festival and folk celebration with traditional music, food, and Bavarian culture.",
    },
    {
      name: "Christkindlmarkt",
      date: "November-December",
      description:
        "Traditional Christmas market at Marienplatz with festive decorations, food, and handcrafted gifts.",
    },
    {
      name: "Tollwood Summer Festival",
      date: "June-July",
      description:
        "Cultural festival in Olympic Park featuring music, performances, art, and international cuisine.",
    },
    {
      name: "Starkbierfest",
      date: "March",
      description:
        "Strong beer festival celebrating Munich's potent brews with traditional music and food.",
    },
  ];

  // Google Maps embed URL
  const mapUrl =
    "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d170129.5014083699!2d11.4041352!3d48.1550534!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x479e75f9a38c5fd9%3A0x10cb84a7db1987d!2sMunich%2C%20Germany!5e0!3m2!1sen!2sus!4v1653060799729!5m2!1sen!2sus";

  return (
    <CityTemplate
      cityKey="munich"
      cityImages={cityImages}
      attractions={attractions}
      restaurants={restaurants}
      shopping={shopping}
      hotels={hotels}
      events={events}
      mapUrl={mapUrl}
      data-oid="1i_lf25"
    />
  );
};

export default Munich;
