import { useState } from "react";
import { Box, Select, Text } from "@chakra-ui/react";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { hyperspeedPresets } from "../../content/Backgrounds/Hyperspeed/HyperSpeedPresets";

import PropTable from "../../components/common/PropTable";
import CodeExample from "../../components/code/CodeExample";
import Dependencies from "../../components/code/Dependencies";
import useForceRerender from "../../hooks/useForceRerender";
import CliInstallation from "../../components/code/CliInstallation";

import Hyperspeed from "../../content/Backgrounds/Hyperspeed/Hyperspeed";
import { hyperspeed } from "../../constants/code/Backgrounds/hyperspeedCode";

const HyperspeedDemo = () => {
  const [activePreset, setActivePreset] = useState("one");
  const [key, forceRerender] = useForceRerender();

  const propData = [
    {
      name: "effectOptions",
      type: "object",
      default: 'See the "code" tab for default values and presets.',
      description:
        "The highly customizable configuration object for the effect, controls things like colors, distortion, line properties, etc.",
    },
  ];

  return (
    <TabbedLayout data-oid="9bxjkro">
      <PreviewTab data-oid="mpcj8py">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          cursor="pointer"
          p={0}
          mb={4}
          data-oid="s.jjv51"
        >
          <Text
            background={"linear-gradient(to bottom, #444, #111)"}
            backgroundClip="text"
            position="absolute"
            fontWeight={900}
            top={6}
            fontSize="4rem"
            data-oid="0-ybg32"
          >
            Click Me
          </Text>
          <Hyperspeed
            key={key}
            effectOptions={hyperspeedPresets[activePreset]}
            data-oid="iue9:c_"
          />
        </Box>

        <h2 className="demo-title-extra" data-oid="4d9dag.">
          Preset
        </h2>
        <Select
          defaultValue="one"
          rounded="xl"
          w={"300px"}
          onChange={(e) => {
            setActivePreset(e.target.value);
            forceRerender();
          }}
          data-oid="8sd023m"
        >
          <option value="one" data-oid="zmtdmoz">
            Cyberpunk
          </option>
          <option value="two" data-oid="tdr89m4">
            Akira
          </option>
          <option value="three" data-oid="9jc4ny0">
            Golden
          </option>
          <option value="four" data-oid="glnsr--">
            Split
          </option>
          <option value="five" data-oid="d_sa55v">
            Highway
          </option>
        </Select>

        <PropTable data={propData} data-oid="7t_p:7e" />
        <Dependencies
          dependencyList={["three", "postprocessing"]}
          data-oid="8a3_h-d"
        />
      </PreviewTab>

      <CodeTab data-oid="en7fqwl">
        <CodeExample codeObject={hyperspeed} data-oid="ivz:wpd" />
      </CodeTab>

      <CliTab data-oid="3.pb_bv">
        <CliInstallation {...hyperspeed} data-oid="9ghuz:8" />
      </CliTab>
    </TabbedLayout>
  );
};

export default HyperspeedDemo;
