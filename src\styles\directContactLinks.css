.direct-contact-links {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 15px;
  width: 100%;
  padding: 5px;
}

.contact-header {
  font-size: 14px;
  font-weight: 600;
  color: #D4AF37;
  margin-bottom: 5px;
  text-align: center;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.contact-link {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 10px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid rgba(212, 175, 55, 0.3);
  background-color: rgba(0, 0, 0, 0.7);
  color: #D4AF37;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.contact-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  border-color: #D4AF37;
  background-color: rgba(10, 10, 10, 0.8);
}

.contact-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(212, 175, 55, 0.1);
  margin-right: 12px;
  transition: all 0.3s ease;
}

.contact-icon {
  font-size: 18px;
  color: #D4AF37;
}

.contact-info {
  display: flex;
  flex-direction: column;
}

.contact-label {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 2px;
  color: rgba(212, 175, 55, 0.8);
}

.contact-value {
  font-size: 14px;
  font-weight: 500;
}

/* Specific styling for each link type */
.whatsapp-link:hover {
  border-color: #25D366;
}

.whatsapp-link:hover .contact-icon,
.whatsapp-link:hover .contact-label,
.whatsapp-link:hover .contact-value {
  color: #25D366;
}

.whatsapp-link:hover .contact-icon-wrapper {
  background-color: rgba(37, 211, 102, 0.1);
}

.email-link:hover {
  border-color: #D44638;
}

.email-link:hover .contact-icon,
.email-link:hover .contact-label,
.email-link:hover .contact-value {
  color: #D44638;
}

.email-link:hover .contact-icon-wrapper {
  background-color: rgba(212, 70, 56, 0.1);
}

.phone-link:hover {
  border-color: #0077B5;
}

.phone-link:hover .contact-icon,
.phone-link:hover .contact-label,
.phone-link:hover .contact-value {
  color: #0077B5;
}

.phone-link:hover .contact-icon-wrapper {
  background-color: rgba(0, 119, 181, 0.1);
}

/* Animation */
.contact-link {
  animation: fadeIn 0.5s ease forwards;
  opacity: 0;
}

.phone-link {
  animation-delay: 0s;
}

.whatsapp-link {
  animation-delay: 0.1s;
}

.email-link {
  animation-delay: 0.2s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* RTL support */
[dir="rtl"] .contact-icon-wrapper {
  margin-right: 0;
  margin-left: 12px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .contact-link {
    padding: 10px;
  }

  .contact-icon-wrapper {
    width: 32px;
    height: 32px;
  }

  .contact-icon {
    font-size: 16px;
  }

  .contact-label {
    font-size: 11px;
  }

  .contact-value {
    font-size: 13px;
  }
}
