import React, { useState, useRef, useEffect } from "react";
import DirectContactLinks from "./DirectContactLinks";

/**
 * FullPageChatBot component
 * A full-page version of the PocketFlowChatBot
 * Shares the same functionality but takes up the entire screen
 */
const FullPageChatBot = ({
  messages,
  inputValue,
  setInputValue,
  isLoading,
  handleSubmit,
  language,
  t,
  aiConnected,
  showQuickReplies,
  handleFeedback,
  clearChat,
  toggleLanguage,
  onClose,
}) => {
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const inputRef = useRef(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Focus input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  return (
    <div
      className="fixed inset-0 z-50 bg-black-100 flex flex-col overflow-hidden"
      style={{
        animation: "fadeIn 0.3s ease-out",
        direction: language === "ar" ? "rtl" : "ltr",
      }}
      data-oid="z-0kyle"
    >
      {/* Chat header */}
      <div
        className="flex items-center justify-between p-4 bg-gradient-to-r from-black-200 to-black-100 border-b border-secondary"
        data-oid="y5diu1z"
      >
        <div className="flex items-center" data-oid="zmx8t.q">
          {/* Luxury car icon with AI connection indicator */}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-6 w-6 mr-2 text-secondary ${aiConnected ? "ai-connected" : ""}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            data-oid="8r7b.pl"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z"
              data-oid="1ta-ll6"
            />

            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0"
              data-oid="xju90jk"
            />
          </svg>
          <h3 className="font-medium text-secondary" data-oid="uwv_l--">
            {t.chatTitle}
          </h3>
        </div>
        <div className="flex space-x-2" data-oid="3ndv7ye">
          {/* Language toggle button */}
          <button
            onClick={toggleLanguage}
            className="p-1 hover:bg-black-200 rounded transition-colors duration-300"
            aria-label="Switch language"
            title="Switch language"
            data-oid="urk0334"
          >
            <span
              className="text-secondary text-sm font-medium"
              data-oid="bvi8-74"
            >
              {t.switchLanguage}
            </span>
          </button>

          {/* Clear chat button */}
          <button
            onClick={clearChat}
            className="p-1 hover:bg-black-200 rounded transition-colors duration-300"
            aria-label="Clear chat"
            title={t.clearChat}
            data-oid="r:wdgwf"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-secondary"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              data-oid="v:1mkmu"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                data-oid="xo8.16r"
              />
            </svg>
          </button>

          {/* Close full-page chat button */}
          <button
            onClick={onClose}
            className="p-1 hover:bg-black-200 rounded transition-colors duration-300"
            aria-label="Close full-page chat"
            title="Close full-page chat"
            data-oid="xon2h3p"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-secondary"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              data-oid="pr:ppra"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
                data-oid="wrjyai3"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Chat messages - reduced bottom padding to make room for input */}
      <div
        className="flex-1 p-4 pb-0 overflow-y-auto bg-black-100"
        ref={messagesContainerRef}
        data-oid="0e5s6f:"
      >
        {messages.map((message, index) => (
          <div
            key={index}
            className={`mb-4 ${
              message.role === "user"
                ? language === "ar"
                  ? "text-left"
                  : "text-right"
                : language === "ar"
                  ? "text-right"
                  : "text-left"
            }`}
            data-oid="ynpotxf"
          >
            <div className="relative" data-oid="xal45gd">
              <div
                className={`inline-block p-3 rounded-lg max-w-[80%] ${
                  message.role === "user"
                    ? language === "ar"
                      ? "bg-tertiary border border-secondary text-white-100 rounded-bl-none"
                      : "bg-tertiary border border-secondary text-white-100 rounded-br-none"
                    : language === "ar"
                      ? "bg-black-200 border border-gold-200 text-white-100 rounded-br-none"
                      : "bg-black-200 border border-gold-200 text-white-100 rounded-bl-none"
                }`}
                style={{
                  animation: "slideIn 0.3s ease-out",
                  animationDelay: `${index * 0.1}s`,
                }}
                data-oid="9izb3no"
              >
                {message.content}
              </div>

              {/* Read receipt for user messages */}
              {message.role === "user" && (
                <div
                  className="text-xs text-gold-200 mt-1 mr-1 text-right"
                  data-oid="sikvgh_"
                >
                  {index < messages.length - 1 &&
                  messages[index + 1].role === "assistant" ? (
                    <span
                      className="flex items-center justify-end"
                      data-oid="xzuisce"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3 w-3 mr-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        data-oid="xi4xsai"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                          data-oid="n7681e3"
                        />
                      </svg>
                      {t.readReceipt}
                    </span>
                  ) : (
                    <span
                      className="flex items-center justify-end"
                      data-oid="eaxb2a7"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3 w-3 mr-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        data-oid="wcp0cm9"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z"
                          data-oid="5i7ati1"
                        />
                      </svg>
                      {t.sentReceipt}
                    </span>
                  )}
                </div>
              )}

              {/* Feedback UI for assistant messages */}
              {message.role === "assistant" && message.showFeedback && (
                <div
                  className="mt-2 flex justify-start space-x-2"
                  data-oid="cly0jl8"
                >
                  <button
                    onClick={() => handleFeedback(true)}
                    className="flex items-center px-2 py-1 bg-black-200 border border-gold-200 text-gold-200 rounded-lg hover:bg-black-300 transition-colors duration-300 text-xs"
                    aria-label="Helpful"
                    title="This was helpful"
                    data-oid="zdyywh."
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      data-oid=".7sxo3g"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"
                        data-oid="76r4i17"
                      />
                    </svg>
                    {t.helpful}
                  </button>
                  <button
                    onClick={() => handleFeedback(false)}
                    className="flex items-center px-2 py-1 bg-black-200 border border-gold-200 text-gold-200 rounded-lg hover:bg-black-300 transition-colors duration-300 text-xs"
                    aria-label="Not helpful"
                    title="This was not helpful"
                    data-oid="qzv17fd"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      data-oid="7ne9jaf"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018a2 2 0 01.485.06l3.76.94m-7 10v5a2 2 0 002 2h.096c.5 0 .905-.405.905-.904 0-.715.211-1.413.608-2.008L17 13V4m-7 10h2"
                        data-oid="cryf..z"
                      />
                    </svg>
                    {t.notHelpful}
                  </button>
                </div>
              )}

              {/* Direct Contact Links */}
              {message.role === "assistant" && message.showDirectContact && (
                <div className="mt-3" data-oid="7x1vkxw">
                  <div
                    className="text-gold-200 text-sm mb-2"
                    data-oid="_hb8gmv"
                  >
                    {language === "en"
                      ? "For direct assistance, you can contact us via:"
                      : "للمساعدة المباشرة، يمكنك التواصل معنا عبر:"}
                  </div>
                  <DirectContactLinks language={language} data-oid="2.y24lz" />
                </div>
              )}
            </div>
          </div>
        ))}

        {/* Typing indicator with improved animation */}
        {isLoading && (
          <div className="text-left mb-4" data-oid="jufrmh8">
            <div
              className="inline-block p-3 rounded-lg max-w-[80%] bg-black-200 border border-gold-200 text-white-100 rounded-bl-none"
              style={{ animation: "fadeIn 0.3s ease-out" }}
              data-oid=":pkk_ov"
            >
              <div className="flex items-center" data-oid="ft7blgq">
                <span className="text-sm text-gold-200 mr-2" data-oid="dl5of62">
                  {t.typingIndicator}
                </span>
                <div className="flex space-x-1" data-oid="9jl3zph">
                  <div
                    className="w-2 h-2 bg-secondary rounded-full animate-typing"
                    data-oid="peu_fny"
                  ></div>
                  <div
                    className="w-2 h-2 bg-secondary rounded-full animate-typing"
                    style={{ animationDelay: "0.2s" }}
                    data-oid="lq7mkqq"
                  ></div>
                  <div
                    className="w-2 h-2 bg-secondary rounded-full animate-typing"
                    style={{ animationDelay: "0.4s" }}
                    data-oid="ny7b3zm"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Invisible element to scroll to */}
        <div ref={messagesEndRef} data-oid="d9o1y3a" />
      </div>

      {/* Chat input - moved up with bottom padding to avoid social media bubble */}
      <div className="pb-20 bg-black-100" data-oid="17r:ajb">
        <form
          onSubmit={handleSubmit}
          className="p-4 border-t border-secondary bg-black-200"
          data-oid="44433a1"
        >
          <div className="flex space-x-2" data-oid="1vnb:7v">
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder={t.inputPlaceholder}
              className="flex-1 p-2 bg-tertiary border border-gold-200 text-white-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-secondary"
              style={{ animation: "borderPulse 3s infinite" }}
              disabled={isLoading}
              data-oid="2451n1j"
            />

            <button
              type="submit"
              className="p-2 bg-black-100 border border-secondary text-secondary rounded-lg hover:bg-black-200 focus:outline-none focus:ring-2 focus:ring-secondary disabled:opacity-50 transition-colors duration-300"
              disabled={!inputValue.trim() || isLoading}
              data-oid="a4f-l3n"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                data-oid="njpf:mp"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                  data-oid="_6uoe_v"
                />
              </svg>
            </button>
          </div>
        </form>

        {/* Quick reply buttons - always shown in full-page mode */}
        {
          <div
            className="px-4 pb-4 bg-black-200"
            style={{ animation: "fadeIn 0.5s ease-out" }}
            data-oid="zgibap:"
          >
            {/* Suggestion buttons */}
            <div
              className="flex flex-wrap gap-2 justify-center"
              data-oid="tc4y17j"
            >
              {/* Original buttons */}
              <button
                onClick={() => {
                  setInputValue(
                    language === "en"
                      ? "I'd like to book a chauffeur service"
                      : "أرغب في حجز خدمة سائق",
                  );
                  handleSubmit({ preventDefault: () => {} });
                }}
                className="px-3 py-2 bg-black-200 border border-secondary text-secondary rounded-lg hover:bg-black-300 transition-colors duration-300 text-sm"
                data-oid="rnn7x0s"
              >
                {t.bookChauffeur}
              </button>
              <button
                onClick={() => {
                  setInputValue(
                    language === "en"
                      ? "I need contact information"
                      : "أحتاج معلومات الاتصال",
                  );
                  handleSubmit({ preventDefault: () => {} });
                }}
                className="px-3 py-2 bg-black-200 border border-secondary text-secondary rounded-lg hover:bg-black-300 transition-colors duration-300 text-sm"
                data-oid="0f84m.g"
              >
                {t.contactUs}
              </button>
              <button
                onClick={() => {
                  setInputValue(
                    language === "en"
                      ? "What vehicles do you offer?"
                      : "ما هي السيارات التي تقدمونها؟",
                  );
                  handleSubmit({ preventDefault: () => {} });
                }}
                className="px-3 py-2 bg-black-200 border border-secondary text-secondary rounded-lg hover:bg-black-300 transition-colors duration-300 text-sm"
                data-oid="ipb9dhr"
              >
                {t.ourVehicles}
              </button>
              <button
                onClick={() => {
                  setInputValue(
                    language === "en"
                      ? "Tell me about your services"
                      : "أخبرني عن خدماتكم",
                  );
                  handleSubmit({ preventDefault: () => {} });
                }}
                className="px-3 py-2 bg-black-200 border border-secondary text-secondary rounded-lg hover:bg-black-300 transition-colors duration-300 text-sm"
                data-oid="4o_kken"
              >
                {t.ourServices}
              </button>
              <button
                onClick={() => {
                  setInputValue(
                    language === "en"
                      ? "What tourism packages do you offer?"
                      : "ما هي باقات السياحة التي تقدمونها؟",
                  );
                  handleSubmit({ preventDefault: () => {} });
                }}
                className="px-3 py-2 bg-black-200 border border-secondary text-secondary rounded-lg hover:bg-black-300 transition-colors duration-300 text-sm"
                data-oid="2nx81._"
              >
                {t.tourPackages}
              </button>

              {/* Additional suggestion buttons */}
              <button
                onClick={() => {
                  setInputValue(
                    language === "en"
                      ? "What are your rates?"
                      : "ما هي أسعاركم؟",
                  );
                  handleSubmit({ preventDefault: () => {} });
                }}
                className="px-3 py-2 bg-black-200 border border-secondary text-secondary rounded-lg hover:bg-black-300 transition-colors duration-300 text-sm"
                data-oid="sxphfr:"
              >
                {language === "en" ? "Pricing" : "الأسعار"}
              </button>
              <button
                onClick={() => {
                  setInputValue(
                    language === "en"
                      ? "Do you offer airport transfers?"
                      : "هل تقدمون خدمة النقل من وإلى المطار؟",
                  );
                  handleSubmit({ preventDefault: () => {} });
                }}
                className="px-3 py-2 bg-black-200 border border-secondary text-secondary rounded-lg hover:bg-black-300 transition-colors duration-300 text-sm"
                data-oid="r0jahuq"
              >
                {language === "en" ? "Airport Transfers" : "النقل من المطار"}
              </button>
              <button
                onClick={() => {
                  setInputValue(
                    language === "en"
                      ? "What areas do you serve?"
                      : "ما هي المناطق التي تخدمونها؟",
                  );
                  handleSubmit({ preventDefault: () => {} });
                }}
                className="px-3 py-2 bg-black-200 border border-secondary text-secondary rounded-lg hover:bg-black-300 transition-colors duration-300 text-sm"
                data-oid="3_11jos"
              >
                {language === "en" ? "Service Areas" : "مناطق الخدمة"}
              </button>
              <button
                onClick={() => {
                  setInputValue(
                    language === "en"
                      ? "How do I make a reservation?"
                      : "كيف يمكنني إجراء حجز؟",
                  );
                  handleSubmit({ preventDefault: () => {} });
                }}
                className="px-3 py-2 bg-black-200 border border-secondary text-secondary rounded-lg hover:bg-black-300 transition-colors duration-300 text-sm"
                data-oid="zlq:je_"
              >
                {language === "en" ? "Reservation Process" : "عملية الحجز"}
              </button>
              <button
                onClick={() => {
                  setInputValue(
                    language === "en"
                      ? "Do you offer VIP services?"
                      : "هل تقدمون خدمات كبار الشخصيات؟",
                  );
                  handleSubmit({ preventDefault: () => {} });
                }}
                className="px-3 py-2 bg-black-200 border border-secondary text-secondary rounded-lg hover:bg-black-300 transition-colors duration-300 text-sm"
                data-oid=":j9vxwp"
              >
                {language === "en" ? "VIP Services" : "خدمات كبار الشخصيات"}
              </button>
              <button
                onClick={() => {
                  setInputValue(
                    language === "en"
                      ? "What payment methods do you accept?"
                      : "ما هي طرق الدفع التي تقبلونها؟",
                  );
                  handleSubmit({ preventDefault: () => {} });
                }}
                className="px-3 py-2 bg-black-200 border border-secondary text-secondary rounded-lg hover:bg-black-300 transition-colors duration-300 text-sm"
                data-oid="-1s.hp5"
              >
                {language === "en" ? "Payment Methods" : "طرق الدفع"}
              </button>
            </div>
          </div>
        }
      </div>
    </div>
  );
};

export default FullPageChatBot;
