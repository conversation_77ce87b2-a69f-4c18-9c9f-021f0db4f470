.hyperspeed-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: -1;
  overflow: hidden;
  background: #000000;
}

.hyperspeed-container canvas {
  display: block;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
}

/* Ensure proper rendering on different devices */
@media (max-width: 768px) {
  .hyperspeed-container {
    position: fixed;
    width: 100vw;
    height: 100vh;
  }
}

/* Fullscreen support */
:fullscreen .hyperspeed-container,
::backdrop .hyperspeed-container {
  position: fixed;
  width: 100vw;
  height: 100vh;
}

/* Performance optimizations */
.hyperspeed-container {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Loading state */
.hyperspeed-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000000;
  z-index: 1;
  opacity: 1;
  transition: opacity 1s ease-out;
  pointer-events: none;
}

.hyperspeed-container.loaded::before {
  opacity: 0;
}

/* Interactive hint */
.hyperspeed-container::after {
  content: 'Click to accelerate';
  position: absolute;
  bottom: 20px;
  right: 20px;
  color: rgba(212, 175, 55, 0.7);
  font-size: 12px;
  font-weight: 300;
  z-index: 2;
  pointer-events: none;
  opacity: 0;
  animation: fadeInHint 3s ease-in-out 2s forwards;
}

@keyframes fadeInHint {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

/* Hide hint on mobile */
@media (max-width: 768px) {
  .hyperspeed-container::after {
    content: 'Tap to accelerate';
  }
}

@media (max-width: 480px) {
  .hyperspeed-container::after {
    display: none;
  }
}
