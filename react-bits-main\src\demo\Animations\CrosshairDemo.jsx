import { useRef, useState } from "react";
import { Box, Button, Flex, Text } from "@chakra-ui/react";
import {
  CliTab,
  CodeTab,
  PreviewTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";

import CodeExample from "../../components/code/CodeExample";
import Dependencies from "../../components/code/Dependencies";
import CliInstallation from "../../components/code/CliInstallation";

import Crosshair from "../../content/Animations/Crosshair/Crosshair";
import { crosshair } from "../../constants/code/Animations/crosshairCode";

const CrosshairDemo = () => {
  const [linkText, setLinkText] = useState("Aim.. aand..");
  const [color, setColor] = useState("#ffffff");
  const [targeted, setTargeted] = useState(true);

  const containerRef = useRef(null);

  return (
    <TabbedLayout data-oid="5fk-ten">
      <PreviewTab data-oid="y93z01f">
        <Box
          ref={containerRef}
          position="relative"
          className="demo-container"
          minH={300}
          overflow="hidden"
          data-oid="sbj6v1."
        >
          <Crosshair
            containerRef={targeted ? null : containerRef}
            color={color}
            data-oid="q:wjwkv"
          />

          <Flex
            direction="column"
            justifyContent="center"
            alignItems="center"
            data-oid="_kd8ji7"
          >
            <Text
              _hover={{ color: "cyan" }}
              transition=".3s ease"
              textAlign="center"
              fontWeight={900}
              fontSize={{ base: "2rem", md: "4rem" }}
              as="a"
              href="https://github.com/DavidHDev/react-bits"
              onMouseEnter={() => setLinkText("Shoot!!!")}
              onMouseLeave={() => setLinkText("Aim.. aand..")}
              data-oid="o89p1md"
            >
              {linkText}
            </Text>
            <Text
              position="relative"
              top="-10px"
              color="#444"
              data-oid="pxf9945"
            >
              (hover me)
            </Text>
          </Flex>
        </Box>

        <div className="preview-options" data-oid="p.y.quf">
          <h2 className="demo-title-extra" data-oid="n7fma_4">
            Customize
          </h2>
          <Flex gap={2} data-oid="n-83olf">
            <Button
              fontSize="xs"
              h={8}
              onClick={() => {
                setTargeted(!targeted);
              }}
              data-oid="nvpcc1."
            >
              Active on:{" "}
              <Text
                color={targeted ? "lightgreen" : "coral"}
                data-oid="0ccrcsg"
              >
                &nbsp;{targeted ? "Viewport" : "Container"}
              </Text>
            </Button>
            <Flex
              alignItems="center"
              fontSize="xs"
              h={8}
              onClick={() => {}}
              data-oid="tp6tq1c"
            >
              Color:&nbsp;&nbsp;
              <input
                type="color"
                value={color}
                style={{ height: "22px", outline: "none", border: "none" }}
                onChange={(e) => setColor(e.target.value)}
                data-oid="pi:gby9"
              />
            </Flex>
          </Flex>
        </div>

        <Dependencies dependencyList={["gsap"]} data-oid="s:vh171" />
      </PreviewTab>

      <CodeTab data-oid="ns77ey6">
        <CodeExample codeObject={crosshair} data-oid="f0cknx5" />
      </CodeTab>

      <CliTab data-oid="suea6p8">
        <CliInstallation {...crosshair} data-oid="_:d4.-1" />
      </CliTab>
    </TabbedLayout>
  );
};

export default CrosshairDemo;
