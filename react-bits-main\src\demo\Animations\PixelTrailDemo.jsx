import { useState } from "react";
import {
  CodeTab,
  PreviewTab,
  CliTab,
  TabbedLayout,
} from "../../components/common/TabbedLayout";
import { Box, Flex, Text, Input } from "@chakra-ui/react";

import RefreshButton from "../../components/common/RefreshButton";
import CodeExample from "../../components/code/CodeExample";
import Dependencies from "../../components/code/Dependencies";
import useForceRerender from "../../hooks/useForceRerender";
import PropTable from "../../components/common/PropTable";
import CliInstallation from "../../components/code/CliInstallation";
import PreviewSlider from "../../components/common/PreviewSlider";
import PreviewSwitch from "../../components/common/PreviewSwitch";
import Customize from "../../components/common/Customize";

import PixelTrail from "../../content/Animations/PixelTrail/PixelTrail";
import { pixelTrail } from "../../constants/code/Animations/pixelTrailCode";

const PixelTrailDemo = () => {
  const [gridSize, setGridSize] = useState(50);
  const [trailSize, setTrailSize] = useState(0.1);
  const [maxAge, setMaxAge] = useState(250);
  const [interpolate, setInterpolate] = useState(5);
  const [color, setColor] = useState("#00d8ff");
  const [gooeyEnabled, setGooeyEnabled] = useState(true);
  const [gooStrength, setGooStrength] = useState(2);

  const [key, forceRerender] = useForceRerender();

  const propData = [
    {
      name: "gridSize",
      type: "number",
      default: "40",
      description: "Number of pixels in grid.",
    },
    {
      name: "trailSize",
      type: "number",
      default: "0.1",
      description: "Size of each trail dot.",
    },
    {
      name: "maxAge",
      type: "number",
      default: "500",
      description: "Duration of the trail effect.",
    },
    {
      name: "interpolate",
      type: "number",
      default: "5",
      description: "Interpolation factor for pointer movement.",
    },
    {
      name: "color",
      type: "string",
      default: "#ffffff",
      description: "Pixel color.",
    },
    {
      name: "gooeyFilter",
      type: "object",
      default: "{ id: 'custom-goo-filter', strength: 5 }",
      description: "Configuration for gooey filter.",
    },
  ];

  return (
    <TabbedLayout data-oid="hqxhpb-">
      <PreviewTab data-oid="xh80w8m">
        <Box
          position="relative"
          className="demo-container"
          h={500}
          p={0}
          overflow="hidden"
          data-oid="nimwgyx"
        >
          <RefreshButton onClick={forceRerender} data-oid="rmjpx70" />
          <PixelTrail
            key={key}
            gridSize={gridSize}
            trailSize={trailSize}
            maxAge={maxAge}
            interpolate={interpolate}
            color={color}
            gooeyFilter={
              gooeyEnabled
                ? { id: "custom-goo-filter", strength: gooStrength }
                : undefined
            }
            data-oid="b:wyrco"
          />

          <Text
            position="absolute"
            zIndex={0}
            fontSize="clamp(2rem, 6vw, 6rem)"
            color="#222"
            fontWeight={900}
            data-oid="47ja3y2"
          >
            Hover Me.
          </Text>
        </Box>

        <Customize data-oid="y1cw1-c">
          <PreviewSlider
            title="Grid Size"
            min={10}
            max={100}
            step={1}
            value={gridSize}
            onChange={(val) => {
              setGridSize(val);
              forceRerender();
            }}
            data-oid="oxner7f"
          />

          <PreviewSlider
            title="Trail Size"
            min={0.05}
            max={0.5}
            step={0.01}
            value={trailSize}
            onChange={(val) => {
              setTrailSize(val);
              forceRerender();
            }}
            data-oid="i:_k0qp"
          />

          <PreviewSlider
            title="Max Age"
            min={100}
            max={1000}
            step={50}
            value={maxAge}
            onChange={(val) => {
              setMaxAge(val);
              forceRerender();
            }}
            data-oid=":tn3fdz"
          />

          <PreviewSlider
            title="Interpolate"
            min={0}
            max={10}
            step={0.1}
            value={interpolate}
            onChange={(val) => {
              setInterpolate(val);
              forceRerender();
            }}
            data-oid="0dw81-9"
          />

          <Flex gap={4} align="center" mt={4} data-oid=".p70j6x">
            <Text fontSize="sm" data-oid="bzizt1g">
              Color
            </Text>
            <Input
              type="color"
              value={color}
              onChange={(e) => {
                setColor(e.target.value);
                forceRerender();
              }}
              width="50px"
              data-oid=".0-qw-z"
            />

            <Text fontSize="sm" data-oid="je954en">
              {color}
            </Text>
          </Flex>

          <PreviewSwitch
            title="Gooey Filter"
            isChecked={gooeyEnabled}
            onChange={(e) => {
              setGooeyEnabled(e.target.checked);
              forceRerender();
            }}
            data-oid="txiw6xd"
          />

          {gooeyEnabled && (
            <PreviewSlider
              title="Gooey Strength"
              min={1}
              max={20}
              step={1}
              value={gooStrength}
              onChange={(val) => {
                setGooStrength(val);
                forceRerender();
              }}
              data-oid="euk6slc"
            />
          )}
        </Customize>

        <PropTable data={propData} data-oid="ftm9hhk" />
        <Dependencies
          dependencyList={["@react-three/fiber", "@react-three/drei", "three"]}
          data-oid="y-qob_s"
        />
      </PreviewTab>

      <CodeTab data-oid="k9tr75x">
        <CodeExample codeObject={pixelTrail} data-oid="ga:yxk:" />
      </CodeTab>

      <CliTab data-oid="ug4_.lo">
        <CliInstallation {...pixelTrail} data-oid="n8tyo7q" />
      </CliTab>
    </TabbedLayout>
  );
};

export default PixelTrailDemo;
